# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development
- `pnpm dev` - Start development server
- `pnpm build` - Build for production  
- `pnpm start` - Start production server
- `pnpm lint` - Run ESLint and Biome linting with auto-fix
- `pnpm lint:fix` - Run linting with fixes
- `pnpm format` - Format code with Biome

### Database Operations
- `pnpm db:generate` - Generate Drizzle schema migrations
- `pnpm db:migrate` - Run database migrations
- `pnpm db:studio` - Open Drizzle studio
- `pnpm db:push` - Push schema changes to database
- `pnpm db:pull` - Pull schema from database
- `pnpm db:check` - Check migration status

### Testing
- `pnpm test` - Run Playwright tests (sets PLAYWRIGHT=True environment variable)

## Architecture Overview

### Core Technology Stack
- **Framework**: Next.js 15 with App Router and React Server Components
- **UI**: Radix UI components with shadcn/ui, styled with Tailwind CSS
- **Database**: PostgreS<PERSON> with Dr<PERSON>zle ORM for schema management
- **Authentication**: Clerk for user management
- **AI Integration**: AI SDK with support for multiple model providers
- **Testing**: Playwright for end-to-end testing
- **Code Quality**: Biome for linting and formatting, ESLint for additional rules

### Project Structure
- **`app/`** - Next.js App Router pages and API routes
  - `(chat)/` - Main chat interface pages
  - `api/` - API endpoints for chat, files, integrations
  - `dashboard/`, `workflows/`, `sidepanel/` - Feature-specific pages
- **`components/`** - React components organized by feature
  - `ui/` - Base UI components (shadcn/ui)
  - `multimodal/` - File upload and attachment handling
  - `sidebar/` - Navigation and history components
- **`lib/`** - Core business logic and utilities
  - `ai/` - AI model configurations, tools, and integrations
  - `db/` - Database schema, migrations, and queries
  - `store/` - Client-side state management with Zustand
- **`artifacts/`** - AI-generated artifact handling (code, images, sheets, text)
- **`hooks/`** - Custom React hooks
- **`optimus-sidepanel/`** - Browser extension for sidepanel functionality

### Key Features Architecture

#### AI Chat System
- Multi-model support with configurable providers
- Real-time streaming responses using AI SDK
- Tool integration system for extensible functionality
- Memory system for conversation context

#### Document Management
- RAG (Retrieval-Augmented Generation) system with vector embeddings
- Document upload, indexing, and semantic search
- Team-based access control with row-level security
- Integration with Google Drive and file storage

#### MCP (Model Context Protocol) Integration
- Support for external tool integration via MCP servers
- SSE and stdio transport types
- Runtime tool configuration through UI
- Examples: Firecrawl, Git, PostgreSQL, filesystem tools

#### Database Schema
- User management with Clerk integration
- Chat and message persistence
- Document storage with metadata and embeddings
- Memory system for AI context
- JIRA integration for task management

### Development Patterns

#### Authentication Flow
- Clerk handles authentication with waitlist support
- User data synchronized to PostgreSQL
- Department-based access control

#### AI Tool System
- Unified tool architecture in `lib/ai/tools/`
- Context-aware tool selection based on workspace and mode
- Built-in tools: knowledge base, memory, JIRA, Google Sheets, n8n workflows

#### State Management
- Zustand stores for client-side state
- SWR for server state management and caching
- Real-time updates through streaming responses

#### File Handling
- Multimodal input supporting text, images, and documents
- Vercel Blob storage for file persistence
- Document processing pipeline with embedding generation

### Environment Configuration
Required environment variables are defined in `.env.example`. Key integrations include:
- Database connection (POSTGRES_URL)
- Authentication (Clerk keys)
- AI model providers (API keys)
- File storage (Vercel Blob)
- External integrations (JIRA, Google APIs)

### Browser Extension
The `optimus-sidepanel/` directory contains a Chrome extension that provides:
- Sidepanel integration with the main application
- Context extraction from web pages
- Floating action buttons for quick access
- Local conversation management