"use client";

import { useModalsStore } from "@/lib/store/modals";
import { format, isToday, isYesterday, subMonths, subWeeks } from "date-fns";
import { motion } from "framer-motion";
import { Search, Trash2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";
import useSWRInfinite from "swr/infinite";

import { LoaderIcon } from "@/components/icons";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import type { Chat } from "@/lib/db/schema";
import { fetcher } from "@/lib/utils";

type GroupedChats = {
  today: Chat[];
  yesterday: Chat[];
  lastWeek: Chat[];
  lastMonth: Chat[];
  older: Chat[];
};

interface ChatHistory {
  chats: Array<Chat>;
  hasMore: boolean;
}

const PAGE_SIZE = 20;

export function HistoryDialog() {
  const router = useRouter();
  const { historyOpen, setHistoryOpen } = useModalsStore();
  const [search, setSearch] = useState("");
  const [deleteId, setDeleteId] = useState<string | null>(null);

  const getChatHistoryPaginationKey = (pageIndex: number, previousPageData: ChatHistory) => {
    if (previousPageData && previousPageData.hasMore === false) {
      return null;
    }

    if (pageIndex === 0) {
      const key = `/api/history?limit=${PAGE_SIZE}`;
      return key;
    }

    // This part is for subsequent pages
    if (!previousPageData || previousPageData.chats.length === 0) {
      return null;
    }

    const lastChatFromPage = previousPageData.chats.at(-1);

    if (!lastChatFromPage) {
      return null;
    }

    const key = `/api/history?ending_before=${lastChatFromPage.id}&limit=${PAGE_SIZE}`;

    return key;
  };

  const {
    data: paginatedChatHistories,
    setSize,
    isValidating,
    isLoading,
    error,
    mutate,
  } = useSWRInfinite<ChatHistory>(getChatHistoryPaginationKey, fetcher, {
    revalidateIfStale: false,
    revalidateOnFocus: false,
    revalidateOnReconnect: false,
    onError: (err, key) => {
      console.error("Error fetching chat history for key:", key, err);
      toast.error("Failed to load chat history. Please try again.");
    },
  });

  const hasReachedEnd = paginatedChatHistories ? paginatedChatHistories.some((page) => page.hasMore === false) : false;
  const hasEmptyChatHistory = paginatedChatHistories ? paginatedChatHistories.every((page) => page.chats.length === 0) : false;

  const handleDelete = async () => {
    if (!deleteId) return;

    const deletePromise = fetch(`/api/chat?id=${deleteId}`, {
      method: "DELETE",
    });

    toast.promise(deletePromise, {
      loading: "Deleting chat...",
      success: () => {
        mutate((chatHistories) => {
          if (chatHistories) {
            return chatHistories.map((chatHistory) => ({
              ...chatHistory,
              chats: chatHistory.chats.filter((chat) => chat.id !== deleteId),
            }));
          }
        });
        setDeleteId(null);
        return "Chat deleted successfully";
      },
      error: "Failed to delete chat",
    });
  };

  const groupChatsByDate = (chats: Chat[]): GroupedChats => {
    const now = new Date();
    const oneWeekAgo = subWeeks(now, 1);
    const oneMonthAgo = subMonths(now, 1);

    return chats.reduce(
      (groups, chat) => {
        const chatDate = new Date(chat.createdAt);

        if (isToday(chatDate)) {
          groups.today.push(chat);
        } else if (isYesterday(chatDate)) {
          groups.yesterday.push(chat);
        } else if (chatDate > oneWeekAgo) {
          groups.lastWeek.push(chat);
        } else if (chatDate > oneMonthAgo) {
          groups.lastMonth.push(chat);
        } else {
          groups.older.push(chat);
        }

        return groups;
      },
      {
        today: [],
        yesterday: [],
        lastWeek: [],
        lastMonth: [],
        older: [],
      } as GroupedChats
    );
  };

  // Filter chats based on search term
  const filterChats = (chats: Chat[]) => {
    if (!search) return chats;
    return chats.filter((chat) => chat.title.toLowerCase().includes(search.toLowerCase()));
  };

  const renderChatGroup = (title: string, chats: Chat[]) => {
    const filteredChats = filterChats(chats);
    if (filteredChats.length === 0) return null;

    return (
      <div
        key={title}
        className="mb-4"
      >
        <h4 className="mb-2 text-sm font-medium text-muted-foreground pl-3">{title}</h4>
        {filteredChats.map((chat) => (
          <div
            key={chat.id}
            className="flex items-center justify-between rounded-lg px-3 py-2 text-sm hover:bg-accent cursor-pointer"
            onClick={() => {
              router.push(`/chat/${chat.id}`);
              setHistoryOpen(false);
            }}
          >
            <div className="flex-1">
              <p className="font-medium">{chat.title}</p>
              <p className="text-xs text-muted-foreground">{format(new Date(chat.createdAt), "MMM d, yyyy h:mm a")}</p>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={(e) => {
                e.stopPropagation();
                setDeleteId(chat.id);
              }}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        ))}
      </div>
    );
  };

  return (
    <>
      <Dialog
        open={historyOpen}
        onOpenChange={setHistoryOpen}
      >
        <DialogContent className="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle>Chat History</DialogTitle>
          </DialogHeader>
          <div className="flex items-center border rounded-md px-3 py-1">
            <Search className="h-4 w-4 shrink-0 opacity-50" />
            <Input
              className="flex w-full border-0! bg-transparent! outline-0! ring-0! p-0 px-2 text-sm placeholder:text-muted-foreground focus-visible:outline-none! disabled:cursor-not-allowed disabled:opacity-50"
              placeholder="Search chats..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
          </div>
          {isLoading ? (
            <div className="flex items-center justify-center h-40">
              <div className="animate-spin mr-2">
                <LoaderIcon />
              </div>
              <p className="text-sm text-muted-foreground">Loading chats...</p>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-40">
              <p className="text-sm text-destructive">Error loading chat history. Please check the console for details or try again later.</p>
            </div>
          ) : hasEmptyChatHistory ? (
            <div className="flex items-center justify-center h-40">
              <p className="text-sm text-muted-foreground">Your chats will appear here once you start chatting!</p>
            </div>
          ) : (
            <ScrollArea className="h-96 pr-4">
              <div className="flex flex-col">
                {paginatedChatHistories &&
                  (() => {
                    const chatsFromHistory = paginatedChatHistories.flatMap((page) => page.chats);
                    const groupedChats = groupChatsByDate(chatsFromHistory);

                    return (
                      <>
                        {renderChatGroup("Today", groupedChats.today)}
                        {renderChatGroup("Yesterday", groupedChats.yesterday)}
                        {renderChatGroup("Last 7 Days", groupedChats.lastWeek)}
                        {renderChatGroup("Last 30 Days", groupedChats.lastMonth)}
                        {renderChatGroup("Older", groupedChats.older)}
                      </>
                    );
                  })()}

                {hasReachedEnd ? (
                  <div className="px-2 text-xs text-muted-foreground w-full flex flex-row justify-center items-center mt-2">
                    You have reached the end of your chat history.
                  </div>
                ) : (
                  <div className="p-2 text-xs text-muted-foreground flex flex-row gap-2 items-center justify-center">
                    <div className="animate-spin">
                      <LoaderIcon />
                    </div>
                    <div>Loading more chats...</div>
                  </div>
                )}
                <motion.div
                  onViewportEnter={() => {
                    if (!isValidating && !hasReachedEnd) {
                      setSize((size) => size + 1);
                    }
                  }}
                />
              </div>
            </ScrollArea>
          )}
        </DialogContent>
      </Dialog>

      <AlertDialog
        open={!!deleteId}
        onOpenChange={(open) => !open && setDeleteId(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete your chat and remove it from our servers.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
