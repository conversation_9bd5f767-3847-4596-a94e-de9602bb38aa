"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON>Header, <PERSON>alogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Edit3, FileText, Plus, X } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

interface EnvVar {
  name: string;
  value: string;
}

export function McpSettingsDialog() {
  const [open, setOpen] = useState(false);
  const [testing, setTesting] = useState(false);
  const [configMode, setConfigMode] = useState<"manual" | "auto">("auto");
  const [jsonConfig, setJsonConfig] = useState("");
  const [name, setName] = useState("");
  const [transportType, setTransportType] = useState<"sse" | "stdio">("sse");
  const [url, setUrl] = useState("");
  const [command, setCommand] = useState("");
  const [args, setArgs] = useState("");
  const [headers, setHeaders] = useState("");
  const [envVars, setEnvVars] = useState<EnvVar[]>([]);

  const resetForm = () => {
    setName("");
    setTransportType("sse");
    setUrl("");
    setCommand("");
    setArgs("");
    setHeaders("");
    setEnvVars([]);
    setJsonConfig("");
    setConfigMode("auto");
  };

  const addEnvVar = () => {
    setEnvVars([...envVars, { name: "", value: "" }]);
  };

  const updateEnvVar = (index: number, field: "name" | "value", value: string) => {
    const updated = [...envVars];
    updated[index][field] = value;
    setEnvVars(updated);
  };

  const removeEnvVar = (index: number) => {
    setEnvVars(envVars.filter((_, i) => i !== index));
  };

  const parseJsonConfig = () => {
    try {
      const parsed = JSON.parse(jsonConfig.trim());

      // Handle MCP servers format: { "mcpServers": { "server-name": { ... } } }
      if (parsed.mcpServers && typeof parsed.mcpServers === "object") {
        const serverNames = Object.keys(parsed.mcpServers);
        if (serverNames.length === 0) {
          throw new Error("No MCP servers found in configuration");
        }

        // Use the first server if multiple are defined
        const serverName = serverNames[0];
        const serverConfig = parsed.mcpServers[serverName];

        if (serverNames.length > 1) {
          toast.info(`Multiple servers found. Using "${serverName}". You can add others separately.`);
        }

        // Extract server configuration
        setName(
          serverName
            .replace(/^mcp-server-/, "")
            .replace(/-/g, " ")
            .replace(/\b\w/g, (l: string) => l.toUpperCase())
        );

        if (serverConfig.command) {
          setTransportType("stdio");
          setCommand(serverConfig.command);
          setArgs(Array.isArray(serverConfig.args) ? serverConfig.args.join(", ") : "");

          // Extract environment variables
          if (serverConfig.env) {
            const envVarsList = Object.entries(serverConfig.env).map(([name, value]) => ({
              name,
              value: value === "YOUR_API_KEY_HERE" ? "" : String(value),
            }));
            setEnvVars(envVarsList);
          }
        } else if (serverConfig.url) {
          setTransportType("sse");
          setUrl(serverConfig.url);
          if (serverConfig.headers) {
            setHeaders(JSON.stringify(serverConfig.headers, null, 2));
          }
        }
      }
      // Handle our current format: { "transport": { ... } }
      else if (parsed.transport) {
        const transport = parsed.transport;

        if (transport.type === "stdio") {
          setTransportType("stdio");
          setCommand(transport.command || "");
          setArgs(Array.isArray(transport.args) ? transport.args.join(", ") : "");
        } else if (transport.type === "sse") {
          setTransportType("sse");
          setUrl(transport.url || "");
          if (transport.headers) {
            setHeaders(JSON.stringify(transport.headers, null, 2));
          }
        }

        // Try to generate a name from the config
        if (transport.command && transport.args) {
          const lastArg = Array.isArray(transport.args) ? transport.args[transport.args.length - 1] : transport.args;
          if (lastArg) {
            setName(
              lastArg
                .replace(/^(@[^\/]+\/)?/, "")
                .replace(/-/g, " ")
                .replace(/\b\w/g, (l: string) => l.toUpperCase())
            );
          }
        } else if (transport.url) {
          const urlObj = new URL(transport.url);
          setName(
            urlObj.hostname
              .replace(/^www\./, "")
              .replace(/\./g, " ")
              .replace(/\b\w/g, (l: string) => l.toUpperCase())
          );
        }
      }
      // Handle direct server config (just the server object without wrapper)
      else if (parsed.command || parsed.url) {
        if (parsed.command) {
          setTransportType("stdio");
          setCommand(parsed.command);
          setArgs(Array.isArray(parsed.args) ? parsed.args.join(", ") : "");

          if (parsed.env) {
            const envVarsList = Object.entries(parsed.env).map(([name, value]) => ({
              name,
              value: value === "YOUR_API_KEY_HERE" ? "" : String(value),
            }));
            setEnvVars(envVarsList);
          }
        } else if (parsed.url) {
          setTransportType("sse");
          setUrl(parsed.url);
          if (parsed.headers) {
            setHeaders(JSON.stringify(parsed.headers, null, 2));
          }
        }
      } else {
        throw new Error("Unrecognized configuration format. Expected 'mcpServers', 'transport', or direct server config.");
      }

      setConfigMode("manual");
      toast.success("Configuration parsed successfully! Review and adjust the fields if needed.");
    } catch (error) {
      toast.error(`Failed to parse configuration: ${error instanceof Error ? error.message : "Invalid JSON"}`);
      console.error("JSON parse error:", error);
    }
  };

  const buildConfig = () => {
    if (transportType === "sse") {
      const config: any = {
        transport: {
          type: "sse",
          url: url,
        },
      };

      // Add headers if provided
      if (headers.trim()) {
        try {
          config.transport.headers = JSON.parse(headers);
        } catch (e) {
          throw new Error("Invalid headers JSON");
        }
      }

      return config;
    } else {
      // stdio transport
      const argsList = args
        .split(",")
        .map((arg) => arg.trim())
        .filter(Boolean);
      return {
        transport: {
          type: "stdio",
          command: command,
          args: argsList,
        },
      };
    }
  };

  const buildCredential = () => {
    // Convert environment variables to a JSON string for storage
    if (envVars.length === 0) return "";

    const envObject = envVars.reduce((acc, env) => {
      if (env.name && env.value) {
        acc[env.name] = env.value;
      }
      return acc;
    }, {} as Record<string, string>);

    return Object.keys(envObject).length > 0 ? JSON.stringify(envObject) : "";
  };

  const handleAdd = async () => {
    try {
      const config = buildConfig();
      const credential = buildCredential();
      const response = await fetch("/api/mcp", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ name, config, credential }),
      });
      if (!response.ok) {
        throw new Error("Failed to save MCP config");
      }
      toast.success("MCP config saved");
      resetForm();
      // mutate();
    } catch (e) {
      toast.error(e instanceof Error ? e.message : "Invalid MCP config");
      console.error(e);
    }
  };

  const handleTest = async () => {
    if (testing) return;

    setTesting(true);
    try {
      const response = await fetch("/api/mcp/test", {
        method: "POST",
      });

      if (!response.ok) {
        throw new Error("Test request failed");
      }

      const result = await response.json();

      if (result.success) {
        toast.success(`✅ MCP "${result.configName}" works! Found ${result.tools.length} tools: ${result.tools.map((t: any) => t.name).join(", ")}`);
      } else {
        toast.error(`❌ MCP test failed: ${result.error}`);
      }

      // Log detailed results to console for debugging
      console.log("[MCP-TEST] Test results:", result);
    } catch (error) {
      console.error("[MCP-TEST] Test error:", error);
      toast.error(`❌ Test failed: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setTesting(false);
    }
  };

  const exampleConfigs = {
    mcpServers: `{
  "mcpServers": {
    "mcp-server-firecrawl": {
      "command": "npx",
      "args": ["-y", "firecrawl-mcp"],
      "env": {
        "FIRECRAWL_API_KEY": "YOUR_API_KEY_HERE"
      }
    }
  }
}`,
    transport: `{
  "transport": {
    "type": "stdio",
    "command": "npx",
    "args": ["-y", "@modelcontextprotocol/server-memory"]
  }
}`,
    sse: `{
  "transport": {
    "type": "sse",
    "url": "https://api.example.com/mcp",
    "headers": {
      "Authorization": "Bearer your-api-key"
    }
  }
}`,
  };

  return (
    <Dialog
      open={open}
      onOpenChange={setOpen}
    >
      <DialogTrigger asChild>
        <Button>
          <Plus /> Add MCP
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>MCP Settings</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col gap-4">
          <Tabs
            value={configMode}
            onValueChange={(value) => setConfigMode(value as "manual" | "auto")}
          >
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger
                value="auto"
                className="flex items-center gap-2"
              >
                <FileText className="h-4 w-4" />
                Auto-detect
              </TabsTrigger>
              <TabsTrigger
                value="manual"
                className="flex items-center gap-2"
              >
                <Edit3 className="h-4 w-4" />
                Manual
              </TabsTrigger>
            </TabsList>

            <TabsContent
              value="auto"
              className="space-y-4"
            >
              <div className="space-y-2">
                <Label htmlFor="json-config">Paste MCP Configuration</Label>
                <Textarea
                  id="json-config"
                  placeholder="Paste your MCP configuration JSON here..."
                  value={jsonConfig}
                  onChange={(e) => setJsonConfig(e.target.value)}
                  className="min-h-[200px] font-mono text-sm"
                />
                <div className="flex gap-2">
                  <Button
                    onClick={parseJsonConfig}
                    disabled={!jsonConfig.trim()}
                    variant="outline"
                    size="sm"
                  >
                    Parse Configuration
                  </Button>
                  <Button
                    onClick={() => setConfigMode("manual")}
                    variant="ghost"
                    size="sm"
                  >
                    Switch to Manual
                  </Button>
                </div>
              </div>

              <div className="space-y-3">
                <Label className="text-sm font-medium">Example Formats:</Label>
                <div className="space-y-2">
                  <details className="group">
                    <summary className="cursor-pointer text-sm text-muted-foreground hover:text-foreground">MCP Servers Format (recommended)</summary>
                    <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-x-auto">{exampleConfigs.mcpServers}</pre>
                  </details>
                  <details className="group">
                    <summary className="cursor-pointer text-sm text-muted-foreground hover:text-foreground">Transport Format</summary>
                    <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-x-auto">{exampleConfigs.transport}</pre>
                  </details>
                  <details className="group">
                    <summary className="cursor-pointer text-sm text-muted-foreground hover:text-foreground">SSE Transport Format</summary>
                    <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-x-auto">{exampleConfigs.sse}</pre>
                  </details>
                </div>
              </div>
            </TabsContent>

            <TabsContent
              value="manual"
              className="space-y-4"
            >
              <div className="space-y-2">
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  placeholder="Config name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="transport">Transport Type</Label>
                <Select
                  value={transportType}
                  onValueChange={(value: "sse" | "stdio") => setTransportType(value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="sse">SSE (Server-Sent Events)</SelectItem>
                    <SelectItem value="stdio">Stdio (Local Command)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {transportType === "sse" ? (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="url">Server URL</Label>
                    <Input
                      id="url"
                      placeholder="https://your-mcp-server.com/sse"
                      value={url}
                      onChange={(e) => setUrl(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="headers">Headers (JSON)</Label>
                    <Textarea
                      id="headers"
                      placeholder='{"Authorization": "Bearer your-api-key"}'
                      value={headers}
                      onChange={(e) => setHeaders(e.target.value)}
                    />
                  </div>
                </>
              ) : (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="command">Command</Label>
                    <Input
                      id="command"
                      placeholder="npx"
                      value={command}
                      onChange={(e) => setCommand(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="args">Arguments (comma-separated)</Label>
                    <Input
                      id="args"
                      placeholder="-y, firecrawl-mcp"
                      value={args}
                      onChange={(e) => setArgs(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label>Environment Variables</Label>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={addEnvVar}
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        Add Variable
                      </Button>
                    </div>
                    {envVars.length === 0 ? (
                      <div className="text-sm text-muted-foreground italic">No environment variables configured</div>
                    ) : (
                      <div className="space-y-2">
                        {envVars.map((envVar, index) => (
                          <div
                            key={index}
                            className="flex gap-2 items-center"
                          >
                            <Input
                              placeholder="Variable name (e.g., API_KEY)"
                              value={envVar.name}
                              onChange={(e) => updateEnvVar(index, "name", e.target.value)}
                              className="flex-1"
                            />
                            <Input
                              placeholder="Variable value"
                              type="password"
                              value={envVar.value}
                              onChange={(e) => updateEnvVar(index, "value", e.target.value)}
                              className="flex-1"
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              onClick={() => removeEnvVar(index)}
                              className="flex-shrink-0"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </>
              )}
            </TabsContent>
          </Tabs>

          <DialogFooter className="pt-2">
            <Button
              onClick={handleAdd}
              disabled={!name || (transportType === "sse" ? !url : !command)}
            >
              Save
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                // setAdding(false);
                setOpen(false);
                resetForm();
              }}
            >
              Cancel
            </Button>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
}
