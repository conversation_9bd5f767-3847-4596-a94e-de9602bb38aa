import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { ChevronsUpDown, <PERSON>, Loader2 } from "lucide-react";
import { Button } from "./ui/button";

function formatJsonForDisplay(data: any): string {
  const currentData = data;

  // Recursively parse string values until no more JSON strings are found
  const R = (current: any): any => {
    if (typeof current === "string") {
      try {
        const parsed = JSON.parse(current);
        // If parsing succeeds, recurse on the parsed data
        return R(parsed);
      } catch {
        // If it's not a valid JSON string, return it as is
        return current;
      }
    }

    if (Array.isArray(current)) {
      return current.map(R);
    }

    if (typeof current === "object" && current !== null) {
      return Object.fromEntries(Object.entries(current).map(([key, value]) => [key, R(value)]));
    }

    return current;
  };

  try {
    const fullyParsed = R(currentData);
    return JSON.stringify(fullyParsed, null, 2);
  } catch {
    // Fallback for any unexpected error
    return JSON.stringify(currentData, null, 2);
  }
}

export function McpToolCall({ toolInvocation }: { toolInvocation: any }) {
  const { toolName, args, result, state } = toolInvocation;

  if (state === "call") {
    return (
      <div className="mb-4 flex items-center gap-2 rounded-lg border bg-muted/50 px-4 py-2.5 text-muted-foreground">
        <Loader2 className="h-4 w-4 animate-spin" />
        <span>Calling tool: </span>
        <span className="font-semibold text-foreground">{toolName}</span>
        <span>...</span>
      </div>
    );
  }

  if (state === "result") {
    const formattedArgs = formatJsonForDisplay(args);
    const formattedResult = formatJsonForDisplay(result);

    return (
      <Collapsible
        defaultOpen={false}
        className="rounded-xl border bg-muted/50 w-full mb-2.5"
      >
        <div className="flex items-center justify-between px-4 py-2">
          <div className="flex items-center gap-2">
            <Hammer className="h-4 w-4" />
            <span className="font-semibold">{toolName}</span>
          </div>
          <CollapsibleTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="w-9 p-0"
            >
              <ChevronsUpDown className="h-4 w-4" />
              <span className="sr-only">Toggle</span>
            </Button>
          </CollapsibleTrigger>
        </div>
        <CollapsibleContent className="">
          <div className="space-y-4 p-4 pt-0">
            <div>
              <h4 className="mb-2 font-semibold text-muted-foreground">Request</h4>
              <pre className="text-sm w-full overflow-x-auto dark:bg-zinc-900 bg-zinc-50 p-4 border border-zinc-200 dark:border-zinc-700 rounded-lg dark:text-zinc-50 text-zinc-900 max-h-64 overflow-y-auto font-mono leading-relaxed">
                <code className="whitespace-pre-wrap break-words">{formattedArgs}</code>
              </pre>
            </div>
            <div>
              <h4 className="mb-2 font-semibold text-muted-foreground">Response</h4>
              <pre className="text-sm w-full overflow-x-auto dark:bg-zinc-900 bg-zinc-50 p-4 border border-zinc-200 dark:border-zinc-700 rounded-lg dark:text-zinc-50 text-zinc-900 max-h-64 overflow-y-auto font-mono leading-relaxed">
                <code className="whitespace-pre-wrap break-words">{formattedResult}</code>
              </pre>
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>
    );
  }

  return null;
}
