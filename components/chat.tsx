"use client";

import { <PERSON><PERSON><PERSON>eader } from "@/components/chat-header";
import { JiraTasksDialog } from "@/components/jira-tasks-dialog";
import type { ExtendedAttachment } from "@/components/multimodal/types";
import { useArtifactSelector } from "@/hooks/use-artifact";
import { useChatVisibility } from "@/hooks/use-chat-visibility";
import { MODES } from "@/lib/constants";
import { useChatSettingsStore } from "@/lib/store/chat-settings-store";
import { generateUUID } from "@/lib/utils";
import { useChat, type UseChatHelpers } from "@ai-sdk/react";
import type { Attachment, ChatRequest, UIMessage } from "ai";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useSWRConfig } from "swr";
import { unstable_serialize } from "swr/infinite";
import { Artifact } from "./artifact";
import { Messages } from "./messages";
import { MultimodalInput } from "./multimodal/multimodal-input";
import { getChatHistoryPaginationKey } from "./sidebar/sidebar-history";
import { toast } from "./toast";
import type { VisibilityType } from "./visibility-selector";

export function Chat({
  id,
  initialMessages,
  initialChatModel,
  initialVisibilityType,
  isReadonly,
  // session,
  autoResume,
}: {
  id: string;
  initialMessages: Array<UIMessage>;
  initialChatModel: string;
  initialVisibilityType: VisibilityType;
  isReadonly: boolean;
  // session: Session;
  autoResume: boolean;
}) {
  const { mutate } = useSWRConfig();
  const { visibilityType } = useChatVisibility({
    chatId: id,
    initialVisibilityType,
  });
  // const { mentionedItems } = useMention();

  const {
    messages,
    setMessages,
    handleSubmit: handleSubmitAI,
    input,
    setInput,
    append,
    status,
    stop,
    reload,
    experimental_resume,
  } = useChat({
    id,
    maxSteps: 10,
    initialMessages,
    experimental_throttle: 25, // Reduced for faster streaming
    sendExtraMessageFields: true,
    generateId: generateUUID,
    experimental_prepareRequestBody: (request: ChatRequest) => {
      const { forceDeepResearch, selectedOptions, mode } = useChatSettingsStore.getState();
      const lastMessage = request.messages.at(-1) as (UIMessage & { experimental_attachments?: Attachment[] }) | undefined;

      const attachmentsFromBody = lastMessage?.experimental_attachments;

      return {
        id,
        message: lastMessage as any, // Keep original message structure for backend
        selectedChatModel: initialChatModel,
        selectedVisibilityType: visibilityType,
        data: {
          forceDeepResearch,
          selectedOptions,
          mode: mode || MODES.DEFAULT,
          // mentionedItems,
        },
        ...(attachmentsFromBody &&
          attachmentsFromBody.length > 0 && {
            experimental_attachments: attachmentsFromBody,
          }),
      };
    },
    onFinish: () => {
      mutate(unstable_serialize(getChatHistoryPaginationKey));
      setAttachments([]);
    },
    onError: (error) => {
      console.error("Error in onError:", error);
      toast({
        type: "error",
        description: error.message,
      });

      setMessages((prevMessages) => [
        ...prevMessages,
        {
          id: generateUUID(),
          role: "assistant",
          content: `An error occurred: ${error.message}. Please try again or provide more details.`,
          createdAt: new Date(),
        },
      ]);
    },
  });

  // Wrapper function to handle both modes
  const handleSubmit = (event?: { preventDefault?: () => void }, chatRequestOptions?: Parameters<UseChatHelpers["handleSubmit"]>[1]) => {
    if (event?.preventDefault) event.preventDefault();
    handleSubmitAI(event, chatRequestOptions);
  };

  useEffect(() => {
    if (autoResume) {
      experimental_resume();
    }

    // note: this hook has no dependencies since it only needs to run once
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const searchParams = useSearchParams();
  const query = searchParams?.get("query");

  const [hasAppendedQuery, setHasAppendedQuery] = useState(false);

  useEffect(() => {
    if (query && !hasAppendedQuery) {
      append({
        role: "user",
        content: query,
      });

      setHasAppendedQuery(true);
      window.history.replaceState({}, "", `/chat/${id}`);
    }
  }, [query, append, hasAppendedQuery, id]);

  // const { data: votes } = useSWR<Array<Vote>>(messages.length >= 2 ? `/api/vote?chatId=${id}` : null, fetcher);

  const [attachments, setAttachments] = useState<Array<ExtendedAttachment>>([]);
  const isArtifactVisible = useArtifactSelector((state) => state.isVisible);
  return (
    <>
      <div className="flex flex-col min-w-0 h-dvh">
        <ChatHeader
          chatId={id}
          selectedModelId={initialChatModel}
          selectedVisibilityType={initialVisibilityType}
          isReadonly={isReadonly}
          // session={session}
        />

        {!!messages.length && (
          <Messages
            chatId={id}
            status={status}
            votes={[]}
            messages={messages}
            setMessages={setMessages}
            reload={reload}
            isReadonly={isReadonly}
            isArtifactVisible={isArtifactVisible}
          />
        )}

        {!isReadonly && (
          <MultimodalInput
            chatId={id}
            input={input}
            setInput={setInput}
            handleSubmit={handleSubmit}
            status={status}
            stop={stop}
            attachments={attachments}
            setAttachments={setAttachments}
            messages={messages}
            setMessages={setMessages}
          />
        )}
      </div>

      <Artifact
        chatId={id}
        input={input}
        setInput={setInput}
        handleSubmit={handleSubmit}
        status={status}
        stop={stop}
        attachments={attachments}
        setAttachments={setAttachments}
        append={append}
        messages={messages}
        setMessages={setMessages}
        reload={reload}
        votes={[]}
        isReadonly={isReadonly}
        selectedVisibilityType={visibilityType}
      />

      <JiraTasksDialog
        attachments={attachments}
        setAttachments={setAttachments}
      />
    </>
  );
}
