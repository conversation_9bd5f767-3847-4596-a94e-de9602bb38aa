"use client";

import { KnowledgeBaseCollapse } from "@/components/knowledge-base";
import type { Vote } from "@/lib/db/schema";
import { cn, sanitizeText } from "@/lib/utils";
import type { UseChatHelpers } from "@ai-sdk/react";
import type { UIMessage } from "ai";
import cx from "classnames";
import equal from "fast-deep-equal";
import { AnimatePresence, motion } from "framer-motion";
import { memo, useEffect, useState } from "react";
import { DocumentToolCall, DocumentToolResult } from "./document";
import { DocumentBadge, DocumentBadgeLoading } from "./document-badge";
import { DocumentPreview } from "./document-preview";
import { SparklesIcon } from "./icons";
import { Markdown } from "./markdown";
import { McpToolCall } from "./mcp-tool-call";
import { MessageActions } from "./message-actions";
import { MessageEditor } from "./message-editor";
import { MessageReasoning } from "./message-reasoning";
import type { ExtendedAttachment } from "./multimodal/types";
import { N8nWorkflowLoading, N8nWorkflowResults } from "./n8n-workflow";
import { PreviewAttachment } from "./preview-attachment";
import { Weather } from "./weather";
import { BUILT_IN_TOOLS, SKELETON_LOADING_TOOLS, TOOL_IDS } from "@/lib/constants";

interface SourceInfo {
  sourceType: string;
  id: string;
  url: string;
  // Add other fields if present in the actual source object, e.g., title, name
}

const PerplexityAssistantMessage: React.FC<{ message: UIMessage }> = ({ message }) => {
  const [thinkingContent, setThinkingContent] = useState<string | null>(null);
  const [mainContent, setMainContent] = useState<string | null>(null);
  const [extractedSources, setExtractedSources] = useState<SourceInfo[]>([]);
  const [showThinking, setShowThinking] = useState<boolean>(false);

  useEffect(() => {
    let tempThinkingContent: string | null = null;
    let tempMainContent: string | "" = "";
    const sources: SourceInfo[] = [];

    message.parts?.forEach((part) => {
      if (part.type === "source" && part.source) {
        // As per mock data structure
        sources.push(part.source as SourceInfo);
      } else if (part.type === "text") {
        const thinkMatch = part.text.match(/<think>([\s\S]*?)<\/think>/);
        if (thinkMatch?.[1]) {
          tempThinkingContent = thinkMatch[1].trim();
          tempMainContent += part.text.replace(thinkMatch[0], "").trim();
        } else {
          tempMainContent += part.text.trim();
        }
      }
    });

    setExtractedSources(sources);
    setThinkingContent(tempThinkingContent);
    setMainContent(tempMainContent.length > 0 ? tempMainContent : null);
  }, [message.parts]);

  const renderContentWithReferences = (text: string | null, sourcesList: SourceInfo[]): string => {
    if (!text) return "";

    // Replace [N] with Markdown links using a custom 'source-ref:' scheme
    // The link destination will be the 1-based index of the source
    return text.replace(/\[(\d+)\]/g, (match, p1) => {
      const refNum = Number.parseInt(p1, 10);
      // Check if the source actually exists to avoid creating dead links
      if (refNum > 0 && refNum <= sourcesList.length) {
        return `[${refNum}](source-ref:${refNum})`;
      }
      return match; // Return the original match if source index is invalid
    });
  };

  return (
    <div className="flex flex-col gap-2 w-full">
      {thinkingContent && (
        <details className="bg-muted p-3 rounded-md">
          <summary
            className="cursor-pointer text-sm font-medium hover:text-primary list-none flex items-center gap-1"
            onClick={(e) => {
              e.preventDefault();
              setShowThinking(!showThinking);
            }}
          >
            {showThinking ? "Hide Thoughts" : "Show Thoughts"}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className={cn("transition-transform", showThinking && "rotate-180")}
            >
              <polyline points="6 9 12 15 18 9" />
            </svg>
          </summary>
          {showThinking && <pre className="mt-2 whitespace-pre-wrap text-sm text-muted-foreground bg-background p-2 rounded">{thinkingContent}</pre>}
        </details>
      )}
      {mainContent && (
        <div className="prose prose-sm dark:prose-invert max-w-none">
          <div
            data-testid="message-content"
            className={cn("flex flex-col gap-4 max-w-full", {
              "bg-primary text-primary-foreground px-3 py-2 rounded-xl ": message.role === "user",
            })}
          >
            <Markdown sources={extractedSources}>{renderContentWithReferences(mainContent, extractedSources)}</Markdown>
          </div>
        </div>
      )}
      {extractedSources.length > 0 && (
        <div className="mt-4 border-t pt-2">
          <h4 className="font-semibold mb-1 text-muted-foreground">References</h4>
          <ol className="list-decimal list-inside text-sm space-y-1">
            {extractedSources.map((source, index) => (
              <li key={source.id || index}>
                <a
                  href={source.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary hover:underline break-all"
                >
                  {source.url} {/* Ideally, use source.title or a snippet if available */}
                </a>
              </li>
            ))}
          </ol>
        </div>
      )}
    </div>
  );
};

const PurePreviewMessage = ({
  // chatId,
  message,
  // vote,
  isLoading,
  setMessages,
  reload,
  isReadonly,
  requiresScrollPadding,
}: {
  chatId: string;
  message: UIMessage;
  vote: Vote | undefined;
  isLoading: boolean;
  setMessages: UseChatHelpers["setMessages"];
  reload: UseChatHelpers["reload"];
  isReadonly: boolean;
  requiresScrollPadding: boolean;
}) => {
  const [mode, setMode] = useState<"view" | "edit">("view");

  return (
    <AnimatePresence>
      <motion.div
        data-testid={`message-${message.role}`}
        className="w-full mx-auto max-w-3xl px-4 group/message"
        initial={{ y: 5, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        data-role={message.role}
      >
        <div
          className={cn("flex gap-4 w-full group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl", {
            "w-full": mode === "edit",
            "group-data-[role=user]/message:w-fit": mode !== "edit",
          })}
        >
          {message.role === "assistant" && (
            <div className="size-8 flex items-center rounded-full justify-center ring-1 shrink-0 ring-border bg-background">
              <div className="translate-y-px">
                <SparklesIcon size={14} />
              </div>
            </div>
          )}

          <div
            className={cn("flex flex-col gap-2 w-full overflow-x-hidden", {
              "min-h-96": message.role === "assistant" && requiresScrollPadding,
            })}
          >
            {message.experimental_attachments && message.experimental_attachments.length > 0 && (
              <div
                data-testid={`message-attachments`}
                className="flex flex-row justify-end gap-2"
              >
                {message.experimental_attachments.map((attachment) => (
                  <PreviewAttachment
                    key={attachment.url}
                    attachment={attachment as ExtendedAttachment}
                  />
                ))}
              </div>
            )}

            {(() => {
              // Determine if the message is Perplexity-style (assistant message with <think> or type:"source" parts)
              const isMessagePerplexityStyle =
                message.role === "assistant" &&
                (message.parts.some((p) => p.type === "source") || message.parts.some((p) => p.type === "text" && p.text.includes("<think>")));

              if (isMessagePerplexityStyle) {
                return <PerplexityAssistantMessage message={message} />;
              }

              // Original rendering logic for non-Perplexity messages or user messages
              return message.parts?.map((part, index) => {
                const { type } = part;

                const key = `message-${message.id}-part-${index}`;

                if (type === "reasoning") {
                  return (
                    <MessageReasoning
                      key={key}
                      isLoading={isLoading}
                      reasoning={part.reasoning}
                    />
                  );
                }

                if (type === "text") {
                  if (mode === "view") {
                    return (
                      <div
                        key={key}
                        className={cn("flex flex-row gap-2 items-center", message.role === "user" ? "justify-end" : "justify-start")}
                      >
                        {/* {message.role === "user" && !isReadonly && (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                data-testid="message-edit-button"
                                variant="ghost"
                                className="px-2 h-fit rounded-full text-muted-foreground opacity-0 group-hover/message:opacity-100"
                                onClick={() => {
                                  setMode("edit");
                                }}
                              >
                                <PencilEditIcon />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>Edit message</TooltipContent>
                          </Tooltip>
                        )} */}

                        <div
                          data-testid="message-content"
                          className={cn("flex flex-col gap-4 max-w-full", {
                            "bg-primary text-primary-foreground px-3 py-2 rounded-xl": message.role === "user",
                          })}
                        >
                          <Markdown>{sanitizeText(part.text)}</Markdown>
                        </div>
                      </div>
                    );
                  }

                  if (mode === "edit") {
                    return (
                      <div
                        key={key}
                        className="flex flex-row gap-2 items-start"
                      >
                        <div className="size-8" />

                        <MessageEditor
                          key={message.id}
                          message={message}
                          setMode={setMode}
                          setMessages={setMessages}
                          reload={reload}
                        />
                      </div>
                    );
                  }
                }

                if (type === "tool-invocation") {
                  const { toolInvocation } = part;
                  const { toolName, toolCallId, state } = toolInvocation;
                  // UI Calling Tool
                  if (state === "call") {
                    const { args } = toolInvocation;

                    if (!BUILT_IN_TOOLS.includes(toolName as any)) {
                      return (
                        <McpToolCall
                          key={toolCallId}
                          toolInvocation={toolInvocation}
                        />
                      );
                    }

                    return (
                      <div
                        key={toolCallId}
                        className={cx({
                          skeleton: SKELETON_LOADING_TOOLS.includes(toolName as any),
                        })}
                      >
                        {toolName === TOOL_IDS.GET_WEATHER ? (
                          <Weather />
                        ) : toolName === TOOL_IDS.KNOWLEDGE_BASE ? (
                          <div className="flex items-center gap-2 text-muted-foreground">
                            <span>Đang tìm tài liệu…</span>
                          </div>
                        ) : toolName === TOOL_IDS.N8N_WORKFLOW ? (
                          <N8nWorkflowLoading />
                        ) : toolName === TOOL_IDS.LIST_DOCUMENTS ? (
                          <DocumentBadgeLoading />
                        ) : toolName === TOOL_IDS.CREATE_DOCUMENT ? (
                          <DocumentPreview
                            isReadonly={isReadonly}
                            args={args}
                          />
                        ) : toolName === TOOL_IDS.UPDATE_DOCUMENT ? (
                          <DocumentToolCall
                            type="update"
                            args={args}
                            isReadonly={isReadonly}
                          />
                        ) : toolName === TOOL_IDS.REQUEST_SUGGESTIONS ? (
                          <DocumentToolCall
                            type="request-suggestions"
                            args={args}
                            isReadonly={isReadonly}
                          />
                        ) : null}
                      </div>
                    );
                  }

                  if (state === "result") {
                    const { result } = toolInvocation;
                    const { args } = toolInvocation;

                    if (!BUILT_IN_TOOLS.includes(toolName as any)) {
                      return (
                        <McpToolCall
                          key={toolCallId}
                          toolInvocation={toolInvocation}
                        />
                      );
                    }

                    return (
                      <div key={toolCallId}>
                        {toolName === TOOL_IDS.KNOWLEDGE_BASE && result.length > 0 ? (
                          <KnowledgeBaseCollapse result={result} />
                        ) : toolName === TOOL_IDS.N8N_WORKFLOW ? (
                          <N8nWorkflowResults result={result} />
                        ) : toolName === TOOL_IDS.GET_WEATHER ? (
                          <Weather weatherAtLocation={result} />
                        ) : toolName === TOOL_IDS.LIST_DOCUMENTS ? (
                          <DocumentBadge
                            result={result}
                            args={args}
                          />
                        ) : toolName === TOOL_IDS.CREATE_DOCUMENT ? (
                          <DocumentPreview
                            isReadonly={isReadonly}
                            result={result}
                          />
                        ) : toolName === TOOL_IDS.UPDATE_DOCUMENT ? (
                          <DocumentToolResult
                            type="update"
                            result={result}
                            isReadonly={isReadonly}
                          />
                        ) : toolName === TOOL_IDS.REQUEST_SUGGESTIONS ? (
                          <DocumentToolResult
                            type="request-suggestions"
                            result={result}
                            isReadonly={isReadonly}
                          />
                        ) : toolName === TOOL_IDS.JIRA ? (
                          <PreviewAttachment attachment={result} />
                        ) : (
                          <pre>{JSON.stringify(result, null, 2)}</pre>
                        )}
                      </div>
                    );
                  }
                }
              });
            })()}

            {!isReadonly && (
              <MessageActions
                key={`action-${message.id}`}
                message={message}
                isLoading={isLoading}
                // chatId={chatId}
                // vote={vote}
              />
            )}
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export const PreviewMessage = memo(PurePreviewMessage, (prevProps, nextProps) => {
  if (prevProps.isLoading !== nextProps.isLoading) return false;
  if (prevProps.message.id !== nextProps.message.id) return false;
  if (prevProps.requiresScrollPadding !== nextProps.requiresScrollPadding) return false;
  if (!equal(prevProps.message.parts, nextProps.message.parts)) return false;
  if (!equal(prevProps.vote, nextProps.vote)) return false;

  return true;
});

export const ThinkingMessage = () => {
  const role = "assistant";
  const messages = ["Hmm...", "Phân vân nhỉ...", "*đang chạy đi hỏi đồng nghiệp*", "Mạng lag thế nhỉ!", "Gần ra rồi đâyy..."];
  const [msgIndex, setMsgIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setMsgIndex(() => Math.floor(Math.random() * messages.length));
    }, 10000);
    return () => clearInterval(interval);
  }, []);

  return (
    <motion.div
      data-testid="message-assistant-loading"
      className="w-full mx-auto max-w-3xl px-4 group/message min-h-96"
      initial={{ y: 5, opacity: 0 }}
      animate={{ y: 0, opacity: 1, transition: { delay: 1 } }}
      data-role={role}
    >
      <div
        className={cx(
          "flex gap-4 group-data-[role=user]/message:px-3 w-full group-data-[role=user]/message:w-fit group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl group-data-[role=user]/message:py-2 rounded-xl",
          {
            "group-data-[role=user]/message:bg-muted": true,
          }
        )}
      >
        <div className="size-8 flex items-center rounded-full justify-center ring-1 shrink-0 ring-border animate-pulse">
          <SparklesIcon size={14} />
        </div>
        <div className="flex flex-col gap-2 w-full pt-1">
          <div className="flex flex-col gap-4 text-muted-foreground">{messages[msgIndex]}</div>
        </div>
      </div>
    </motion.div>
  );
};
