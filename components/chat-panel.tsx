"use client";

// import { Model } from "@/lib/types/models";
import { cn } from "@/lib/utils";
import type { Message } from "ai";
import { ArrowUp, MessageCirclePlus, Square } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import Textarea from "react-textarea-autosize";
// import { EmptyScreen } from "./empty-screen";
// import { ModelSelector } from "./model-selector";
// import { SearchModeToggle } from "./search-mode-toggle";
import { Button } from "./ui/button";
import { IconLogo } from "@/components/ui/icons";

interface ChatPanelProps {
  input: string;
  handleInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  handleSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
  isLoading: boolean;
  messages: Message[];
  setMessages: (messages: Message[]) => void;
  query?: string;
  stop: () => void;
  append: (message: any) => void;
  // models?: Model[];
}

export function ChatPanel({
  input,
  handleInputChange,
  handleSubmit,
  isLoading,
  messages,
  setMessages,
  query,
  stop,
  append,
}: ChatPanelProps) {
  const router = useRouter();
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const isFirstRender = useRef(true);
  const [isComposing, setIsComposing] = useState(false); // Composition state
  const [enterDisabled, setEnterDisabled] = useState(false); // Disable Enter after composition ends
  const [showAtMentionPopup, setShowAtMentionPopup] = useState(false);

  const handleCompositionStart = () => setIsComposing(true);

  const handleCompositionEnd = () => {
    setIsComposing(false);
    setEnterDisabled(true);
    setTimeout(() => {
      setEnterDisabled(false);
    }, 300);
  };

  const handleNewChat = () => {
    setMessages([]);
    router.push("/");
  };

  const isToolInvocationInProgress = () => {
    if (!messages.length) return false;

    const lastMessage = messages[messages.length - 1];
    if (lastMessage.role !== "assistant" || !lastMessage.parts) return false;

    const parts = lastMessage.parts;
    const lastPart = parts[parts.length - 1];

    return (
      lastPart?.type === "tool-invocation" &&
      lastPart?.toolInvocation?.state === "call"
    );
  };

  // if query is not empty, submit the query
  useEffect(() => {
    if (isFirstRender.current && query && query.trim().length > 0) {
      append({
        role: "user",
        content: query,
      });
      isFirstRender.current = false;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [query, append]);

  const handleSelectOption = (option: string) => {
    // Replace the last '@' with the selected option
    const lastAtIndex = input.lastIndexOf("@");
    if (lastAtIndex !== -1) {
      const newInput = `${input.substring(0, lastAtIndex)}@${option} `;
      handleInputChange({
        target: { value: newInput },
      } as React.ChangeEvent<HTMLTextAreaElement>);
    } else {
      // If for some reason '@' is not found, just append
      handleInputChange({
        target: { value: `${input}@${option} ` },
      } as React.ChangeEvent<HTMLTextAreaElement>);
    }
    setShowAtMentionPopup(false);
    inputRef.current?.focus(); // Focus back on the input
  };

  return (
    <div
      className={cn(
        "mx-auto w-full",
        messages.length > 0
          ? "fixed bottom-0 left-0 right-0 bg-background"
          : "fixed bottom-8 left-0 right-0 top-6 flex flex-col items-center justify-center"
      )}
    >
      {messages.length === 0 && (
        <div className="mb-10 flex flex-col items-center gap-4">
          <IconLogo className="size-12 text-muted-foreground" />
          <p className="text-center text-3xl font-semibold">
            How can I help you today?
          </p>
        </div>
      )}
      <form
        onSubmit={handleSubmit}
        className={cn(
          "max-w-3xl w-full mx-auto",
          messages.length > 0 ? "px-2 pb-4" : "px-6"
        )}
      >
        <div className="relative flex flex-col w-full gap-2 bg-muted rounded-3xl border border-input">
          <Textarea
            ref={inputRef}
            name="input"
            rows={2}
            maxRows={5}
            tabIndex={0}
            onCompositionStart={handleCompositionStart}
            onCompositionEnd={handleCompositionEnd}
            placeholder="Ask a question..."
            spellCheck={false}
            value={input}
            disabled={isLoading || isToolInvocationInProgress()}
            className="resize-none w-full min-h-12 bg-transparent border-0 p-4 text-sm placeholder:text-muted-foreground focus-visible:outline-hidden disabled:cursor-not-allowed disabled:opacity-50"
            onChange={(e) => {
              handleInputChange(e);
              if (e.target.value.includes("@")) {
                setShowAtMentionPopup(true);
              } else {
                setShowAtMentionPopup(false);
              }
            }}
            onKeyDown={(e) => {
              if (
                e.key === "Enter" &&
                !e.shiftKey &&
                !isComposing &&
                !enterDisabled
              ) {
                if (input.trim().length === 0) {
                  e.preventDefault();
                  return;
                }
                e.preventDefault();
                const textarea = e.target as HTMLTextAreaElement;
                textarea.form?.requestSubmit();
              }
              // Hide popup on Escape key press
              if (e.key === "Escape") {
                setShowAtMentionPopup(false);
                e.preventDefault(); // Prevent default browser behavior
              }
            }}
          />

          {/* Bottom menu area */}
          <div className="flex items-center justify-between p-3">
            {/* <div className="flex items-center gap-2"> */}
            {/* <ModelSelector models={models || []} /> */}
            {/* <SearchModeToggle /> */}
            {/* </div> */}
            <div className="flex items-center gap-2">
              {messages.length > 0 && (
                <Button
                  variant="outline"
                  size="icon"
                  onClick={handleNewChat}
                  className="shrink-0 rounded-full group"
                  type="button"
                  disabled={isLoading || isToolInvocationInProgress()}
                >
                  <MessageCirclePlus className="size-4 group-hover:rotate-12 transition-all" />
                </Button>
              )}
              <Button
                type={isLoading ? "button" : "submit"}
                size={"icon"}
                variant={"outline"}
                className={cn(isLoading && "animate-pulse", "rounded-full")}
                disabled={
                  (input.length === 0 && !isLoading) ||
                  isToolInvocationInProgress()
                }
                onClick={isLoading ? stop : undefined}
              >
                {isLoading ? <Square size={20} /> : <ArrowUp size={20} />}
              </Button>
            </div>
          </div>
        </div>

        {showAtMentionPopup && (
          <div className="absolute top-[calc(100%+8px)] left-0 w-full bg-background border rounded-md shadow-lg p-2 z-10">
            <button
              className="block w-full text-left p-2 hover:bg-muted rounded-md"
              onClick={() => handleSelectOption("tickets")}
            >
              Tickets
            </button>
            <button
              className="block w-full text-left p-2 hover:bg-muted rounded-md"
              onClick={() => handleSelectOption("files")}
            >
              Files
            </button>
          </div>
        )}
        {/* {messages.length === 0 && (
          <EmptyScreen
            submitMessage={(message) => {
              handleInputChange({
                target: { value: message },
              } as React.ChangeEvent<HTMLTextAreaElement>);
            }}
            className={cn(showEmptyScreen ? "visible" : "invisible")}
          />
        )} */}
      </form>
    </div>
  );
}
