"use client";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useJiraTasks } from "@/hooks/use-jira";
import { useMention } from "@/hooks/use-mention";
import { useModalsStore } from "@/lib/store/modals";
import { formatDistance } from "date-fns";
import { FilterX, KanbanSquare, Search } from "lucide-react";
import { useCallback, useState, type Dispatch, type SetStateAction } from "react";
import { type ExtendedAttachment, isTicketAttachment } from "@/components/multimodal/types";

// Define the enhanced JiraTask type
export interface JiraTask {
  id: string;
  key: string;
  title: string;
  status?: {
    id: string;
    name: string;
    statusCategory?: string;
  } | null;
  priority?: {
    id: string;
    name: string;
    iconUrl?: string;
  } | null;
  dueDate?: string;
  project?: {
    id: string;
    key: string;
    name: string;
  } | null;
  description?: string;
  created?: string;
  updated?: string;
  creator?: {
    displayName: string;
    emailAddress: string;
  } | null;
}

// Status colors
const statusColors: Record<string, string> = {
  "To Do": "border-gray-200 text-gray-200",
  "In Progress": "border-blue-200 text-blue-200",
  Done: "border-green-200 text-green-200",
  Closed: "border-purple-200 text-purple-200",
  Canceled: "border-red-200 text-red-200",
  Blocked: "border-yellow-200 text-yellow-200",
  default: "border-gray-200 text-gray-200",
};

// Priority colors
const priorityColors: Record<string, string> = {
  Highest: "text-red-600",
  High: "text-orange-500",
  Medium: "text-yellow-500",
  Low: "text-blue-500",
  Lowest: "text-gray-500",
  default: "text-gray-500",
};

interface JiraTasksDialogProps {
  attachments?: Array<ExtendedAttachment>;
  setAttachments?: Dispatch<SetStateAction<Array<ExtendedAttachment>>>;
}

export function JiraTasksDialog(props: JiraTasksDialogProps = {}) {
  const { attachments = [], setAttachments } = props;
  const { jiraTasksOpen, setJiraTasksOpen } = useModalsStore();
  const { handleTicketMention } = useMention();

  const [searchTerm, setSearchTerm] = useState("");
  const [dateFilter, setDateFilter] = useState("month");
  const [statusFilter, setStatusFilter] = useState("all");

  // Use hook with filter parameters
  const { tasks, isLoading, error, refetch } = useJiraTasks({
    searchTerm,
    dateFilter,
    statusFilter,
  });

  // Log any errors
  if (error) {
    console.error("Error fetching Jira tasks:", error);
  }

  // Helper function to add attachment
  const addToAttachments = useCallback(
    (attachment: ExtendedAttachment) => {
      if (!setAttachments) {
        console.warn("JiraTasksDialog: setAttachments not provided, cannot add attachment");
        return;
      }

      setAttachments((prev) => {
        // Check if attachment already exists (by id or url)
        const exists = prev.some((att) => (att.id && att.id === attachment.id) || att.url === attachment.url);

        if (exists) {
          return prev; // Don't add duplicates
        }

        return [...prev, attachment];
      });
    },
    [setAttachments]
  );

  // Helper function to remove attachment
  const removeFromAttachments = useCallback(
    (taskId: string) => {
      if (!setAttachments) {
        console.warn("JiraTasksDialog: setAttachments not provided, cannot remove attachment");
        return;
      }

      setAttachments((prev) =>
        prev.filter((att) => {
          if (isTicketAttachment(att)) {
            return att.ticketData?.id !== taskId;
          }
          return true;
        })
      );
    },
    [setAttachments]
  );

  // Check if a task is already mentioned
  const isTaskMentioned = useCallback(
    (taskId: string) => {
      return attachments.some((att) => isTicketAttachment(att) && att.ticketData?.id === taskId);
    },
    [attachments]
  );

  // Handle search input
  const handleSearch = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  }, []);

  // Handle filter changes
  const handleDateFilterChange = useCallback((value: string) => {
    setDateFilter(value === "all" ? "" : value);
  }, []);

  const handleStatusFilterChange = useCallback((value: string) => {
    setStatusFilter(value === "all" ? "" : value);
  }, []);

  // Clear all filters
  const clearFilters = useCallback(() => {
    setSearchTerm("");
    setDateFilter("month");
    setStatusFilter("all");
    refetch();
  }, [refetch]);

  // Format updated date
  const formatUpdatedDate = (dateString?: string) => {
    if (!dateString) return "Unknown date";
    return formatDistance(new Date(dateString), new Date(), { addSuffix: true });
  };

  const handleMentionTicket = (task: JiraTask) => {
    handleTicketMention(task, addToAttachments);
  };

  return (
    <Dialog
      open={jiraTasksOpen}
      onOpenChange={setJiraTasksOpen}
    >
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <KanbanSquare className="w-4 h-4" />
            Jira Tickets
          </DialogTitle>
        </DialogHeader>

        {/* Search and filter controls */}
        <div className="flex flex-col sm:flex-row gap-2">
          <div className="relative flex-grow">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              className="pl-8"
              placeholder="Search tickets..."
              value={searchTerm}
              onChange={handleSearch}
            />
          </div>

          <div className="flex gap-2">
            <Select
              value={dateFilter}
              onValueChange={handleDateFilterChange}
            >
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Date Filter" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="week">This Week</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
                <SelectItem value="all">All Time</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={statusFilter}
              onValueChange={handleStatusFilterChange}
            >
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="To Do">To Do</SelectItem>
                <SelectItem value="In Progress">In Progress</SelectItem>
                <SelectItem value="Done">Done</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              size="icon"
              onClick={clearFilters}
              title="Clear Filters"
            >
              <FilterX className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Tickets list */}
        <div className="max-h-96 overflow-auto space-y-3">
          {isLoading && <div className="text-sm p-4 text-center">Loading tickets...</div>}
          {!isLoading && tasks.length === 0 && (
            <div className="text-sm p-4 text-center text-muted-foreground">No tickets found. Try adjusting your filters.</div>
          )}

          {tasks.map((task: JiraTask) => (
            <div
              key={task.id}
              className="p-3 border rounded-md hover:bg-accent/50 transition-colors"
            >
              <div className="flex justify-between">
                <div className="flex flex-col">
                  <div className="flex items-center gap-2 mb-1">
                    <Badge className="font-mono text-xs px-2 py-0.5 rounded">{task.key}</Badge>
                    {task.status && (
                      <Badge
                        variant="outline"
                        className={`${statusColors[task.status.name] || statusColors.default}`}
                      >
                        {task.status.name}
                      </Badge>
                    )}
                    {task.priority && (
                      <Badge
                        variant="secondary"
                        className={`text-xs font-medium ${priorityColors[task.priority.name] || priorityColors.default}`}
                      >
                        {task.priority.name}
                      </Badge>
                    )}
                  </div>

                  <h4 className="font-medium line-clamp-2">{task.title}</h4>

                  <div className="mt-1 flex flex-wrap gap-x-4 gap-y-1 text-xs text-muted-foreground">
                    {task.project && <span>Project: {task.project.name}</span>}
                    {task.updated && <span>Updated: {formatUpdatedDate(task.updated)}</span>}
                    {task.dueDate && <span>Due: {new Date(task.dueDate).toLocaleDateString()}</span>}
                  </div>

                  {task?.description && (
                    <p className="mt-2 text-sm text-muted-foreground line-clamp-2">{String(task.description).replace(/<[^>]*>?/gm, "")}</p>
                  )}
                </div>

                <div className="ml-4">
                  {isTaskMentioned(task.id) ? (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => removeFromAttachments(task.id)}
                    >
                      Remove
                    </Button>
                  ) : (
                    <Button
                      size="sm"
                      onClick={() => handleMentionTicket(task)}
                    >
                      Add
                    </Button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
}
