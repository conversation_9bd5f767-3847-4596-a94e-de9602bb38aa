import { Search } from "lucide-react";
import { ExternalLink } from "lucide-react";

interface Citation {
  url: string;
  text: string;
}

interface SearchResult {
  title: string;
  url: string;
  content: string;
}

interface DeepResearchResult {
  answer: string;
  citations: Citation[];
  searchResults: SearchResult[];
  relatedQuestions?: string[];
}

export const DeepResearchLoading = () => {
  return (
    <div className="flex items-center gap-2 text-sm italic text-muted-foreground">
      <span>Researching deeply...</span>
    </div>
  );
};

export const DeepResearchResults = ({ result }: { result: DeepResearchResult }) => {
  return (
    <div className="border rounded-xl overflow-hidden">
      <div className="bg-muted px-4 py-2.5 font-medium flex items-center gap-1">
        <Search size={16} />
        Research Results:
      </div>
      <div className="p-3 flex flex-col gap-3">
        {/* Main Answer */}
        <div className="prose dark:prose-invert max-w-none">{result.answer}</div>

        {/* Citations */}
        {result.citations.length > 0 && (
          <div className="mt-4">
            <h3 className="text-sm font-medium mb-2">Citations:</h3>
            <div className="space-y-2">
              {result.citations.map((citation, index) => (
                <a
                  key={index}
                  href={citation.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block text-sm text-muted-foreground hover:text-foreground transition-colors"
                >
                  <div className="flex items-start gap-2">
                    <span className="mt-0.5">•</span>
                    <span className="flex-1">{citation.text}</span>
                    <ExternalLink className="mt-1 flex-shrink-0" />
                  </div>
                </a>
              ))}
            </div>
          </div>
        )}

        {/* Search Results */}
        {result.searchResults.length > 0 && (
          <div className="mt-4">
            <h3 className="text-sm font-medium mb-2">Related Sources:</h3>
            <div className="space-y-3">
              {result.searchResults.map((searchResult, index) => (
                <a
                  key={index}
                  href={searchResult.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block border rounded-md p-3 hover:bg-muted/50 transition-colors"
                >
                  <div className="font-medium flex items-center gap-1">
                    {searchResult.title}
                    <ExternalLink />
                  </div>
                  <div className="text-sm text-muted-foreground mt-1">{searchResult.content}</div>
                </a>
              ))}
            </div>
          </div>
        )}

        {/* Related Questions */}
        {result.relatedQuestions && result.relatedQuestions.length > 0 && (
          <div className="mt-4">
            <h3 className="text-sm font-medium mb-2">Related Questions:</h3>
            <ul className="list-disc list-inside space-y-1 text-sm">
              {result.relatedQuestions.map((question, index) => (
                <li
                  key={index}
                  className="text-muted-foreground"
                >
                  {question}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};
