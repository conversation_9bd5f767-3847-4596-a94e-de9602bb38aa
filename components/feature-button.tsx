import { cn } from "@/lib/utils";
import { ChevronDown } from "lucide-react";
import React from "react";
import { Button } from "./ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "./ui/dropdown-menu";

export type ButtonChild = {
  id: string;
  icon: React.ComponentType<{ size?: number; className?: string }>;
  label: string;
  color?: string;
  onClick?: () => void;
  isAction?: boolean;
};

export type FeatureButton = {
  id: string;
  icon: React.ComponentType<{ size?: number; className?: string }>;
  label: string;
  type: "button" | "dropdown";
  color?: string;
  onClick?: () => void;
  children?: ButtonChild[];
  selectedWorkspace?: string;
};

export const FeatureButton = ({ button }: { button: FeatureButton }) => {
  if (button.type === "button") {
    return (
      <Button
        variant="outline"
        className="rounded-full cursor-pointer opacity-80 hover:opacity-100 transition-opacity"
        onClick={button.onClick}
      >
        <button.icon
          size={14}
          className={cn(button.color)}
        />
        {button.label}
      </Button>
    );
  }

  // Separate regular items from action items
  const regularItems = button.children?.filter((child) => !child.isAction) || [];
  const actionItems = button.children?.filter((child) => child.isAction) || [];
  const isWorkspaceDropdown = button.children?.some((child) => !!child.isAction);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className="rounded-full cursor-pointer opacity-80 hover:opacity-100 transition-opacity"
        >
          <button.icon size={14} />
          {button.label}
          <ChevronDown
            size={14}
            className="ml-1"
          />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className={cn("w-fit rounded-xl", isWorkspaceDropdown && "p-2")}>
        {isWorkspaceDropdown ? (
          // Workspace-specific layout
          <>
            {regularItems.map((child, index) => (
              <DropdownMenuItem
                key={child.label}
                className={cn("flex items-center gap-2 cursor-pointer hover:bg-zinc-700 rounded-lg px-3 py-2", index < 2 && "mb-1")}
                onClick={child.onClick}
              >
                <child.icon className={cn("size-4", child.color)} />
                <span>{child.label}</span>
              </DropdownMenuItem>
            ))}

            <div className="flex space-x-2 mt-2 pt-2 border-t border-zinc-700">
              {actionItems.map((child) => (
                <Button
                  key={child.label}
                  variant="outline"
                  className="flex-1 flex items-center justify-center gap-1 h-9 rounded-lg border-zinc-700 bg-zinc-800 text-sm"
                  onClick={child.onClick}
                >
                  <child.icon className="size-4" />
                  {child.label}
                </Button>
              ))}
            </div>
          </>
        ) : (
          // Standard dropdown layout
          button.children?.map((child) => (
            <DropdownMenuItem
              key={child.label}
              className="flex items-center gap-2 cursor-pointer hover:bg-zinc-700 rounded-lg m-1 px-3 py-2"
              onClick={child.onClick}
            >
              <child.icon className={cn("size-4", child.color)} />
              <span>{child.label}</span>
            </DropdownMenuItem>
          ))
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
