import React from "react";

interface SourceInfo {
  sourceType?: string; // Made optional as not all sources might have it explicitly
  id?: string; // Made optional
  url: string;
  title?: string; // For display, if available
  // Add other relevant fields like snippet, etc.
}

interface SourceChipProps {
  source: SourceInfo;
  referenceNumber: number;
}

export const SourceChip: React.FC<SourceChipProps> = ({ source, referenceNumber }) => {
  if (!source || !source.url) {
    return <span title="Source data missing">[{referenceNumber}]</span>;
  }

  return (
    <a
      href={source.url}
      target="_blank"
      rel="noopener noreferrer"
      className="inline-block bg-muted dark:bg-zinc-700 dark:text-zinc-200 text-zinc-700 text-xs font-medium mr-1 px-2.5 py-1 rounded-full hover:bg-zinc-200 dark:hover:bg-zinc-600 hover:underline no-underline cursor-pointer transition-colors duration-150 ease-in-out"
      title={source.title || source.url}
    >
      [{referenceNumber}]
    </a>
  );
};
