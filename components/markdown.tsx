import Link from "next/link";
import React, { memo, useMemo } from "react";
import ReactMarkdown, { type Components } from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import { CodeBlock } from "./code-block";
import { SourceChip } from "./source-chip";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { JiraTicketMention } from "./multimodal/components/jira-ticket-mention";

// Regex to match Jira ticket mentions in the format [JIRA:XXX-123]
const JIRA_TICKET_REGEX = /\[JIRA:([A-Z]+-\d+)\]/g;

// Define SourceInfo type (can be imported if defined elsewhere centrally)
interface SourceInfo {
  sourceType?: string;
  id?: string;
  url: string;
  title?: string;
}

const getComponents = (sources?: SourceInfo[]): Partial<Components> => ({
  // @ts-expect-error
  code: CodeBlock,
  pre: ({ children }) => <>{children}</>,
  ol: ({ node, children, ...props }) => {
    return (
      <ol
        className="list-decimal list-outside ml-4"
        {...props}
      >
        {children}
      </ol>
    );
  },
  li: ({ node, children, ...props }) => {
    return (
      <li
        className="py-1"
        {...props}
      >
        {children}
      </li>
    );
  },
  ul: ({ node, children, ...props }) => {
    return (
      <ul
        className="list-decimal list-outside ml-4"
        {...props}
      >
        {children}
      </ul>
    );
  },
  strong: ({ node, children, ...props }) => {
    return (
      <span
        className="font-semibold"
        {...props}
      >
        {children}
      </span>
    );
  },
  a: ({ node, children, href, ...props }) => {
    if (typeof href === "string" && href.startsWith("source-ref:")) {
      const refNumStr = href.substring("source-ref:".length);
      const refNum = Number.parseInt(refNumStr, 10);

      if (!Number.isNaN(refNum) && sources && sources[refNum - 1]) {
        const source = sources[refNum - 1];
        return (
          <SourceChip
            source={source}
            referenceNumber={refNum}
          />
        );
      }
      // Fallback if source-ref is invalid or sources not provided
      return <span title={`Invalid source reference: ${refNumStr}`}>{children}</span>;
    }

    // Default link rendering for other links
    return (
      <Link
        href={href || "#"}
        className="text-blue-500 hover:underline mx-0.5"
        target="_blank"
        rel="noreferrer"
        {...props} // Spread other props, href is explicitly handled
      >
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge
              variant="outline"
              className="rounded-full"
            >
              {children}
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <p>{href}</p>
          </TooltipContent>
        </Tooltip>
      </Link>
    );
  },
  h1: ({ node, children, ...props }) => {
    return (
      <h1
        className="text-3xl font-semibold mt-6 mb-2"
        {...props}
      >
        {children}
      </h1>
    );
  },
  h2: ({ node, children, ...props }) => {
    return (
      <h2
        className="text-2xl font-semibold mt-6 mb-2"
        {...props}
      >
        {children}
      </h2>
    );
  },
  h3: ({ node, children, ...props }) => {
    return (
      <h3
        className="text-xl font-semibold mt-6 mb-2"
        {...props}
      >
        {children}
      </h3>
    );
  },
  h4: ({ node, children, ...props }) => {
    return (
      <h4
        className="text-lg font-semibold mt-6 mb-2"
        {...props}
      >
        {children}
      </h4>
    );
  },
  h5: ({ node, children, ...props }) => {
    return (
      <h5
        className="text-base font-semibold mt-6 mb-2"
        {...props}
      >
        {children}
      </h5>
    );
  },
  h6: ({ node, children, ...props }) => {
    return (
      <h6
        className="text-sm font-semibold mt-6 mb-2"
        {...props}
      >
        {children}
      </h6>
    );
  },
});

const remarkPlugins = [remarkGfm];
const rehypePlugins = [rehypeRaw];

interface MarkdownProps {
  children: string;
  sources?: SourceInfo[]; // Add sources to props
}

const NonMemoizedMarkdown = ({ children, sources }: MarkdownProps) => {
  // Process content to extract Jira tickets and replace with custom components
  const { contentParts, hasJiraTickets } = useMemo(() => {
    if (!children) return { contentParts: [], hasJiraTickets: false };

    // Find all Jira ticket mentions
    const matches = Array.from(children.matchAll(JIRA_TICKET_REGEX));
    if (matches.length === 0) {
      return { contentParts: [{ type: "text", content: children }], hasJiraTickets: false };
    }

    // Split the content into text and Jira ticket parts
    const parts: Array<{ type: "text" | "jira"; content: string }> = [];
    let lastIndex = 0;

    matches.forEach((match) => {
      const [fullMatch, ticketKey] = match;
      const matchIndex = match.index!;

      // Add text before the match
      if (matchIndex > lastIndex) {
        parts.push({
          type: "text",
          content: children.substring(lastIndex, matchIndex),
        });
      }

      // Add the Jira ticket
      parts.push({
        type: "jira",
        content: ticketKey,
      });

      lastIndex = matchIndex + fullMatch.length;
    });

    // Add any remaining text after the last match
    if (lastIndex < children.length) {
      parts.push({
        type: "text",
        content: children.substring(lastIndex),
      });
    }

    return { contentParts: parts, hasJiraTickets: true };
  }, [children]);

  // If no Jira tickets, render normally
  if (!hasJiraTickets) {
    return (
      <ReactMarkdown
        remarkPlugins={remarkPlugins}
        rehypePlugins={rehypePlugins}
        components={getComponents(sources)}
      >
        {children}
      </ReactMarkdown>
    );
  }

  // Render the mixed content with Jira tickets
  return (
    <div className="space-y-2">
      {contentParts.map((part, index) =>
        part.type === "text" ? (
          <ReactMarkdown
            key={`text-${index}`}
            remarkPlugins={remarkPlugins}
            rehypePlugins={rehypePlugins}
            components={getComponents(sources)}
          >
            {part.content}
          </ReactMarkdown>
        ) : (
          <JiraTicketMention
            key={`jira-${part.content}-${index}`}
            ticketKey={part.content}
            baseUrl={process.env.NEXT_PUBLIC_JIRA_BASE_URL || process.env.JIRA_BASE_URL}
          />
        )
      )}
    </div>
  );
};

export const Markdown = memo(NonMemoizedMarkdown, (prevProps, nextProps) => {
  return prevProps.children === nextProps.children && prevProps.sources === nextProps.sources;
});
