"use client";

import { toast as sonnerToast } from "sonner";
import { useState } from "react";
import { cn } from "@/lib/utils";

export function memoryToast(content: string) {
  sonnerToast.custom((id) => <MemoryToast id={id} content={content} />);
}

function MemoryToast({ id, content }: { id: string | number; content: string }) {
  const [show, setShow] = useState(false);
  return (
    <div className="flex w-full toast-mobile:w-[356px] justify-center">
      <div
        key={id}
        onClick={() => setShow((v) => !v)}
        className={cn(
          "bg-zinc-100 p-3 rounded-lg w-full toast-mobile:w-fit cursor-pointer"
        )}
      >
        <div className="font-medium">Memory updated</div>
        {show && <div className="text-sm mt-2 whitespace-pre-wrap">{content}</div>}
      </div>
    </div>
  );
}
