"use client";

// import { JiraTask } from "@/components/jira-tasks-dialog";
import { useFileManager } from "@/components/multimodal/hooks/use-file-manager";
import {
  type ExtendedAttachment,
  isFileAttachment,
  isTicketAttachment,
} from "@/components/multimodal/types";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useJiraTasks } from "@/hooks/use-jira";
import { useMention } from "@/hooks/use-mention";
import { useModalsStore } from "@/lib/store/modals";
import { cn } from "@/lib/utils";
import { Check, File, Search, SquareKanban } from "lucide-react";
import {
  useEffect,
  useRef,
  useState,
  type Dispatch,
  type SetStateAction,
  useCallback,
} from "react";

export function MentionSelector({
  attachments,
  setAttachments,
}: {
  attachments: Array<ExtendedAttachment>;
  setAttachments: Dispatch<SetStateAction<Array<ExtendedAttachment>>>;
}) {
  const { handleTicketMention, handleFileMention } = useMention();
  const { mentionSelectorOpen, setMentionSelectorOpen } = useModalsStore();
  const [searchTerm, setSearchTerm] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const itemRefs = useRef<(HTMLDivElement | null)[]>([]);
  const [activeTab, setActiveTab] = useState<"tickets" | "files">("files");

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value.replace("@", ""));
    setSelectedIndex(0);
  };

  const addToAttachments = useCallback(
    (attachment: ExtendedAttachment) => {
      setAttachments((prev) => {
        const exists = prev.some(
          (att) =>
            (att.id && att.id === attachment.id) || att.url === attachment.url
        );
        if (exists) return prev;
        return [...prev, attachment];
      });
    },
    [setAttachments]
  );

  const mentionedAttachments = attachments.filter((att) => att.fromMention);

  const { tasks: jiraTickets, isLoading: isLoadingTickets } = useJiraTasks({
    searchTerm: activeTab === "tickets" ? searchTerm : "",
    dateFilter: "",
    statusFilter: "all",
  });

  const { files: allFiles, isLoading: isLoadingFiles } = useFileManager();

  const filteredFiles = allFiles?.filter((file) => {
    if (activeTab === "files" && !searchTerm.trim()) return true;
    if (activeTab !== "files") return true;
    const searchLower = searchTerm.toLowerCase();
    return (
      file.name?.toLowerCase().includes(searchLower) ||
      file.contentType?.toLowerCase().includes(searchLower) ||
      file.size?.toString().includes(searchLower)
    );
  });

  const currentResults = (() => {
    if (activeTab === "tickets") {
      return (jiraTickets || []).map((ticket) => ({
        type: "ticket" as const,
        data: ticket,
      }));
    } else {
      return (filteredFiles || []).map((file) => ({
        type: "file" as const,
        data: file,
      }));
    }
  })();

  useEffect(() => {
    itemRefs.current = itemRefs.current.slice(0, currentResults.length);
    if (selectedIndex >= currentResults.length) {
      setSelectedIndex(currentResults.length > 0 ? 0 : -1);
    }
  }, [currentResults, selectedIndex, activeTab]);

  const handleItemSelect = useCallback(
    (type: string, data: any) => {
      if (type === "ticket") {
        handleTicketMention(data, addToAttachments);
      } else if (type === "file") {
        handleFileMention(data, addToAttachments);
      }
      setMentionSelectorOpen(false);
    },
    [
      handleTicketMention,
      handleFileMention,
      addToAttachments,
      setMentionSelectorOpen,
    ]
  );

  const isItemMentioned = useCallback(
    (type: string, data: any) => {
      if (type === "ticket") {
        return mentionedAttachments.some(
          (att) => isTicketAttachment(att) && att.ticketData?.id === data.id
        );
      } else if (type === "file") {
        return mentionedAttachments.some(
          (att) =>
            isFileAttachment(att) &&
            (att.id === data.id || att.url === data.url)
        );
      }
      return false;
    },
    [mentionedAttachments]
  );

  const scrollSelectedIntoView = useCallback((index: number) => {
    if (index >= 0 && itemRefs.current[index]) {
      itemRefs.current[index]?.scrollIntoView({
        behavior: "smooth",
        block: "nearest",
      });
    }
  }, []);

  const setItemRef = useCallback(
    (index: number) => (el: HTMLDivElement | null) => {
      itemRefs.current[index] = el;
    },
    []
  );

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (!currentResults.length) return;

      switch (e.key) {
        case "ArrowDown":
          e.preventDefault();
          setSelectedIndex((prevIndex) => {
            const newIndex = Math.min(prevIndex + 1, currentResults.length - 1);
            scrollSelectedIntoView(newIndex);
            return newIndex;
          });
          break;
        case "ArrowUp":
          e.preventDefault();
          setSelectedIndex((prevIndex) => {
            const newIndex = Math.max(prevIndex - 1, 0);
            scrollSelectedIntoView(newIndex);
            return newIndex;
          });
          break;
        case "Enter":
          e.preventDefault();
          if (selectedIndex >= 0 && selectedIndex < currentResults.length) {
            const selected = currentResults[selectedIndex];
            handleItemSelect(selected.type, selected.data);
          }
          break;
        case "Escape":
          e.preventDefault();
          setMentionSelectorOpen(false);
          break;
      }
    },
    [
      currentResults,
      selectedIndex,
      scrollSelectedIntoView,
      handleItemSelect,
      setMentionSelectorOpen,
    ]
  );

  useEffect(() => {
    if (mentionSelectorOpen) {
      setSearchTerm("");
      setActiveTab("files");
      setSelectedIndex(0);
      setTimeout(() => inputRef.current?.focus(), 50);
    }
  }, [mentionSelectorOpen]);

  useEffect(() => {
    setSelectedIndex(0);
  }, [activeTab]);

  const renderCurrentTabItems = () => {
    const itemsToRender = currentResults;
    const isLoading =
      activeTab === "tickets" ? isLoadingTickets : isLoadingFiles;

    if (isLoading) {
      return (
        <div className="p-4 text-center text-sm text-muted-foreground">
          Loading...
        </div>
      );
    }
    if (itemsToRender.length === 0) {
      return (
        <div className="p-4 text-center text-sm text-muted-foreground">
          {searchTerm ? "No matching items found" : "No items available"}
        </div>
      );
    }

    return (
      <div className="overflow-y-auto" style={{ maxHeight: "200px" }}>
        {itemsToRender.map((item, index) => (
          <div
            ref={setItemRef(index)}
            key={
              item.type === "ticket"
                ? `ticket-${item.data.key}`
                : `file-${item.data.id || item.data.url}`
            }
            className={cn(
              "flex items-start gap-2 p-2 w-full text-left rounded-md cursor-pointer",
              selectedIndex === index
                ? "bg-accent text-accent-foreground"
                : "hover:bg-accent/50"
            )}
            onClick={() => handleItemSelect(item.type, item.data)}
            onMouseEnter={() => setSelectedIndex(index)}
          >
            {item.type === "ticket" ? (
              <SquareKanban className="h-4 w-4 mt-0.5 text-muted-foreground" />
            ) : (
              <File className="h-4 w-4 mt-0.5 text-muted-foreground" />
            )}
            <div className="flex-1 overflow-hidden">
              <div className="text-sm font-medium truncate">
                {item.type === "ticket" ? (
                  <>
                    <Badge
                      variant="outline"
                      className="text-xs font-mono px-1 py-0 mr-2"
                    >
                      {item.data.key}
                    </Badge>
                    {item.data.title}
                  </>
                ) : (
                  item.data.name
                )}
              </div>
            </div>
            {isItemMentioned(item.type, item.data) && (
              <Check className="h-4 w-4 mt-0.5 text-green-600" />
            )}
          </div>
        ))}
      </div>
    );
  };

  return (
    <Popover
      open={mentionSelectorOpen}
      onOpenChange={(open) => {
        if (!open) {
          setSearchTerm("");
        }
        setMentionSelectorOpen(open);
      }}
    >
      <PopoverTrigger asChild>
        <Button
          size="sm"
          type="button"
          variant="outline"
          className={cn(
            "h-6 rounded-sm text-xs opacity-80 p-1.5",
            mentionedAttachments?.length > 0 ? "" : ""
          )}
          onClick={() => setMentionSelectorOpen(true)}
        >
          @
          {mentionedAttachments?.length > 0
            ? mentionedAttachments.length
            : " Add context"}
        </Button>
      </PopoverTrigger>
      <PopoverContent
        side="top"
        align="start"
        className="rounded-xl p-2 w-[400px]"
      >
        <div className="relative mb-3">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            ref={inputRef}
            className="pl-8"
            placeholder={`Search ${activeTab}...`}
            value={searchTerm}
            onChange={handleSearchChange}
            onKeyDown={handleKeyDown}
          />
        </div>

        <Tabs
          value={activeTab}
          onValueChange={(value) => setActiveTab(value as "tickets" | "files")}
          className="w-full"
        >
          <TabsList className="grid w-full grid-cols-1 mb-2">
            <TabsTrigger value="files">Files</TabsTrigger>
            {/* <TabsTrigger value="tickets">Tickets</TabsTrigger> */}
          </TabsList>
          <TabsContent value="files">{renderCurrentTabItems()}</TabsContent>
          {/* <TabsContent value="tickets">{renderCurrentTabItems()}</TabsContent> */}
        </Tabs>
      </PopoverContent>
    </Popover>
  );
}
