import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { type ExtendedAttachment, isTicketAttachment } from "@/components/multimodal/types";
import { FileIcon, TicketIcon, X } from "lucide-react";

type Props = {
  attachment: ExtendedAttachment;
  onRemove: () => void;
};

const MentionItem = ({ attachment, onRemove }: Props) => {
  const isTicket = isTicketAttachment(attachment);
  const displayName = isTicket ? `${attachment.ticketData?.key}` : attachment.name || "File";

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge
            variant="outline"
            className="opacity-90 group rounded-sm pl-1 cursor-default"
          >
            <Button
              variant="ghost"
              size="icon"
              className="size-4 p-0 rounded-full"
              onClick={onRemove}
              type="button"
            >
              <X className="size-3 hidden group-hover:block" />
              <span className="block group-hover:hidden">{isTicket ? <TicketIcon className="size-3" /> : <FileIcon className="size-3" />}</span>
            </Button>
            {displayName}
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <p>{isTicket ? attachment.ticketData?.title : attachment.name}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default MentionItem;
