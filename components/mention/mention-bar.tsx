
import type { ExtendedAttachment } from "@/components/multimodal/types";
import { useModalsStore } from "@/lib/store/modals";
import type { Dispatch, SetStateAction } from "react";

const MentionBar = ({
  attachments,
  setAttachments,
}: {
  attachments: Array<ExtendedAttachment>;
  setAttachments: Dispatch<SetStateAction<Array<ExtendedAttachment>>>;
}) => {
  const { mentionSelectorOpen } = useModalsStore();

  // Filter attachments to show only those created from mentions
  const mentionedAttachments = attachments.filter((att) => att.fromMention);

  // Show MentionBar if mention selector is open OR there are mentioned items
  const shouldShow = mentionSelectorOpen || mentionedAttachments.length > 0;

  // Function to clear all mentioned attachments
  const clearAllMentions = () => {
    setAttachments((prev) => prev.filter((att) => !att.fromMention));
  };

  // Function to remove a specific mentioned attachment
  const removeMentionedAttachment = (attachmentToRemove: ExtendedAttachment) => {
    setAttachments((prev) => prev.filter((att) => !(att.id && att.id === attachmentToRemove.id) && !(att.url === attachmentToRemove.url)));
  };

  return (
    <></>
    // <div
    //   className={cn(
    //     "bg-muted -mb-4 mx-4 rounded-t-md p-1.5 flex gap-1 flex-wrap relative transition-all duration-300",
    //     shouldShow ? "translate-y-0" : "translate-y-full"
    //   )}
    // >
    //   <MentionSelector
    //     attachments={attachments}
    //     setAttachments={setAttachments}
    //   />
    //   {mentionedAttachments.map((attachment) => (
    //     <MentionItem
    //       key={attachment.id || attachment.url}
    //       attachment={attachment}
    //       onRemove={() => removeMentionedAttachment(attachment)}
    //     />
    //   ))}

    //   <Button
    //     size="sm"
    //     type="button"
    //     variant="ghost"
    //     className={cn(
    //       "h-6 rounded-sm text-xs opacity-80 p-1.5 hover:opacity-100 absolute right-0 top-1.5 group",
    //       mentionedAttachments.length === 0 && "hidden"
    //     )}
    //     onClick={clearAllMentions}
    //   >
    //     <X className="size-3" />
    //     <span className="max-w-0 group-hover:max-w-12 opacity-0 group-hover:opacity-100 overflow-hidden transition-all duration-200">Clear all</span>
    //   </Button>
    // </div>
  );
};

export default MentionBar;
