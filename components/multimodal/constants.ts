/**
 * File extension mappings for content types.
 * Used for file input accept attribute and extension validation.
 */

// TODO: Implement conversion logic for unsupported types (e.g., DOCX to PDF/TXT) or restrict further based on Gemini model used.
// See https://firebase.google.com/docs/ai-logic/input-file-requirements?api=dev for Gemini supported types.

// Maps MIME types to their common extensions for the <input accept> attribute.
export const FILE_EXTENSIONS_AND_SUPPORTED_CONTENT_TYPES: Record<string, string> = {
  // Images
  "image/png": ".png",
  "image/jpeg": ".jpg",
  "image/webp": ".webp",
  "image/heic": ".heic",
  "image/heif": ".heif",
  // Audio
  "audio/wav": ".wav",
  "audio/mp3": ".mp3",
  "audio/aac": ".aac",
  "audio/flac": ".flac",
  "audio/opus": ".opus",
  "audio/m4a": ".m4a", // Note: Firebase doc lists audio/m4a as MPA - audio/m4a
  "audio/mpeg": ".mp3",
  "audio/mpga": ".mp3",
  "audio/mp4": ".mp4", // audio part of mp4
  "audio/webm": ".webm", // audio part of webm
  // Video - Note: Ensure your AI model and use case supports video processing if you enable these widely.
  "video/mp4": ".mp4",
  "video/mpeg": ".mpeg",
  "video/mov": ".mov",
  "video/quicktime": ".mov", // Often the same as video/mov
  "video/webm": ".webm",
  "video/flv": ".flv",
  "video/avi": ".avi",
  "video/wmv": ".wmv",
  "video/3gpp": ".3gpp",
  // Documents
  "application/pdf": ".pdf",
  "text/plain": ".txt",
};

export const SUPPORTED_CONTENT_TYPES = Object.keys(FILE_EXTENSIONS_AND_SUPPORTED_CONTENT_TYPES) as readonly string[];
