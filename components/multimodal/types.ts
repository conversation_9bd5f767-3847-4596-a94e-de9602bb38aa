import type { Attachment as OriginalAttachment } from "ai";
import type { JiraTask } from "@/components/jira-tasks-dialog";

/**
 * Extends the base Attachment type from the AI SDK with additional UI-specific fields
 * for managing file uploads and display within the multimodal input component.
 */
export interface ExtendedAttachment extends OriginalAttachment {
  /** Optional unique identifier for the attachment, typically generated on the client-side (e.g., UUID). */
  id?: string;
  /** The File object, present for files selected by the user before upload. */
  file?: File;
  /** The type of the attachment, e.g., "file", "image", "url". */
  attachmentType: "file" | "image" | "url" | "document" | "ticket";
  /** Indicates if the attachment was added via a mention/slash command. */
  fromMention: boolean;
  /** The current upload state of the file. */
  uploadState?: "uploading" | "uploaded" | "failed";
  /** Optional AbortController for cancelling an ongoing upload. */
  abortController?: AbortController;
  /**
   * Additional metadata for file attachments
   */
  size?: number;
  supabasePath?: string;
  /**
   * Jira ticket data for ticket attachments
   */
  ticketData?: JiraTask;
}

/**
 * Type guard to check if an attachment is a file attachment
 */
export function isFileAttachment(
  attachment: ExtendedAttachment
): attachment is ExtendedAttachment & { attachmentType: "file" } {
  return (
    attachment.attachmentType === "file" ||
    (!attachment.attachmentType && !attachment.ticketData)
  );
}

/**
 * Type guard to check if an attachment is a ticket attachment
 */
export function isTicketAttachment(
  attachment: ExtendedAttachment
): attachment is ExtendedAttachment & {
  attachmentType: "ticket";
  ticketData: JiraTask;
} {
  return attachment.attachmentType === "ticket" && !!attachment.ticketData;
}

/**
 * Helper function to map fileType to contentType
 */
function mapFileTypeToContentType(fileType?: string): string {
  const typeMap: Record<string, string> = {
    pdf: "application/pdf",
    txt: "text/plain",
    md: "text/markdown",
    csv: "text/csv",
    json: "application/json",
    doc: "application/msword",
    docx: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    xls: "application/vnd.ms-excel",
    xlsx: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    png: "image/png",
    jpg: "image/jpeg",
    jpeg: "image/jpeg",
    gif: "image/gif",
  };

  return typeMap[fileType?.toLowerCase() || ""] || "application/octet-stream";
}

/**
 * Helper function to convert Google Drive sharing URLs to direct download URLs
 */
function convertGoogleDriveUrl(url: string): string {
  // Check if it's a Google Drive URL
  if (!url.includes("docs.google.com")) {
    return url;
  }

  try {
    // Extract file ID from Google Drive URL
    const fileIdMatch = url.match(/\/d\/([a-zA-Z0-9-_]+)/);
    if (!fileIdMatch) {
      return url;
    }

    const fileId = fileIdMatch[1];

    // Convert based on document type
    if (url.includes("/spreadsheets/")) {
      // Google Sheets - export as CSV
      return `https://docs.google.com/spreadsheets/d/${fileId}/export?format=csv`;
    } else if (url.includes("/document/")) {
      // Google Docs - export as plain text
      return `https://docs.google.com/document/d/${fileId}/export?format=txt`;
    } else if (url.includes("/presentation/")) {
      // Google Slides - export as plain text
      return `https://docs.google.com/presentation/d/${fileId}/export?format=txt`;
    }

    // Default: try to use the view URL with export
    return `https://drive.google.com/uc?id=${fileId}&export=download`;
  } catch (error) {
    console.warn("Failed to convert Google Drive URL:", error);
    return url;
  }
}

/**
 * Helper function to create a file attachment from mention
 * Handles both Attachment objects and FileMetadata objects from the file manager
 */
export function createFileAttachmentFromMention(file: any): ExtendedAttachment {
  // Handle FileMetadata from file manager
  if (file.fileType) {
    return {
      id: file.id,
      name: file.name,
      url: convertGoogleDriveUrl(file.url),
      contentType: mapFileTypeToContentType(file.fileType),
      size: file.size,
      attachmentType: "file",
      fromMention: true,
    };
  }

  // Handle regular Attachment objects
  return {
    ...file,
    attachmentType: "file",
    fromMention: true,
    // Convert Google Drive URLs to direct download URLs
    url: convertGoogleDriveUrl(file.url),
    // Ensure contentType is always set to a valid value
    contentType: file.contentType || "application/octet-stream",
  };
}

/**
 * Helper function to create a ticket attachment from mention
 */
export function createTicketAttachmentFromMention(
  ticket: JiraTask
): ExtendedAttachment {
  return {
    id: ticket.id,
    name: `${ticket.key}: ${ticket.title}`,
    contentType: "application/json",
    url: `jira:${ticket.key}`, // Use a special URL scheme for tickets
    attachmentType: "ticket",
    ticketData: ticket,
    fromMention: true,
  };
}
