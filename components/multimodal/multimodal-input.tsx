"use client";

import { Greeting } from "@/components/greeting";
import <PERSON><PERSON><PERSON> from "@/components/mention/mention-bar";
import { AttachmentsButton, SendButton, StopButton } from "@/components/multimodal/components/input-buttons";
import { AttachmentsPreview } from "@/components/multimodal/components/pure-attachments-preview";
import { FILE_EXTENSIONS_AND_SUPPORTED_CONTENT_TYPES } from "@/components/multimodal/constants";
import { useDragAndDrop } from "@/components/multimodal/hooks/use-drag-n-drop";
import { useFileUpload } from "@/components/multimodal/hooks/use-file-upload";
import type { ExtendedAttachment } from "@/components/multimodal/types";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useScrollToBottom } from "@/hooks/use-scroll-to-bottom";
import { type Mode, MODES } from "@/lib/constants";
import { useChatSettingsStore } from "@/lib/store/chat-settings-store";
import { useModalsStore } from "@/lib/store/modals";
import { cn } from "@/lib/utils";
import type { UseChatHelpers } from "@ai-sdk/react";
import type { UIMessage } from "ai";
import cx from "classnames";
import equal from "fast-deep-equal";
import { AnimatePresence, motion } from "framer-motion";
import { ArrowDown, PaperclipIcon, Search, SquareKanban } from "lucide-react";
import React, { memo, useCallback, useEffect, useRef, type ChangeEvent, type Dispatch, type SetStateAction } from "react";
import { toast } from "sonner";
import { useLocalStorage, useWindowSize } from "usehooks-ts";
import { Button } from "../ui/button";
import { Textarea } from "../ui/textarea";

// const getConfigFeatureButtons = (): FeatureButtonType[] => [];

function PureMultimodalInput({
  chatId,
  input,
  setInput,
  status,
  stop,
  attachments,
  setAttachments,
  messages,
  setMessages,
  handleSubmit,
  className,
}: // isPoAssistantMode,
// setIsPoAssistantMode,
{
  chatId: string;
  input: UseChatHelpers["input"];
  setInput: UseChatHelpers["setInput"];
  status: UseChatHelpers["status"];
  stop: () => void;
  attachments: Array<ExtendedAttachment>;
  setAttachments: Dispatch<SetStateAction<Array<ExtendedAttachment>>>;
  messages: Array<UIMessage>;
  setMessages: UseChatHelpers["setMessages"];
  handleSubmit: UseChatHelpers["handleSubmit"];
  className?: string;
  // selectedVisibilityType: VisibilityType;
  // isPoAssistantMode: boolean;
  // setIsPoAssistantMode: React.Dispatch<React.SetStateAction<boolean>>;
}) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dropZoneRef = useRef<HTMLDivElement>(null);
  const [localStorageInput, setLocalStorageInput] = useLocalStorage("input", "");
  const { toggleJiraTasks } = useModalsStore();
  const { width } = useWindowSize();

  // Use the new simplified file upload hook
  const { uploadQueue, handleFileChange } = useFileUpload(setAttachments);

  const isMessageEmpty = messages.length === 0;

  // Handle textarea input changes
  const handleInput = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(event.target.value);
  };

  // Drag-and-drop handler: convert dropped files to a FileList-like event and call handleFileChange
  const processFilesForDragAndDrop = useCallback(
    async (droppedFiles: File[]) => {
      // Create a fake event to reuse handleFileChange
      const dataTransfer = new DataTransfer();
      droppedFiles.forEach((file) => dataTransfer.items.add(file));
      const event = { target: { files: dataTransfer.files } } as ChangeEvent<HTMLInputElement>;
      await handleFileChange(event);
    },
    [handleFileChange]
  );

  const { isDraggingOver, handleDragOver, handleDragLeave, handleDrop } = useDragAndDrop(processFilesForDragAndDrop);
  const fileInputOnChange = useCallback(
    async (event: ChangeEvent<HTMLInputElement>) => {
      await handleFileChange(event);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    },
    [handleFileChange]
  );

  // Submit the form with message and attachments
  const submitForm = useCallback(
    (message?: string) => {
      const successfullyUploadedAttachments = attachments.filter((att) => !!att.url);

      const attachmentsForAISDK = successfullyUploadedAttachments.map((att) => ({
        name: att.name,
        contentType: att.contentType,
        url: att.url,
      }));

      console.log("attachmentsForAISDK", attachmentsForAISDK);

      if (message || attachmentsForAISDK.length > 0) {
        handleSubmit(undefined, {
          experimental_attachments: attachmentsForAISDK,
        });

        window.history.replaceState({}, "", `/chat/${chatId}`);
        setAttachments([]);
        setLocalStorageInput("");
        if (width && width > 768) {
          textareaRef.current?.focus();
        }
      } else {
        toast.info("Please enter a message...");
      }
    },
    [attachments, setAttachments, setLocalStorageInput, width, chatId, handleSubmit]
  );

  // const renderFeatureButtons = useMemo(() => {
  //   return getConfigFeatureButtons().map((button) => {
  //     if (button.type !== "dropdown" || !button.children) return button;

  //     const selectedOptionId = selectedOptions[button.id];
  //     if (!selectedOptionId) {
  //       return { ...button };
  //     }

  //     const selectedChild = button.children?.find((child) => child.id === selectedOptionId);
  //     if (!selectedChild) {
  //       return { ...button };
  //     }

  //     return {
  //       ...button,
  //       icon: selectedChild.icon,
  //       label: selectedChild.label,
  //       color: selectedChild.color,
  //     };
  //   });
  // }, [selectedOptions]);

  // Initialize textarea with localStorage content
  useEffect(() => {
    if (textareaRef.current) {
      const domValue = textareaRef.current.value;
      // Prefer DOM value over localStorage to handle hydration
      const finalValue = domValue || localStorageInput || "";
      setInput(finalValue);
    }
    // Only run once after hydration
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Update localStorage when input changes
  useEffect(() => {
    setLocalStorageInput(input);
  }, [input, setLocalStorageInput]);

  // Auto-scroll to bottom when new messages are submitted
  const { isAtBottom, scrollToBottom } = useScrollToBottom();

  useEffect(() => {
    if (status === "submitted") {
      scrollToBottom();
    }
  }, [status, scrollToBottom]);

  return (
    <form className={cn("flex mx-auto px-4 bg-background pb-4 md:pb-6 w-full md:max-w-3xl", isMessageEmpty && "flex-1 items-center")}>
      <div className={cn("relative w-full flex flex-col gap-4")}>
        {isMessageEmpty && <Greeting />}

        {/* Scroll to bottom button */}
        <AnimatePresence>
          {!isAtBottom && !isMessageEmpty && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              transition={{ type: "spring", stiffness: 300, damping: 20 }}
              className="absolute left-1/2 bottom-38 -translate-x-1/2 z-50 rounded-full bg-muted/90"
            >
              <Button
                data-testid="scroll-to-bottom-button"
                className="rounded-full "
                size="icon"
                variant="outline"
                onClick={(event) => {
                  event.preventDefault();
                  scrollToBottom();
                }}
              >
                <ArrowDown />
              </Button>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Drag and drop overlay */}
        <AnimatePresence>
          {isDraggingOver && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center"
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              <div className="border-2 border-dashed border-primary/50 rounded-xl p-12 text-center">
                <div className="mx-auto mb-4 text-muted-foreground flex justify-center">
                  <PaperclipIcon size={24} />
                </div>
                <h3 className="text-xl font-medium mb-2">Drop files to upload</h3>
                <p className="text-sm text-muted-foreground">Supported formats: PDF, TXT, CSV, JSON, PNG, JPG, GIF, DOC, DOCX, XLS, XLSX</p>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* File input */}
        <input
          type="file"
          className="fixed -top-4 -left-4 size-0.5 opacity-0 pointer-events-none"
          ref={fileInputRef}
          multiple
          onChange={fileInputOnChange}
          tabIndex={-1}
          accept={Object.values(FILE_EXTENSIONS_AND_SUPPORTED_CONTENT_TYPES).join(",")}
        />

        {/* Attachments preview */}
        <AttachmentsPreview
          attachments={attachments}
          uploadQueue={uploadQueue}
          setAttachments={setAttachments}
        />

        <MentionBar
          attachments={attachments}
          setAttachments={setAttachments}
        />

        {/* Input area */}
        <InputArea
          ref={dropZoneRef}
          textareaRef={textareaRef}
          isDraggingOver={isDraggingOver}
          handleDragOver={handleDragOver}
          handleDragLeave={handleDragLeave}
          handleDrop={handleDrop}
          input={input}
          handleInput={handleInput}
          className={className}
          status={status}
          fileInputRef={fileInputRef}
          stop={stop}
          setMessages={setMessages}
          submitForm={() => submitForm(input)}
          uploadQueue={uploadQueue}
          attachments={attachments}
        />

        {isMessageEmpty && (
          <div className="flex flex-row gap-2 absolute -bottom-12 left-1/2 -translate-x-1/2 flex-wrap items-start justify-center w-full">
            {/* {renderFeatureButtons.map((button) => (
              <FeatureButton
                key={button.label}
                button={button}
              />
            ))} */}
            {/* <Button
              variant="outline"
              type="button"
              onClick={() => {
                console.log("isPoAssistantMode", isPoAssistantMode);
                setIsPoAssistantMode(!isPoAssistantMode);
              }}
              className={`rounded-full cursor-pointer opacity-80 hover:opacity-100 transition-colors ${
                isPoAssistantMode ? "!bg-green-500 text-white" : ""
              }`}
            >
              <User className="size-4.5" /> PO Assistant
            </Button> */}
            <Button
              variant="outline"
              type="button"
              onClick={toggleJiraTasks}
              className="rounded-full cursor-pointer opacity-80 hover:opacity-100 transition-opacity"
            >
              <SquareKanban className="size-4.5" /> Jira Tickets
            </Button>
          </div>
        )}
      </div>
    </form>
  );
}

// Component for displaying attachment previews

// Component for the input area
const InputArea = memo(
  React.forwardRef<
    HTMLDivElement,
    {
      textareaRef: React.RefObject<HTMLTextAreaElement | null>;
      isDraggingOver: boolean;
      handleDragOver: (event: React.DragEvent) => void;
      handleDragLeave: () => void;
      handleDrop: (event: React.DragEvent) => void;
      input: string;
      handleInput: (event: React.ChangeEvent<HTMLTextAreaElement>) => void;
      className?: string;
      status: UseChatHelpers["status"];
      fileInputRef: React.RefObject<HTMLInputElement | null>;
      stop: () => void;
      setMessages: UseChatHelpers["setMessages"];
      submitForm: (message?: string) => void;
      uploadQueue: string[];
      attachments: Array<ExtendedAttachment>;
    }
  >(
    (
      {
        textareaRef,
        isDraggingOver,
        handleDragOver,
        handleDragLeave,
        handleDrop,
        input,
        handleInput,
        className,
        status,
        fileInputRef,
        stop,
        setMessages,
        submitForm,
        uploadQueue,
        attachments,
      },
      ref
    ) => {
      const { forceDeepResearch, toggleForceDeepResearch, mode, setMode } = useChatSettingsStore();
      const { setMentionSelectorOpen } = useModalsStore();

      useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
          if (event.key === "@") {
            event?.stopPropagation();
            setMentionSelectorOpen(true);
          }
        };

        const textarea = textareaRef.current;
        if (textarea) {
          textarea.addEventListener("keydown", handleKeyDown);
        }

        return () => {
          if (textarea) {
            textarea.removeEventListener("keydown", handleKeyDown);
          }
        };
      }, [input, setMentionSelectorOpen, textareaRef]);

      return (
        <div
          ref={ref}
          className={cn("bg-muted p-2.5 rounded-2xl border-zinc-700 border-[1px] z-10", isDraggingOver && "border-primary border-2")}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <Textarea
            data-testid="multimodal-input"
            ref={textareaRef}
            placeholder="Send a message or drop files here..."
            value={input}
            onChange={handleInput}
            className={cx(
              "min-h-10 max-h-[calc(75dvh)] rounded-none! overflow-y-auto resize-none text-base! border-0! focus:ring-0! focus:border-0! p-1 bg-transparent! mb-2",
              className
            )}
            rows={2}
            autoFocus
            onKeyDown={(event) => {
              if (event.key === "Enter" && !event.shiftKey && !event.nativeEvent.isComposing) {
                event.preventDefault();

                if (status !== "ready") {
                  toast.error("Please wait for the model to finish its response!");
                } else {
                  submitForm(input);
                }
              }
            }}
            // onSubmit={onSubmit}
          />

          <div className="w-full flex flex-row justify-between">
            <div className="flex gap-1.5">
              <AttachmentsButton
                fileInputRef={fileInputRef}
                status={status}
              />
              <Button
                variant={forceDeepResearch ? "default" : "outline"}
                size="sm"
                onClick={(e) => {
                  e.preventDefault();
                  toggleForceDeepResearch();
                }}
                title="Toggle Deep Research"
                className={cn(
                  "text-muted-foreground hover:text-foreground transition-colors rounded-full w-fit px-3 text-xs",
                  forceDeepResearch ? "text-primary-foreground hover:text-primary-foreground" : "bg-transparent!"
                )}
              >
                <Search size={16} />
                Deep Research
              </Button>
            </div>
            <div className="flex flex-row gap-2">
              <Select
                onValueChange={(value) => setMode(value as Mode)}
                value={mode}
              >
                <SelectTrigger
                  size="sm"
                  className="w-fit rounded-full h-10 bg-transparent! border-0! text-xs! cursor-pointer py-1.5!"
                >
                  <SelectValue placeholder="Mode" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem
                    value={MODES.DEFAULT}
                    className="text-xs"
                  >
                    Default
                  </SelectItem>
                  <SelectItem
                    value={MODES.PO}
                    className="text-xs"
                  >
                    Product Owner
                  </SelectItem>
                </SelectContent>
              </Select>
              {status === "submitted" ? (
                <StopButton
                  stop={stop}
                  setMessages={setMessages}
                />
              ) : (
                <SendButton
                  input={input}
                  submitForm={() => submitForm(input)}
                  uploadQueue={uploadQueue}
                  attachments={attachments}
                />
              )}
            </div>
          </div>
        </div>
      );
    }
  )
);

InputArea.displayName = "InputArea";

export const MultimodalInput = memo(PureMultimodalInput, (prevProps, nextProps) => {
  if (prevProps.input !== nextProps.input) return false;
  if (prevProps.status !== nextProps.status) return false;
  if (!equal(prevProps.attachments, nextProps.attachments)) return false;

  return true;
});
