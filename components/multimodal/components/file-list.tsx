"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import dayjs from "dayjs";
import { FileIcon, FolderIcon, Lock, Trash2Icon, Users } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import type { FileMetadata } from "../hooks/use-file-manager";
import type { ExtendedFileMetadata } from "../mocks/personal-files";

interface FileListProps {
  files: (FileMetadata | ExtendedFileMetadata)[];
  isLoading: boolean;
  error: Error | null;
  activeTab?: "company" | "personal" | "memory";
  onDeleteFile?: (fileId: string) => Promise<void>;
}

export function FileList({ files, isLoading, error, activeTab, onDeleteFile }: FileListProps) {
  const [fileVisibilityStates, setFileVisibilityStates] = useState<Record<string, "private" | "company">>({});

  const handleVisibilityChange = (fileId: string, newVisibility: "private" | "company") => {
    setFileVisibilityStates((prev) => ({
      ...prev,
      [fileId]: newVisibility,
    }));

    const visibilityText = newVisibility === "private" ? "private" : "company shared";
    toast.success(`File visibility changed to ${visibilityText}`);
  };

  const getFileVisibility = (file: FileMetadata | ExtendedFileMetadata): "private" | "company" => {
    // Check if file has visibility property (ExtendedFileMetadata)
    if ("visibility" in file) {
      // Use local state if it exists, otherwise use file's visibility
      return fileVisibilityStates[file.id] || file.visibility;
    }
    // For regular FileMetadata, default to private
    return fileVisibilityStates[file.id] || "private";
  };

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-destructive">
        <p>Error loading files: {error.message}</p>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <span className="loading loading-spinner loading-md" />
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full w-full">
      {files.length === 0 ? (
        <div className="flex flex-col items-center justify-center flex-1 text-muted-foreground min-h-96">
          <FolderIcon className="w-12 h-12 mb-2" />
          <p>{"No files found"}</p>
        </div>
      ) : (
        <div className="flex-1 overflow-y-auto max-h-96">
          {files.map((file) => {
            const currentVisibility = getFileVisibility(file);
            return (
              <div
                key={file.id}
                className="flex items-center gap-2 p-2 pl-0 border-b hover:bg-muted-foreground/10 cursor-pointer h-14"
                onClick={() => {
                  window.open(file.url, "_blank");
                }}
              >
                {/* File icon and name */}
                <div>
                  {file.source === "company" && <FolderIcon className="size-8 text-blue-500" />}
                  {file.source !== "company" && <FileIcon className="size-8 text-gray-500" />}
                </div>
                {/* File info */}
                <div className="flex-1 space-y-0.5 overflow-hidden">
                  <div className="flex items-center gap-2 w-full">
                    <p className="text-sm font-medium truncate max-w-1/2 w-fit">{file.name}</p>
                    {activeTab === "company" && <Badge variant="outline">{file.department.toUpperCase()}</Badge>}
                    {file.isAiGenerated && <Badge variant="outline">AI</Badge>}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    <p>{dayjs(file.updatedAt).format("HH:mm DD/MM/YYYY")}</p>
                  </div>
                </div>
                {/* Action buttons */}
                <div className="flex items-center gap-2">
                  {activeTab === "personal" && (
                    <div
                      className="flex items-center gap-2"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <Select
                        value={currentVisibility}
                        onValueChange={(value: "private" | "company") => handleVisibilityChange(file.id, value)}
                      >
                        <SelectTrigger className="h-8! text-xs px-2 bg-amber-200">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="private">
                            <div className="flex items-center gap-1.5 p-0">
                              <Lock className="size-3" />
                              <span className="text-xs">Private</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="company">
                            <div className="flex items-center gap-1.5 p-0">
                              <Users className="size-3" />
                              <span className="text-xs">Company</span>
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  {activeTab === "personal" && onDeleteFile && (
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={async (e) => {
                        e.stopPropagation();
                        if (window.confirm(`Are you sure you want to delete ${file.name}?`)) {
                          await onDeleteFile(file.id);
                        }
                      }}
                      className="size-8"
                      title={`Delete ${file.name}`}
                    >
                      <Trash2Icon className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}
