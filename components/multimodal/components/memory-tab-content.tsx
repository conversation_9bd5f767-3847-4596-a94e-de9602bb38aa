"use client";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { fetcher } from "@/lib/utils";
import { Edit3, PlusCircle, Trash2 } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import useSWR from "swr";

interface MemoryItem {
  id: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  userId: string;
}

export function MemoryTabContent() {
  const { data: memories, error: memoriesError, mutate: mutateMemories } = useSWR<MemoryItem[]>("/api/memory", fetcher);
  const [newMemoryContent, setNewMemoryContent] = useState("");
  const [editingMemory, setEditingMemory] = useState<MemoryItem | null>(null);
  const [editText, setEditText] = useState("");
  const [isSubmittingMemory, setIsSubmittingMemory] = useState(false);

  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [memoryToDeleteId, setMemoryToDeleteId] = useState<string | null>(null);

  const handleAddMemory = async () => {
    if (!newMemoryContent.trim()) {
      toast.error("Memory content cannot be empty.");
      return;
    }
    setIsSubmittingMemory(true);
    try {
      const response = await fetch("/api/memory", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ content: newMemoryContent }),
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: response.statusText }));
        throw new Error(errorData.message || "Failed to add memory");
      }
      await response.json();
      toast.success("Memory added successfully!");
      setNewMemoryContent("");
      mutateMemories();
    } catch (err) {
      toast.error(`Failed to add memory: ${(err as Error).message}`);
      console.error("Error adding memory:", err);
    } finally {
      setIsSubmittingMemory(false);
    }
  };

  const handleEditMemory = (memory: MemoryItem) => {
    setEditingMemory(memory);
    setEditText(memory.content);
  };

  const handleSaveEdit = async () => {
    if (!editingMemory || !editText.trim()) {
      toast.error("Content cannot be empty.");
      return;
    }
    setIsSubmittingMemory(true);
    try {
      const response = await fetch(`/api/memory`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ memoryId: editingMemory.id, content: editText }),
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: response.statusText }));
        throw new Error(errorData.message || "Failed to update memory");
      }
      await response.json();
      toast.success("Memory updated successfully!");
      setEditingMemory(null);
      setEditText("");
      mutateMemories();
    } catch (err) {
      toast.error(`Failed to update memory: ${(err as Error).message}`);
      console.error("Error updating memory:", err);
    } finally {
      setIsSubmittingMemory(false);
    }
  };

  const handleDeleteMemory = (memoryId: string) => {
    setMemoryToDeleteId(memoryId);
    setIsDeleteDialogOpen(true);
  };

  const confirmDeleteMemory = async () => {
    if (!memoryToDeleteId) return;
    setIsSubmittingMemory(true);
    try {
      const response = await fetch(`/api/memory?memoryId=${memoryToDeleteId}`, {
        method: "DELETE",
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: response.statusText }));
        throw new Error(errorData.message || "Failed to delete memory");
      }
      await response.json();
      toast.success("Memory deleted successfully!");
      mutateMemories();
    } catch (err) {
      toast.error(`Failed to delete memory: ${(err as Error).message}`);
      console.error("Error deleting memory:", err);
    } finally {
      setIsSubmittingMemory(false);
      setIsDeleteDialogOpen(false);
      setMemoryToDeleteId(null);
    }
  };

  return (
    <div className="">
      <div className="flex gap-2 items-center mb-2">
        <Input
          placeholder="Add a new memory..."
          value={newMemoryContent}
          onChange={(e) => setNewMemoryContent(e.target.value)}
          className="flex-grow"
          onKeyPress={(e) => e.key === "Enter" && !isSubmittingMemory && handleAddMemory()}
          disabled={isSubmittingMemory}
        />
        <Button
          onClick={handleAddMemory}
          disabled={isSubmittingMemory || !newMemoryContent.trim()}
        >
          <PlusCircle className="mr-2 h-4 w-4" /> {isSubmittingMemory ? "Adding..." : "Add"}
        </Button>
      </div>

      <div className="space-y-2 h-fit overflow-y-auto max-h-96">
        <div className="flex flex-col items-center justify-center">
          {memoriesError && <p className="text-red-500">Failed to load memories.</p>}
          {!memories && !memoriesError && <p>Loading memories...</p>}
          {memories && memories.length === 0 && <p>No memories found. Add one above!</p>}
        </div>
        {memories?.map((item) => (
          <div
            key={item.id}
            className="p-2 pl-4 border rounded-md shadow-sm hover:shadow-md transition-shadow"
          >
            {editingMemory?.id === item.id ? (
              <div className="space-y-0.5">
                <Input
                  value={editText}
                  onChange={(e) => setEditText(e.target.value)}
                  className="flex-grow"
                  onKeyPress={(e) => e.key === "Enter" && !isSubmittingMemory && handleSaveEdit()}
                  disabled={isSubmittingMemory}
                />
                <div className="flex gap-2 justify-end mt-2.5">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setEditingMemory(null)}
                    disabled={isSubmittingMemory}
                  >
                    Cancel
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleSaveEdit}
                    disabled={isSubmittingMemory || !editText.trim()}
                  >
                    {isSubmittingMemory ? "Saving..." : "Save"}
                  </Button>
                </div>
              </div>
            ) : (
              <div className="flex justify-between items-center">
                <p className="text-sm flex-grow mr-4 break-all whitespace-pre-wrap">{item.content}</p>
                <div className="flex gap-2 flex-shrink-0">
                  <Button
                    size="icon"
                    variant="ghost"
                    onClick={() => handleEditMemory(item)}
                    title="Edit Memory"
                  >
                    <Edit3 className="h-4 w-4" />
                  </Button>
                  <Button
                    size="icon"
                    variant="ghost"
                    onClick={() => handleDeleteMemory(item.id)}
                    title="Delete Memory"
                    disabled={isSubmittingMemory}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
            {/* <p className="text-xs text-muted-foreground mt-1">Last updated: {dayjs(item.updatedAt).format("DD/MM/YYYY HH:mm")}</p> */}
          </div>
        ))}
      </div>

      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>This action cannot be undone. This will permanently delete this memory.</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => setMemoryToDeleteId(null)}
              disabled={isSubmittingMemory}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteMemory}
              disabled={isSubmittingMemory}
            >
              {isSubmittingMemory ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
