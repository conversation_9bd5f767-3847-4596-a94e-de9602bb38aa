"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { clearDocumentCache } from "@/lib/cache";
import { useModalsStore } from "@/lib/store/modals";
import { Brain, Building, SearchIcon, UploadIcon } from "lucide-react";
import React, { useMemo, useRef, useState, useCallback } from "react";
import { toast } from "sonner";
import { FILE_EXTENSIONS_AND_SUPPORTED_CONTENT_TYPES } from "../constants";
import {
  type ActiveFileManagerTab,
  useFileManager,
} from "../hooks/use-file-manager";
import { getMockPersonalFiles } from "../mocks/personal-files";
import { FileList } from "./file-list";
import { MemoryTabContent } from "./memory-tab-content";

export function FileManagerModal() {
  const { fileManagerOpen, setFileManagerOpen } = useModalsStore();
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Only fetch data when modal is open
  const {
    files: realFiles,
    isLoading,
    error,
    filter,
    activeTab,
    setActiveTab,
    mutateFiles,
    searchTerm,
    setSearchTerm,
  } = useFileManager(fileManagerOpen);

  // Use mock data for personal files, real data for others
  const files = useMemo(() => {
    if (activeTab === "personal") {
      return getMockPersonalFiles({
        search: filter.search,
        limit: undefined,
      });
    }
    return realFiles;
  }, [activeTab, realFiles, filter.search]);

  // Mock loading and error states for personal files
  const currentIsLoading = activeTab === "personal" ? false : isLoading;
  const currentError = activeTab === "personal" ? null : error;

  const handleFileInputChange = useCallback(() => {
    // This can be used to update UI if needed when files are selected,
    // but the main logic is in handleUploadFiles.
  }, []);

  const handleUploadFiles = useCallback(
    async (event: React.FormEvent) => {
      event.preventDefault();
      if (!fileInputRef.current?.files?.length) {
        toast.error("No files selected.");
        return;
      }
      setIsUploading(true);
      const formData = new FormData();
      for (let i = 0; i < fileInputRef.current.files.length; i++) {
        formData.append("files", fileInputRef.current.files[i]);
      }
      const currentDestination =
        activeTab === "company" ? "company" : "personal";
      formData.append("fileDestination", currentDestination);

      try {
        // For personal files, simulate upload (since we're using mock data)
        if (activeTab === "personal") {
          // Simulate upload delay
          await new Promise((resolve) => setTimeout(resolve, 1000));
          toast.success(
            `Uploaded ${fileInputRef.current.files.length} file(s) successfully. You can adjust visibility for each file individually.`
          );
          if (fileInputRef.current) {
            fileInputRef.current.value = "";
          }
          return;
        }

        // Use real API for company files
        const response = await fetch("/api/files/upload", {
          method: "POST",
          body: formData,
        });
        if (!response.ok) {
          const errorData = await response
            .json()
            .catch(() => ({ message: response.statusText }));
          throw new Error(
            errorData.message || `Upload failed: ${response.statusText}`
          );
        }
        const result = await response.json();
        if (mutateFiles) mutateFiles();

        // Clear the document cache for this source to ensure tool gets fresh data
        const { userId } = await fetch("/api/auth/user")
          .then((res) => res.json())
          .catch(() => ({ userId: null }));

        if (userId) {
          clearDocumentCache(
            userId,
            currentDestination as "personal" | "company"
          );
        } else {
          // If we can't get the userId, clear the entire cache to be safe
          clearDocumentCache();
        }

        toast.success(
          `Uploaded ${
            result.files?.length || 0
          } file(s) successfully to ${currentDestination}`
        );
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      } catch (error) {
        console.error("Error uploading files:", error);
        toast.error(`Failed to upload files: ${(error as Error).message}`);
      } finally {
        setIsUploading(false);
      }
    },
    [activeTab, mutateFiles]
  );

  const handleTabChange = useCallback(
    (value: string) => {
      setActiveTab(value as ActiveFileManagerTab);
    },
    [setActiveTab]
  );

  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchTerm(e.target.value);
    },
    [setSearchTerm]
  );

  if (!fileManagerOpen) {
    return null;
  }

  return (
    <Dialog open={fileManagerOpen} onOpenChange={setFileManagerOpen}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>Memory Manager</DialogTitle>
        </DialogHeader>

        <Tabs
          value={activeTab}
          onValueChange={handleTabChange}
          className="overflow-x-hidden"
        >
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="company">
              <Building className="mr-2 h-4 w-4" /> Company Files
            </TabsTrigger>
            <TabsTrigger value="memory">
              <Brain className="mr-2 h-4 w-4" /> Memories
            </TabsTrigger>
          </TabsList>

          {(activeTab === "company" || activeTab === "personal") && (
            <div className="flex gap-2">
              <div className="relative flex-1">
                <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <Input
                  placeholder="Search files..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  className="pl-9"
                />
              </div>
              {activeTab === "personal" && (
                <form
                  onSubmit={handleUploadFiles}
                  className="flex items-center gap-2"
                >
                  <Button
                    type="button"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={isUploading}
                    variant="outline"
                  >
                    {isUploading ? (
                      "Uploading..."
                    ) : (
                      <UploadIcon className="h-4 w-4" />
                    )}
                  </Button>
                  <input
                    ref={fileInputRef}
                    type="file"
                    className="hidden"
                    multiple
                    onChange={handleFileInputChange}
                    accept={Object.values(
                      FILE_EXTENSIONS_AND_SUPPORTED_CONTENT_TYPES
                    ).join(",")}
                  />
                </form>
              )}
            </div>
          )}

          <TabsContent value="company">
            <FileList
              files={files}
              isLoading={currentIsLoading}
              error={currentError}
              activeTab="company"
            />
          </TabsContent>
          <TabsContent value="memory">
            <MemoryTabContent />
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
