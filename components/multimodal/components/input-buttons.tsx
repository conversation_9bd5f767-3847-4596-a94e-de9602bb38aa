import { ArrowUpIcon, PaperclipIcon, StopIcon } from "@/components/icons";
import { Button } from "@/components/ui/button";
import type { UseChatHelpers } from "@ai-sdk/react";
import { memo } from "react";
import type { ExtendedAttachment } from "../types";
import { Loader2 } from "lucide-react";

function PureAttachmentsButton({
  fileInputRef,
  status,
}: {
  fileInputRef: React.MutableRefObject<HTMLInputElement | null>;
  status: UseChatHelpers["status"];
}) {
  return (
    <Button
      data-testid="attachments-button"
      className="rounded-full size-8 cursor-pointer opacity-60 hover:opacity-100 transition-opacity"
      onClick={(event) => {
        event.preventDefault();
        fileInputRef.current?.click();
      }}
      disabled={status !== "ready"}
      variant="outline"
      size="sm"
    >
      <PaperclipIcon size={12} />
    </Button>
  );
}
export const AttachmentsButton = memo(PureAttachmentsButton);

function PureStopButton({ stop, setMessages }: { stop: () => void; setMessages: UseChatHelpers["setMessages"] }) {
  return (
    <Button
      data-testid="stop-button"
      className="rounded-full size-8 border dark:border-zinc-600 cursor-pointer"
      onClick={(event) => {
        event.preventDefault();
        stop();
        setMessages((messages) => messages);
      }}
      size="sm"
    >
      <StopIcon size={14} />
    </Button>
  );
}
export const StopButton = memo(PureStopButton);

interface PureSendButtonProps {
  submitForm: () => void;
  input: string;
  uploadQueue: Array<string>;
  attachments: Array<ExtendedAttachment>;
}

function PureSendButton({ submitForm, input, uploadQueue, attachments }: PureSendButtonProps) {
  const hasSuccessfullyUploadedAttachments = attachments.some((att) => att.uploadState === "uploaded" && att.attachmentType === "file");

  const isUploading = uploadQueue.length > 0;
  const isDisabled = isUploading || (input.trim().length === 0 && !hasSuccessfullyUploadedAttachments);

  return (
    <Button
      data-testid="send-button"
      className="rounded-full size-8 border dark:border-zinc-600 cursor-pointer"
      onClick={(event) => {
        event.preventDefault();
        if (!isDisabled) {
          submitForm();
        }
      }}
      disabled={isDisabled}
      size="sm"
    >
      {isUploading ? <Loader2 className="h-4 w-4 animate-spin" /> : <ArrowUpIcon size={14} />}
    </Button>
  );
}
export const SendButton = memo(PureSendButton, (prevProps, nextProps) => {
  if (prevProps.uploadQueue.length !== nextProps.uploadQueue.length) return false;
  if (prevProps.input !== nextProps.input) return false;
  if (prevProps.attachments.length !== nextProps.attachments.length) return false;
  if (prevProps.attachments.some((att, i) => att.uploadState !== nextProps.attachments[i].uploadState)) return false;
  return true;
});
