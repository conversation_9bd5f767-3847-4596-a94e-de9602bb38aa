"use client";

import React from "react";
import ReactMarkdown from "react-markdown";
import rehypeRaw from "rehype-raw";
import remarkGfm from "remark-gfm";
import { cn } from "@/lib/utils";
import { JiraTicketMention } from "./jira-ticket-mention";

interface MessageContentProps {
  content: string;
  className?: string;
}

// Regex to match Jira ticket mentions in the format [JIRA:XXX-123]
const JIRA_TICKET_REGEX = /\[JIRA:([A-Z]+-\d+)\]/g;

export function MessageContent({ content, className }: MessageContentProps) {
  // Process the content to find and replace Jira ticket mentions
  const processedContent = React.useMemo(() => {
    if (!content) return [];

    // Split content by Jira ticket mentions
    const parts = content.split(JIRA_TICKET_REGEX);
    const matches = content.match(JIRA_TICKET_REGEX) || [];

    // Interleave text and Jira ticket components
    const result: React.ReactNode[] = [];

    parts.forEach((part, index) => {
      // Add the text part
      if (part) {
        result.push(
          <ReactMarkdown
            key={`text-${index}`}
            className={cn("prose prose-slate max-w-none dark:prose-invert prose-p:leading-relaxed prose-pre:p-0", className)}
            remarkPlugins={[remarkGfm]}
            rehypePlugins={[rehypeRaw]}
          >
            {part}
          </ReactMarkdown>
        );
      }

      // Add Jira ticket component if there's a match
      if (index < matches.length) {
        const ticketKey = matches[index].match(/\[JIRA:([A-Z]+-\d+)\]/)?.[1];
        if (ticketKey) {
          result.push(
            <JiraTicketMention
              key={`jira-${index}`}
              ticketKey={ticketKey}
              baseUrl={process.env.NEXT_PUBLIC_JIRA_BASE_URL || process.env.JIRA_BASE_URL}
            />
          );
        }
      }
    });

    return result;
  }, [content, className]);

  return (
    <div className="space-y-2">
      {processedContent.length > 0 ? (
        processedContent
      ) : (
        <ReactMarkdown
          className={cn("prose prose-slate max-w-none dark:prose-invert prose-p:leading-relaxed prose-pre:p-0", className)}
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeRaw]}
        >
          {content}
        </ReactMarkdown>
      )}
    </div>
  );
}
