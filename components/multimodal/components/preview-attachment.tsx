import { cn } from "@/lib/utils";
import { X, File as FileIcon, Loader2, AlertCircle } from "lucide-react";
import Image from "next/image";
import type { ExtendedAttachment } from "../types";

interface PreviewAttachmentProps {
  attachment: ExtendedAttachment;
  onRemove: () => void;
  // isUploadingFromQueue is deprecated in favor of attachment.uploadState
}

export const PreviewAttachment: React.FC<PreviewAttachmentProps> = ({ attachment, onRemove }) => {
  const { id, name, url, contentType, size, uploadState } = attachment;

  const isImage = contentType?.startsWith("image/");
  // Add checks for video/audio if you want specific icons/previews for them later
  // const isVideo = contentType?.startsWith("video/");
  // const isAudio = contentType?.startsWith("audio/");

  const isUploading = uploadState === "uploading";
  const hasFailed = uploadState === "failed";
  const isUploaded = uploadState === "uploaded";

  const handleRemoveClick = () => {
    if (!isUploading && id) {
      // Only allow removal if not actively uploading
      onRemove();
    }
  };

  return (
    <div
      data-testid="input-attachment-preview"
      className={cn(
        "relative group flex flex-col items-center justify-center p-2 border rounded-md w-28 h-28 text-center",
        isUploading && "opacity-75 cursor-default",
        hasFailed && "border-red-500 bg-red-50/50"
      )}
    >
      {/* Overlay for uploading/failed states */}
      {isUploading && (
        <div className="absolute inset-0 z-10 flex flex-col items-center justify-center rounded-md bg-background/30 backdrop-blur-sm">
          <Loader2 className="h-8 w-8 animate-spin text-primary mb-1" />
          <p className="text-xs text-muted-foreground">Uploading...</p>
        </div>
      )}
      {hasFailed && (
        <div className="absolute inset-0 z-10 flex flex-col items-center justify-center rounded-md bg-red-50/30 backdrop-blur-sm">
          <AlertCircle className="h-8 w-8 text-red-600 mb-1" />
          <p className="text-xs text-red-700">Failed</p>
        </div>
      )}

      {/* Content: Image or Icon */}
      <div className={cn("flex-grow flex items-center justify-center w-full", (isUploading || hasFailed) && "blur-sm")}>
        {isImage && isUploaded && url ? (
          <Image
            src={url}
            alt={name || "Uploaded image"}
            width={80}
            height={80}
            className="max-w-full max-h-full rounded-md object-contain"
          />
        ) : (
          <FileIcon className="h-10 w-10 text-muted-foreground" />
        )}
      </div>

      {/* File Name and Size */}
      <div className={cn("w-full text-xs mt-1 text-muted-foreground", (isUploading || hasFailed) && "blur-sm")}>
        <p className="truncate">{name || "File"}</p>
        {size && !isUploading && !hasFailed && <p className="text-xs text-muted-foreground/80">{`${(size / 1024).toFixed(1)} KB`}</p>}
      </div>

      {/* Remove Button */}
      {!isUploading && !hasFailed && id && (
        <button
          onClick={handleRemoveClick}
          className="absolute -right-2 -top-2 z-20 rounded-full bg-background border border-destructive text-destructive p-0.5 shadow-md transition-colors hover:bg-destructive hover:text-destructive-foreground opacity-0 group-hover:opacity-100"
          aria-label="Remove attachment"
          title="Remove file"
          disabled={isUploading} // Explicitly disable if somehow still clickable
        >
          <X className="h-4 w-4" />
        </button>
      )}
    </div>
  );
};
