"use client";

import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useJiraTicket } from "@/hooks/use-jira";
import { CircleDashed, ExternalLink, Tag } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

interface JiraTicketMentionProps {
  ticketKey: string;
  baseUrl?: string; // Optional Jira base URL for linking
}

// Status colors
const statusColors: Record<string, string> = {
  "To Do": "bg-gray-200 text-gray-800",
  "In Progress": "bg-blue-200 text-blue-800",
  Done: "bg-green-200 text-green-800",
  Closed: "bg-purple-200 text-purple-800",
  Canceled: "bg-red-200 text-red-800",
  Blocked: "bg-yellow-200 text-yellow-800",
  default: "bg-gray-200 text-gray-800",
};

export function JiraTicketMention({ ticketKey, baseUrl }: JiraTicketMentionProps) {
  const [error, setError] = useState<string | null>(null);

  // Use the useJiraTicket hook instead of direct fetch
  const { ticket, isLoading } = useJiraTicket({ ticketKey });

  // Build the Jira URL for the ticket
  const getTicketUrl = () => {
    if (!baseUrl || !ticketKey) return null;
    return `${baseUrl}/browse/${ticketKey}`;
  };

  if (isLoading) {
    return (
      <span className="inline-flex items-center gap-1 px-2 py-1 bg-gray-100 rounded-md text-sm">
        <CircleDashed className="animate-spin h-3 w-3" />
        <span>{ticketKey}</span>
      </span>
    );
  }

  if (error || !ticket) {
    return (
      <span className="inline-flex items-center gap-1 px-2 py-1 bg-red-50 text-red-800 rounded-md text-sm">
        <Tag className="h-3 w-3" />
        <span>{ticketKey}</span>
      </span>
    );
  }

  // Get status color class based on status name
  const statusColorClass = ticket.status?.name ? statusColors[ticket.status.name] || statusColors.default : statusColors.default;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <span className="inline-flex items-center gap-1 px-2 py-1 bg-blue-50 border border-blue-100 rounded-md text-sm hover:bg-blue-100 transition-colors">
            <Tag className="h-3 w-3 text-blue-500" />
            <span className="font-medium">{ticketKey}</span>
            {ticket.status && (
              <Badge
                variant="outline"
                className={`text-xs px-1 py-0 h-4 ${statusColorClass}`}
              >
                {ticket.status.name}
              </Badge>
            )}
            {getTicketUrl() && (
              <Link
                href={getTicketUrl()!}
                target="_blank"
                rel="noopener noreferrer"
              >
                <ExternalLink className="h-3 w-3 text-blue-500" />
              </Link>
            )}
          </span>
        </TooltipTrigger>
        <TooltipContent className="w-80 p-0">
          <div className="p-3">
            <div className="flex justify-between items-start mb-2">
              <h3 className="font-medium text-sm">{ticket.title}</h3>
              {ticket.priority && <span className="text-xs">{ticket.priority.name} Priority</span>}
            </div>

            <div className="flex flex-wrap gap-2 mb-2">
              {ticket.status && (
                <Badge
                  variant="outline"
                  className={statusColorClass}
                >
                  {ticket.status.name}
                </Badge>
              )}
              {ticket.project && <Badge variant="outline">{ticket.project.key}</Badge>}
            </div>

            {ticket.description && <p className="text-xs text-muted-foreground line-clamp-3 mb-2">{ticket.description.replace(/<[^>]*>?/gm, "")}</p>}

            <div className="text-xs text-muted-foreground">
              {ticket.creator && <div>Created by: {ticket.creator.displayName}</div>}
              {ticket.updated && <div>Updated: {new Date(ticket.updated).toLocaleDateString()}</div>}
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
