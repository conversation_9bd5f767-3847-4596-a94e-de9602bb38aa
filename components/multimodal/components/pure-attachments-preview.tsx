import type { ExtendedAttachment } from "@/components/multimodal/types";
import { PreviewAttachment } from "@/components/preview-attachment";
import { type Dispatch, type SetStateAction, memo } from "react";

export const AttachmentsPreview = ({
  attachments,
  setAttachments,
}: // uploadQueue, // uploadQueue is received but not currently used for individual item status. Could be used for a general queue count.
{
  attachments: Array<ExtendedAttachment>;
  setAttachments: Dispatch<SetStateAction<Array<ExtendedAttachment>>>;
  uploadQueue: string[]; // Prop is kept in types; remove if definitely not needed for other UI elements.
}) => {
  // Filter out mentioned items if you only want to show file uploads for preview
  // Or, adjust if mentions should also appear in this specific preview list
  const attachmentsToPreview = attachments.filter((att) => att.attachmentType === "file" && !att.fromMention);

  if (attachmentsToPreview.length === 0) {
    return null;
  }

  return (
    <div className="flex flex-row gap-2 overflow-x-auto py-2">
      {attachmentsToPreview.map((attachment) => (
        <PreviewAttachment
          key={attachment.id || attachment.url}
          attachment={attachment}
          onRemove={() => {
            if (attachment.id) {
              setAttachments((currentAttachments) => currentAttachments.filter((att) => att.id !== attachment.id));
            } else {
              // Fallback for safety, though uploaded files should have an id
              console.warn("Attempted to remove attachment without an ID", attachment);
              setAttachments((currentAttachments) => currentAttachments.filter((att) => att !== attachment));
            }
          }}
        />
      ))}
    </div>
  );
};

export const AttachmentsPreviewMemo = memo(AttachmentsPreview);
