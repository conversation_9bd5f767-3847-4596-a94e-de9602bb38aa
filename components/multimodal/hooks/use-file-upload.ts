import { useState, useCallback } from "react";
import { toast } from "sonner";
import type { ExtendedAttachment } from "@/components/multimodal/types";
import { v4 as uuidv4 } from "uuid"; // For unique local IDs

export const useFileUpload = (setAttachments: (fn: (prev: ExtendedAttachment[]) => ExtendedAttachment[]) => void) => {
  const [uploadQueue, setUploadQueue] = useState<Array<string>>([]);

  // Renamed to reflect it's an internal helper that also updates state
  const uploadFileAndUpdateState = async (file: File, localId: string) => {
    const formData = new FormData();
    formData.append("file", file);
    try {
      const response = await fetch("/api/files/upload", {
        method: "POST",
        body: formData,
      });

      if (response.ok) {
        const data = await response.json(); // { url, name, type, size } from API
        setAttachments((prev) =>
          prev.map((att) =>
            att.id === localId
              ? {
                  ...att,
                  url: data.url, // Final URL from Supabase
                  // name: data.name, // Keep local name for consistency until successful POST to AI
                  // contentType: data.type, // Keep local type
                  // size: data.size, // Keep local size
                  uploadState: "uploaded",
                  file: undefined, // Clear the file object after upload
                }
              : att
          )
        );
        toast.success(`Uploaded ${file.name}`); // Use original file.name for toast
      } else {
        const errorData = await response.json().catch(() => ({}));
        toast.error(errorData.error || `Failed to upload ${file.name}`);
        setAttachments((prev) => prev.map((att) => (att.id === localId ? { ...att, uploadState: "failed", file: undefined } : att)));
      }
    } catch (error) {
      toast.error(`Error uploading ${file.name}: ${(error as Error).message}`);
      setAttachments((prev) => prev.map((att) => (att.id === localId ? { ...att, uploadState: "failed", file: undefined } : att)));
    } finally {
      setUploadQueue((prev) => prev.filter((id) => id !== localId));
    }
  };

  const handleFileChange = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(event.target.files || []);
      if (files.length === 0) return;

      const newLocalAttachments: ExtendedAttachment[] = [];
      const newUploadQueueIds: string[] = [];

      files.forEach((file) => {
        const localId = uuidv4();
        newUploadQueueIds.push(localId);
        const localAttachment: ExtendedAttachment = {
          id: localId,
          file: file, // Keep the file object for upload
          name: file.name,
          contentType: file.type,
          size: file.size,
          url: URL.createObjectURL(file), // Temporary local URL for preview
          attachmentType: "file" as const,
          fromMention: false,
          uploadState: "uploading", // Set initial state to uploading
        };
        newLocalAttachments.push(localAttachment);
      });

      // Immediately update UI with placeholders in "uploading" state
      setAttachments((prev) => [...prev, ...newLocalAttachments]);
      setUploadQueue((prev) => [...prev, ...newUploadQueueIds]);

      // Start actual uploads
      newLocalAttachments.forEach((att) => {
        if (att.file && att.id) {
          uploadFileAndUpdateState(att.file, att.id);
        }
      });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [setAttachments] // uploadFileAndUpdateState is stable as it's defined in the hook
  );

  return { uploadQueue, handleFileChange };
};
