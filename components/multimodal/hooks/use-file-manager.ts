"use client";

import { fetcher } from "@/lib/utils";
import { applyDocumentSearchFilter } from "@/lib/cache";
import { useMemo, useState, useCallback, useEffect } from "react";
import useSWR from "swr";
import { useDebounce } from "@/lib/hooks/use-debounce";

export interface FileMetadata {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  department: string;
  fileType: string;
  owner: string;
  path: string;
  permissions: string[];
  url: string;
  source: "company" | "personal";
  size?: number;
  isAiGenerated?: boolean;
}

export type SortField = "name" | "fileType" | "updatedAt";
export type SortOrder = "asc" | "desc";

export interface FileFilter {
  search: string;
  type?: string;
}

export interface FileResponse {
  files: FileMetadata[];
  count: number;
  source: "company" | "personal";
}

export type ActiveFileManagerTab = "company" | "personal" | "memory";

// Cache key for SWR
const getCacheKey = (tab: ActiveFileManagerTab) => `/api/files/${tab}`;

export function useFileManager(shouldFetch = true) {
  const [activeTab, setActiveTab] = useState<ActiveFileManagerTab>("company");
  const [sortField, setSortField] = useState<SortField>("updatedAt");
  const [sortOrder, setSortOrder] = useState<SortOrder>("desc");
  const [searchTerm, setSearchTerm] = useState("");
  const debouncedSearchTerm = useDebounce(searchTerm, 1000); // 1 second debounce
  const [filter, setFilter] = useState<FileFilter>({ search: "" });

  // Update filter when debounced search term changes
  useEffect(() => {
    setFilter((prev) => ({ ...prev, search: debouncedSearchTerm }));
  }, [debouncedSearchTerm]);

  // Expose setActiveTab function globally so it can be called from document badge
  useEffect(() => {
    if (typeof window !== "undefined") {
      (window as any).__setFileManagerTab = setActiveTab;
    }
    return () => {
      if (typeof window !== "undefined") {
        (window as any).__setFileManagerTab = undefined;
      }
    };
  }, []);

  // Only fetch data for the active tab when shouldFetch is true
  const {
    data: companyFiles,
    isLoading: isCompanyFilesLoading,
    error: companyFilesError,
    mutate: mutateCompanyFiles,
  } = useSWR<FileResponse>(
    shouldFetch && activeTab === "company" ? getCacheKey("company") : null,
    fetcher,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      dedupingInterval: 5000, // Dedupe requests within 5 seconds
    }
  );

  const {
    data: personalFilesData,
    isLoading: isPersonalFilesLoading,
    error: personalFilesError,
    mutate: mutatePersonalFiles,
  } = useSWR<FileResponse>(
    shouldFetch && activeTab === "personal" ? getCacheKey("personal") : null,
    fetcher,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      dedupingInterval: 5000,
    }
  );

  const files = useMemo(() => {
    const rawFiles =
      activeTab === "company"
        ? companyFiles?.files || []
        : personalFilesData?.files || [];

    if (!Array.isArray(rawFiles)) {
      console.warn("rawFiles is not an array:", rawFiles);
      return [];
    }

    let filteredFiles = filter.search
      ? applyDocumentSearchFilter(rawFiles, filter.search)
      : rawFiles;

    if (filter.type) {
      filteredFiles = filteredFiles.filter(
        (file) => file.fileType === filter.type
      );
    }

    return filteredFiles;
  }, [activeTab, companyFiles, personalFilesData, filter]);

  const currentIsLoading =
    activeTab === "company" ? isCompanyFilesLoading : isPersonalFilesLoading;
  const currentError =
    activeTab === "company" ? companyFilesError : personalFilesError;

  const mutateFiles = useCallback(() => {
    if (activeTab === "company") {
      mutateCompanyFiles();
    } else {
      mutatePersonalFiles();
    }
  }, [activeTab, mutateCompanyFiles, mutatePersonalFiles]);

  const mutateAllFiles = useCallback(() => {
    mutateCompanyFiles();
    mutatePersonalFiles();
  }, [mutateCompanyFiles, mutatePersonalFiles]);

  return {
    files,
    isLoading: currentIsLoading,
    error: currentError,
    activeTab,
    setActiveTab,
    sortField,
    setSortField,
    sortOrder,
    setSortOrder,
    filter,
    setFilter,
    searchTerm,
    setSearchTerm,
    mutateFiles,
    mutateAllFiles,
    companyFiles,
    personalFiles: personalFilesData,
  };
}
