import { useCallback, useEffect, useState } from "react";

export const useDragAndDrop = (processFiles: (files: File[]) => Promise<void>) => {
  const [isDraggingOver, setIsDraggingOver] = useState(false);

  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    setIsDraggingOver(true);
  }, []);

  const handleDragLeave = useCallback(() => {
    setIsDraggingOver(false);
  }, []);

  const handleDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();
      setIsDraggingOver(false);

      const files = Array.from(event.dataTransfer.files || []);
      processFiles(files);
    },
    [processFiles]
  );

  // Set up global drag and drop handlers
  useEffect(() => {
    const handleWindowDragOver = (event: DragEvent) => {
      event.preventDefault();
      if (event.dataTransfer) {
        event.dataTransfer.dropEffect = "copy";
      }
      setIsDraggingOver(true);
    };

    const handleWindowDragLeave = (event: DragEvent) => {
      event.preventDefault();
      // Only set to false if leaving the window
      if (event.clientY <= 0 || event.clientX <= 0 || event.clientX >= window.innerWidth || event.clientY >= window.innerHeight) {
        setIsDraggingOver(false);
      }
    };

    const handleWindowDrop = (event: DragEvent) => {
      event.preventDefault();
      setIsDraggingOver(false);

      if (event.dataTransfer?.files) {
        const files = Array.from(event.dataTransfer.files);
        processFiles(files);
      }
    };

    window.addEventListener("dragover", handleWindowDragOver);
    window.addEventListener("dragleave", handleWindowDragLeave);
    window.addEventListener("drop", handleWindowDrop);

    return () => {
      window.removeEventListener("dragover", handleWindowDragOver);
      window.removeEventListener("dragleave", handleWindowDragLeave);
      window.removeEventListener("drop", handleWindowDrop);
    };
  }, [processFiles]);

  return { isDraggingOver, handleDragOver, handleDragLeave, handleDrop };
};
