import type { FileMetadata } from "../hooks/use-file-manager";

export interface ExtendedFileMetadata extends FileMetadata {
  visibility: "private" | "company";
}

export const mockPersonalFiles: ExtendedFileMetadata[] = [
  {
    id: "pf-001",
    name: "Resume_2024.pdf",
    createdAt: "2024-01-15T10:30:00Z",
    updatedAt: "2024-01-15T10:30:00Z",
    department: "personal",
    fileType: "pdf",
    owner: "user",
    path: "/personal/documents/Resume_2024.pdf",
    permissions: ["read", "write", "delete"],
    url: "https://example.com/files/personal/Resume_2024.pdf",
    source: "personal",
    size: 1024000, // 1MB
    isAiGenerated: false,
    visibility: "private",
  },
  {
    id: "pf-002",
    name: "Project_Notes.md",
    createdAt: "2024-01-20T14:22:00Z",
    updatedAt: "2024-01-22T16:45:00Z",
    department: "development",
    fileType: "md",
    owner: "user",
    path: "/personal/notes/Project_Notes.md",
    permissions: ["read", "write", "delete"],
    url: "https://example.com/files/personal/Project_Notes.md",
    source: "personal",
    size: 15360, // 15KB
    isAiGenerated: false,
    visibility: "company",
  },
  {
    id: "pf-003",
    name: "AI_Generated_Report.docx",
    createdAt: "2024-01-25T09:15:00Z",
    updatedAt: "2024-01-25T09:15:00Z",
    department: "research",
    fileType: "docx",
    owner: "user",
    path: "/personal/reports/AI_Generated_Report.docx",
    permissions: ["read", "write", "delete"],
    url: "https://example.com/files/personal/AI_Generated_Report.docx",
    source: "personal",
    size: 512000, // 512KB
    isAiGenerated: true,
    visibility: "company",
  },
  {
    id: "pf-004",
    name: "Meeting_Recording_Jan_28.mp3",
    createdAt: "2024-01-28T11:00:00Z",
    updatedAt: "2024-01-28T11:00:00Z",
    department: "meetings",
    fileType: "mp3",
    owner: "user",
    path: "/personal/audio/Meeting_Recording_Jan_28.mp3",
    permissions: ["read", "write", "delete"],
    url: "https://example.com/files/personal/Meeting_Recording_Jan_28.mp3",
    source: "personal",
    size: 5242880, // 5MB
    isAiGenerated: false,
    visibility: "private",
  },
  {
    id: "pf-005",
    name: "Budget_Spreadsheet.xlsx",
    createdAt: "2024-01-12T08:45:00Z",
    updatedAt: "2024-01-30T17:20:00Z",
    department: "finance",
    fileType: "xlsx",
    owner: "user",
    path: "/personal/spreadsheets/Budget_Spreadsheet.xlsx",
    permissions: ["read", "write", "delete"],
    url: "https://example.com/files/personal/Budget_Spreadsheet.xlsx",
    source: "personal",
    size: 204800, // 200KB
    isAiGenerated: false,
    visibility: "private",
  },
  {
    id: "pf-006",
    name: "Vacation_Photos.zip",
    createdAt: "2024-01-05T19:30:00Z",
    updatedAt: "2024-01-05T19:30:00Z",
    department: "personal",
    fileType: "zip",
    owner: "user",
    path: "/personal/archives/Vacation_Photos.zip",
    permissions: ["read", "write", "delete"],
    url: "https://example.com/files/personal/Vacation_Photos.zip",
    source: "personal",
    size: 15728640, // 15MB
    isAiGenerated: false,
    visibility: "private",
  },
  {
    id: "pf-007",
    name: "Code_Snippets.txt",
    createdAt: "2024-01-18T13:10:00Z",
    updatedAt: "2024-01-31T10:15:00Z",
    department: "development",
    fileType: "txt",
    owner: "user",
    path: "/personal/code/Code_Snippets.txt",
    permissions: ["read", "write", "delete"],
    url: "https://example.com/files/personal/Code_Snippets.txt",
    source: "personal",
    size: 8192, // 8KB
    isAiGenerated: false,
    visibility: "company",
  },
  {
    id: "pf-008",
    name: "AI_Summary_Weekly_Report.pdf",
    createdAt: "2024-02-01T08:00:00Z",
    updatedAt: "2024-02-01T08:00:00Z",
    department: "reports",
    fileType: "pdf",
    owner: "user",
    path: "/personal/ai-generated/AI_Summary_Weekly_Report.pdf",
    permissions: ["read", "write", "delete"],
    url: "https://example.com/files/personal/AI_Summary_Weekly_Report.pdf",
    source: "personal",
    size: 768000, // 768KB
    isAiGenerated: true,
    visibility: "company",
  },
  {
    id: "pf-009",
    name: "Learning_Presentation.pptx",
    createdAt: "2024-01-08T16:45:00Z",
    updatedAt: "2024-01-29T14:30:00Z",
    department: "education",
    fileType: "pptx",
    owner: "user",
    path: "/personal/presentations/Learning_Presentation.pptx",
    permissions: ["read", "write", "delete"],
    url: "https://example.com/files/personal/Learning_Presentation.pptx",
    source: "personal",
    size: 2097152, // 2MB
    isAiGenerated: false,
    visibility: "private",
  },
  {
    id: "pf-010",
    name: "Quick_Ideas.json",
    createdAt: "2024-01-22T12:00:00Z",
    updatedAt: "2024-02-02T09:22:00Z",
    department: "brainstorming",
    fileType: "json",
    owner: "user",
    path: "/personal/data/Quick_Ideas.json",
    permissions: ["read", "write", "delete"],
    url: "https://example.com/files/personal/Quick_Ideas.json",
    source: "personal",
    size: 4096, // 4KB
    isAiGenerated: false,
    visibility: "private",
  },
];

// Helper function to get mock files with optional filtering
export function getMockPersonalFiles(options?: { search?: string; department?: string; limit?: number }): ExtendedFileMetadata[] {
  // let files = [];

  // if (options?.search) {
  //   const searchTerm = options.search.toLowerCase();
  //   files = files?.filter(
  //     (file) =>
  //       file.name.toLowerCase().includes(searchTerm) ||
  //       file.department.toLowerCase().includes(searchTerm) ||
  //       file.fileType.toLowerCase().includes(searchTerm)
  //   );
  // }

  // if (options?.department) {
  //   files = files.filter((file) => file.department === options.department);
  // }

  // if (options?.limit) {
  //   files = files.slice(0, options.limit);
  // }

  return [];
}

// Helper function to simulate API response format
export function getMockPersonalFilesResponse(options?: { search?: string; department?: string; limit?: number }): { files: ExtendedFileMetadata[] } {
  return {
    files: getMockPersonalFiles(options),
  };
}
