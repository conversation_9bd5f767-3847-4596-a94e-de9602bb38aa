# Mock Data for Personal Files

This directory contains mock data and utilities for testing the File Manager component, specifically the personal files tab.

## Files

### `personal-files.ts`

Contains realistic mock data for personal files with the following features:

- **10 mock files** with various file types (PDF, DOCX, XLSX, MP3, TXT, etc.)
- **Realistic departments** (personal, development, finance, meetings, etc.)
- **Mixed AI-generated and user-created files**
- **Proper date formatting** with ISO timestamps
- **Realistic file sizes** and metadata
- **Search functionality** built-in

## Mock Data Structure

Each mock file includes:

```typescript
{
  id: string;           // Unique identifier
  name: string;         // File name with extension
  createdAt: string;    // ISO timestamp
  updatedAt: string;    // ISO timestamp
  department: string;   // Department/category
  fileType: string;     // File extension
  owner: string;        // File owner
  path: string;         // File path
  permissions: string[]; // Access permissions
  url: string;          // Download URL
  source: "personal";   // Always "personal" for this mock
  size?: number;        // File size in bytes
  isAiGenerated?: boolean; // Whether file was AI-generated
}
```

## Usage

### Basic Usage

```typescript
import { mockPersonalFiles } from "./mocks/personal-files";

// Use all mock files
const files = mockPersonalFiles;
```

### Filtered Usage

```typescript
import { getMockPersonalFiles } from "./mocks/personal-files";

// Search files
const searchResults = getMockPersonalFiles({
  search: "report",
});

// Filter by department
const devFiles = getMockPersonalFiles({
  department: "development",
});

// Limit results
const recentFiles = getMockPersonalFiles({
  limit: 5,
});

// Combined filters
const filteredFiles = getMockPersonalFiles({
  search: "pdf",
  department: "reports",
  limit: 3,
});
```

### API Response Format

```typescript
import { getMockPersonalFilesResponse } from "./mocks/personal-files";

// Simulate API response
const apiResponse = getMockPersonalFilesResponse({
  search: "budget",
});
// Returns: { files: FileMetadata[] }
```

## Development Component

Use `FileManagerModalDev` instead of `FileManagerModal` to see the mock data in action:

```typescript
import { FileManagerModalDev } from "./components/file-manager-modal-dev";

// This component uses mock data for personal files
// while keeping real data for company files and memories
```

## Features of Mock Data

1. **Realistic File Types**: PDF, DOCX, XLSX, MP3, TXT, JSON, ZIP, PPTX, MD
2. **Varied Departments**: personal, development, finance, meetings, research, education, reports, brainstorming
3. **AI-Generated Files**: Some files marked as AI-generated with `isAiGenerated: true`
4. **Recent Dates**: All files created/updated in January-February 2024
5. **Realistic Sizes**: From 4KB text files to 15MB archives
6. **Search Support**: Search works across name, department, and file type

## Testing Scenarios

The mock data supports testing:

- **Search functionality** (try searching for "report", "pdf", "development")
- **Delete operations** (with confirmation dialogs)
- **File type filtering**
- **Department-based grouping**
- **AI-generated file badges**
- **Date formatting and sorting**
- **Empty states** (filter to get no results)

## Customization

To add more mock files or modify existing ones:

1. Add new entries to the `mockPersonalFiles` array
2. Follow the existing structure and naming conventions
3. Ensure `source: "personal"` for all entries
4. Use realistic file names and extensions
5. Include proper ISO timestamps

## Production Notes

- This mock data is for **development and testing only**
- Remove references to mock data before production deployment
- The development modal (`FileManagerModalDev`) should not be used in production
- Real API endpoints should be used for actual file operations
