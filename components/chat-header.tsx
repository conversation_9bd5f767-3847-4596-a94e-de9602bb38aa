"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { useModalsStore } from "@/lib/store/modals";
import { useUser } from "@clerk/nextjs";
import { Brain, ListRestart, <PERSON>Pen, Wrench } from "lucide-react";
import { memo, useEffect } from "react";
import { Tooltip, TooltipContent, TooltipTrigger } from "./ui/tooltip";
import type { VisibilityType } from "./visibility-selector";

function PureChatHeader({}: { chatId: string; selectedModelId: string; selectedVisibilityType: VisibilityType; isReadonly: boolean }) {
  const { isSignedIn } = useUser();
  const { toggleHistory, toggleFileManager, toggleTools } = useModalsStore();

  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === "k" && (e.metaKey || e.ctrl<PERSON><PERSON>)) {
        e.preventDefault();
        toggleHistory();
      }
    };

    document.addEventListener("keydown", down);
    return () => document.removeEventListener("keydown", down);
  }, [toggleHistory]);

  const isNewChat = typeof window !== "undefined" && /^\/chat\/[a-zA-Z0-9-]+$/.test(window.location.pathname);

  return (
    <header className="flex sticky top-0 bg-background py-1.5 items-center px-2 md:px-2 gap-2 justify-between w-full">
      <div className="flex-1 flex px-4">
        <SidebarTrigger className="-ml-1" />
      </div>
      <div className="flex items-center gap-2 pr-1">
        {isSignedIn && (
          <>
            {isNewChat && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    onClick={() => {
                      window.location.assign("/");
                    }}
                  >
                    <SquarePen className="size-4.5" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>New Chat</TooltipContent>
              </Tooltip>
            )}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={toggleHistory}
                >
                  <ListRestart className="size-5" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>History (⌘K)</TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={toggleTools}
                >
                  <Wrench size={20} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Tools</TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  onClick={toggleFileManager}
                >
                  <Brain className="size-4.5" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Memory</TooltipContent>
            </Tooltip>
          </>
        )}
      </div>
    </header>
  );
}

export const ChatHeader = memo(PureChatHeader, (prevProps, nextProps) => {
  return prevProps.chatId === nextProps.chatId;
});
