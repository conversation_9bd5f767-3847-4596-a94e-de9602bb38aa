import { Workflow } from "lucide-react";
import { ExternalLink } from "lucide-react";

interface N8nWorkflow {
  id: string;
  name: string;
  description: string;
  tags: string[];
  templateUrl: string;
}

interface N8nWorkflowResult {
  workflows: N8nWorkflow[];
  message: string;
}

export const N8nWorkflowLoading = () => {
  return (
    <div className="flex items-center gap-2 text-sm italic text-muted-foreground">
      <span>Đang tìm kiếm workflow...</span>
    </div>
  );
};

export const N8nWorkflowResults = ({ result }: { result: N8nWorkflowResult }) => {
  return (
    <div className="border rounded-xl overflow-hidden">
      <div className="bg-muted px-4 py-2.5 font-medium flex items-center gap-1">
        <Workflow size={16} />
        Workflows có thể tham khảo:
      </div>
      <div className="p-3 flex flex-col gap-3">
        {result.workflows.map((workflow) => (
          <a
            href={workflow.templateUrl}
            target="_blank"
            rel="noopener noreferrer"
            key={workflow.id}
            className="border rounded-md p-3 hover:bg-muted/50 transition-colors cursor-pointer"
          >
            <div className="font-medium flex items-center gap-1">
              {workflow.name}
              <ExternalLink />
            </div>
            <div className="text-sm text-muted-foreground">{workflow.description}</div>
            <div className="mt-2 flex flex-wrap gap-1">
              {workflow.tags.map((tag) => (
                <span
                  key={tag}
                  className="bg-primary/10 text-primary text-xs px-2 py-0.5 rounded-full"
                >
                  {tag}
                </span>
              ))}
            </div>
          </a>
        ))}
      </div>
    </div>
  );
};
