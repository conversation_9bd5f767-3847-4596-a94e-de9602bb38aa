"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Switch } from "@/components/ui/switch";
import { TOOL_IDS, type ToolId } from "@/lib/constants";
import type { McpConfig as DbMcpConfig } from "@/lib/db/schema";
import { useModalsStore } from "@/lib/store/modals";
import { cn } from "@/lib/utils";
import { ChevronDown, ChevronRight, Database, ExternalLink, FileText, Grid3X3, Hammer, PlusCircle, Search } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import useSWR from "swr";

// Temporary client-side tool display info (until we create proper API)
interface ClientToolDisplayInfo {
  id: ToolId;
  name: string;
  description: string;
  icon: React.ElementType;
}

interface McpConfigWithTyped<PERSON>son extends Omit<DbMcpConfig, "config"> {
  config: {
    transport: {
      type: "sse" | "stdio";
      url?: string;
      headers?: Record<string, string>;
      command?: string;
      args?: string[];
    };
  };
  isStatic?: boolean;
  tools?: Array<{
    name: string;
    description?: string;
  }>;
}

const CLIENT_DISPLAY_TOOLS_INFO: ClientToolDisplayInfo[] = [
  {
    id: TOOL_IDS.KNOWLEDGE_BASE,
    name: "Knowledge Base",
    description: "Search internal knowledge base for answers",
    icon: Search,
  },
  {
    id: TOOL_IDS.N8N_WORKFLOW,
    name: "n8n Workflow",
    description: "Execute automation workflows",
    icon: ExternalLink,
  },
  {
    id: TOOL_IDS.MEMORY,
    name: "Memory",
    description: "Save and recall information",
    icon: Database,
  },
  {
    id: TOOL_IDS.LIST_DOCUMENTS,
    name: "List Documents",
    description: "Browse available documents",
    icon: FileText,
  },
  {
    id: TOOL_IDS.GOOGLE_SHEETS,
    name: "Google Sheets",
    description: "Read and write spreadsheet data",
    icon: Grid3X3,
  },
  {
    id: TOOL_IDS.JIRA,
    name: "JIRA",
    description: "Manage project tasks and issues",
    icon: PlusCircle,
  },
];

const fetcher = (url: string) => {
  console.log(`[ToolsDialog] Fetching data from: ${url}`);
  return fetch(url).then((res) => res.json());
};

export function ToolsDialog() {
  const toolsInfo = CLIENT_DISPLAY_TOOLS_INFO;
  const { data: mcps, mutate } = useSWR<McpConfigWithTypedJson[]>("/api/mcp", fetcher);
  const [expandedMcps, setExpandedMcps] = useState<Set<string>>(new Set());

  // TODO: Create API endpoint to get available tools dynamically
  // For now, show all tools as available
  const availableToolIds = Object.values(TOOL_IDS);
  const { toolsOpen, setToolsOpen } = useModalsStore();

  const isToolEnabled = (tool: ClientToolDisplayInfo) => {
    return availableToolIds.includes(tool.id);
  };

  // const handleDelete = async (id: string) => {
  //   console.log(`[ToolsDialog] Deleting MCP with id: ${id}`);
  //   try {
  //     const res = await fetch(`/api/mcp?id=${id}`, { method: "DELETE" });
  //     if (!res.ok) throw new Error(`Failed with status ${res.status}`);
  //     toast.success("Deleted");
  //     mutate();
  //   } catch (err) {
  //     console.error("[ToolsDialog] Failed to delete:", err);
  //     toast.error("Failed to delete: " + (err as Error).message);
  //   }
  // };

  const handleActivate = async (id: string) => {
    console.log(`[ToolsDialog] Activating MCP with id: ${id}`);
    try {
      const res = await fetch("/api/mcp", {
        method: "PATCH", // Use PATCH to set active, not PUT to toggle
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ id }),
      });
      if (!res.ok) throw new Error(`Failed with status ${res.status}`);
      mutate();
    } catch (err) {
      console.error("[ToolsDialog] Failed to activate:", err);
      toast.error(`Failed to activate: ${(err as Error).message}`);
    }
  };

  const toggleMcpExpansion = (mcpId: string) => {
    setExpandedMcps((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(mcpId)) {
        newSet.delete(mcpId);
      } else {
        newSet.add(mcpId);
      }
      return newSet;
    });
  };

  return (
    <Dialog
      open={toolsOpen}
      onOpenChange={setToolsOpen}
    >
      <DialogContent className="sm:max-w-xl">
        <DialogHeader>
          <DialogTitle>Available Tools</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col gap-2 w-full max-w-full overflow-x-hidden overflow-y-auto max-h-[50dvh]">
          {/* Built-in tools */}
          {toolsInfo.map((tool) => {
            const enabled = isToolEnabled(tool);
            return (
              <div
                key={tool.id}
                className={cn("flex items-center gap-3 p-3 rounded-lg border", enabled ? "bg-muted/50" : "opacity-50 cursor-not-allowed")}
              >
                <div className="p-2 rounded-md bg-muted">
                  <tool.icon size={18} />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-sm">{tool.name}</div>
                  <div className="text-xs text-muted-foreground truncate">{tool.description}</div>
                </div>
              </div>
            );
          })}

          {/* MCP tools */}
          {mcps?.map((mcp) => {
            const isExpanded = expandedMcps.has(mcp.id);
            const hasTools = mcp.tools && mcp.tools.length > 0;

            return (
              <div
                key={mcp.id}
                className="rounded-lg bg-muted/25"
              >
                <div className={cn("flex items-center gap-3 p-3 rounded-lg border", "bg-muted/25")}>
                  {/* Radio button for activation */}
                  {/* <input
                    type="radio"
                    name="activeMcp"
                    value={mcp.id}
                    checked={mcp.active}
                    onChange={() => handleActivate(mcp.id)}
                    disabled={mcp.isStatic}
                    className="w-4 h-4"
                  /> */}

                  {/* MCP icon */}
                  <div className="p-2 rounded-md bg-muted">
                    <Hammer size={18} />
                  </div>

                  {/* MCP info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-sm">{mcp.name}</span>
                      <Badge
                        variant="secondary"
                        className="text-xs"
                      >
                        MCP
                      </Badge>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {mcp.config?.transport?.type === "sse" ? "SSE" : "Stdio"} Transport
                      {hasTools && ` • ${mcp.tools?.length || 0} tools`}
                    </div>
                  </div>

                  {/* Action buttons */}
                  <div className="flex items-center gap-1">
                    {/* <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleDelete(mcp.id)}
                      disabled={mcp.isStatic}
                      className="h-8 w-8"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button> */}

                    {hasTools && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => toggleMcpExpansion(mcp.id)}
                        className="h-8 w-8"
                      >
                        {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                      </Button>
                    )}
                  </div>
                </div>

                {/* Collapsible tools section */}
                {hasTools && isExpanded && (
                  <div className="p-3">
                    <div className="space-y-2">
                      {mcp.tools?.map((tool) => (
                        <div
                          key={tool.name}
                          className="flex gap-2 px-4 py-2.5 rounded-lg border bg-muted/25 items-center"
                        >
                          <div className="flex-1 min-w-0">
                            <div className="text-sm font-medium">{tool.name}</div>
                            {tool.description && <div className="text-xs text-muted-foreground mt-1">{tool.description}</div>}
                          </div>
                          <Switch
                            checked={true}
                            onCheckedChange={() => handleActivate(mcp.id)}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </DialogContent>
    </Dialog>
  );
}
