import { type ExtendedAttachment, isTicketAttachment } from "@/components/multimodal/types";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { FileIcon, TicketIcon } from "lucide-react";
import Image from "next/image";
import { CrossIcon, LoaderIcon } from "./icons";
import { Button } from "./ui/button";
import { Badge } from "@/components/ui/badge";

export const PreviewAttachment = ({
  attachment,
  isUploading = false,
  onRemove,
}: {
  attachment: ExtendedAttachment;
  isUploading?: boolean;
  onRemove?: () => void;
}) => {
  const { name, url, contentType } = attachment;
  const isTicket = isTicketAttachment(attachment);
  const displayName = isTicket ? `${attachment.ticketData?.key}` : attachment.name || "File";

  return (
    <div
      data-testid="input-attachment-preview"
      className="flex flex-col gap-2 relative group"
    >
      {isTicket ? (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge
                variant="outline"
                className="rounded-sm cursor-default bg-muted text-sm"
              >
                <span>{isTicket ? <TicketIcon className="size-4" /> : <FileIcon className="size-4" />}</span>
                {displayName}
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <p>{isTicket ? attachment.ticketData?.title : (attachment as any).name}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      ) : (
        <>
          <div className="size-20 aspect-video bg-muted rounded-md relative flex flex-col items-center justify-center">
            {contentType ? (
              contentType.startsWith("image") ? (
                <Image
                  key={url}
                  src={url}
                  height={100}
                  width={100}
                  alt={name ?? "An image attachment"}
                  className="rounded-md size-full object-cover"
                />
              ) : (
                <div className="text-xs text-center px-1 overflow-hidden">{contentType.split("/")[1]?.toUpperCase() || "FILE"}</div>
              )
            ) : (
              <div className="text-xs text-center px-1 overflow-hidden">FILE</div>
            )}

            {isUploading && (
              <div
                data-testid="input-attachment-loader"
                className="animate-spin absolute text-zinc-500"
              >
                <LoaderIcon />
              </div>
            )}

            {onRemove && !isUploading && (
              <Button
                size="icon"
                variant="ghost"
                className="absolute -top-2 -right-2 size-5 rounded-full bg-background opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={(e) => {
                  e.preventDefault();
                  onRemove();
                }}
              >
                <CrossIcon size={12} />
              </Button>
            )}
          </div>
          <div className="text-xs text-zinc-500 max-w-16 truncate">{name}</div>
        </>
      )}
    </div>
  );
};
