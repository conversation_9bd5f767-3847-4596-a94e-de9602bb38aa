"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

interface QuickLinksWidgetProps {
  isLoaded: boolean;
}

export function QuickLinksWidget({ isLoaded }: QuickLinksWidgetProps) {
  const links = [
    {
      name: "Google Drive",
      icon: (
        <svg
          className="w-6 h-6"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M4.5 14L8.5 4H15.5L11.5 14H4.5Z"
            fill="#4285F4"
          />
          <path
            d="M11.5 14L15.5 4L19.5 14L15.5 24L11.5 14Z"
            fill="#0F9D58"
          />
          <path
            d="M4.5 14L8.5 24H15.5L19.5 14H4.5Z"
            fill="#FFCD40"
          />
        </svg>
      ),
      url: "https://drive.google.com",
    },
    {
      name: "<PERSON><PERSON>",
      icon: (
        <svg
          className="w-6 h-6"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M8 24C10.2091 24 12 22.2091 12 20V16H8C5.79086 16 4 17.7909 4 20C4 22.2091 5.79086 24 8 24Z"
            fill="#0ACF83"
          />
          <path
            d="M4 12C4 9.79086 5.79086 8 8 8H12V16H8C5.79086 16 4 14.2091 4 12Z"
            fill="#A259FF"
          />
          <path
            d="M4 4C4 1.79086 5.79086 0 8 0H12V8H8C5.79086 8 4 6.20914 4 4Z"
            fill="#F24E1E"
          />
          <path
            d="M12 0H16C18.2091 0 20 1.79086 20 4C20 6.20914 18.2091 8 16 8H12V0Z"
            fill="#FF7262"
          />
          <path
            d="M20 12C20 14.2091 18.2091 16 16 16C13.7909 16 12 14.2091 12 12C12 9.79086 13.7909 8 16 8C18.2091 8 20 9.79086 20 12Z"
            fill="#1ABCFE"
          />
        </svg>
      ),
      url: "https://figma.com",
    },
    {
      name: "GitHub",
      icon: (
        <svg
          className="w-6 h-6"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M12 0C5.37 0 0 5.37 0 12C0 17.31 3.435 21.795 8.205 23.385C8.805 23.49 9.03 23.13 9.03 22.815C9.03 22.53 9.015 21.585 9.015 20.58C6 21.135 5.22 19.845 4.98 19.17C4.845 18.825 4.26 17.76 3.75 17.475C3.33 17.25 2.73 16.695 3.735 16.68C4.68 16.665 5.355 17.55 5.58 17.91C6.66 19.725 8.385 19.215 9.075 18.9C9.18 18.12 9.495 17.595 9.84 17.295C7.17 16.995 4.38 15.96 4.38 11.37C4.38 10.065 4.845 8.985 5.61 8.145C5.49 7.845 5.07 6.615 5.73 4.965C5.73 4.965 6.735 4.65 9.03 6.195C9.99 5.925 11.01 5.79 12.03 5.79C13.05 5.79 14.07 5.925 15.03 6.195C17.325 4.635 18.33 4.965 18.33 4.965C18.99 6.615 18.57 7.845 18.45 8.145C19.215 8.985 19.68 10.05 19.68 11.37C19.68 15.975 16.875 16.995 14.205 17.295C14.64 17.67 15.015 18.39 15.015 19.515C15.015 21.12 15 22.41 15 22.815C15 23.13 15.225 23.505 15.825 23.385C18.2072 22.5807 20.2772 21.0497 21.7437 19.0074C23.2101 16.965 23.9993 14.5143 24 12C24 5.37 18.63 0 12 0Z"
            fill="white"
          />
        </svg>
      ),
      url: "https://github.com",
    },
    {
      name: "Slack",
      icon: (
        <svg
          className="w-6 h-6"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M9.07 17.28C9.07 18.33 8.22 19.18 7.17 19.18C6.12 19.18 5.27 18.33 5.27 17.28C5.27 16.23 6.12 15.38 7.17 15.38H9.07V17.28Z"
            fill="#E01E5A"
          />
          <path
            d="M10.02 17.28C10.02 16.23 10.87 15.38 11.92 15.38C12.97 15.38 13.82 16.23 13.82 17.28V22.03C13.82 23.08 12.97 23.93 11.92 23.93C10.87 23.93 10.02 23.08 10.02 22.03V17.28Z"
            fill="#E01E5A"
          />
          <path
            d="M11.92 9.07C10.87 9.07 10.02 8.22 10.02 7.17C10.02 6.12 10.87 5.27 11.92 5.27C12.97 5.27 13.82 6.12 13.82 7.17V9.07H11.92Z"
            fill="#36C5F0"
          />
          <path
            d="M11.92 10.02C12.97 10.02 13.82 10.87 13.82 11.92C13.82 12.97 12.97 13.82 11.92 13.82H7.17C6.12 13.82 5.27 12.97 5.27 11.92C5.27 10.87 6.12 10.02 7.17 10.02H11.92Z"
            fill="#36C5F0"
          />
          <path
            d="M20.13 11.92C20.13 10.87 20.98 10.02 22.03 10.02C23.08 10.02 23.93 10.87 23.93 11.92C23.93 12.97 23.08 13.82 22.03 13.82H20.13V11.92Z"
            fill="#2EB67D"
          />
          <path
            d="M19.18 11.92C19.18 12.97 18.33 13.82 17.28 13.82C16.23 13.82 15.38 12.97 15.38 11.92V7.17C15.38 6.12 16.23 5.27 17.28 5.27C18.33 5.27 19.18 6.12 19.18 7.17V11.92Z"
            fill="#2EB67D"
          />
          <path
            d="M17.28 20.13C18.33 20.13 19.18 20.98 19.18 22.03C19.18 23.08 18.33 23.93 17.28 23.93C16.23 23.93 15.38 23.08 15.38 22.03V20.13H17.28Z"
            fill="#ECB22E"
          />
          <path
            d="M17.28 19.18C16.23 19.18 15.38 18.33 15.38 17.28C15.38 16.23 16.23 15.38 17.28 15.38H22.03C23.08 15.38 23.93 16.23 23.93 17.28C23.93 18.33 23.08 19.18 22.03 19.18H17.28Z"
            fill="#ECB22E"
          />
        </svg>
      ),
      url: "https://slack.com",
    },
    {
      name: "Notion",
      icon: (
        <svg
          className="w-6 h-6"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M4.459 4.208C4.459 3.841 4.73 3.75 5.459 3.75H6.834C7.18 3.75 7.291 3.841 7.291 4.208V19.792C7.291 20.159 7.18 20.25 6.834 20.25H5.459C4.73 20.25 4.459 20.159 4.459 19.792V4.208Z"
            fill="white"
          />
          <path
            d="M8.709 4.208C8.709 3.841 8.979 3.75 9.709 3.75H11.084C11.429 3.75 11.541 3.841 11.541 4.208V19.792C11.541 20.159 11.429 20.25 11.084 20.25H9.709C8.979 20.25 8.709 20.159 8.709 19.792V4.208Z"
            fill="white"
          />
          <path
            d="M12.959 4.208C12.959 3.841 13.229 3.75 13.959 3.75H15.334C15.68 3.75 15.791 3.841 15.791 4.208V19.792C15.791 20.159 15.68 20.25 15.334 20.25H13.959C13.229 20.25 12.959 20.159 12.959 19.792V4.208Z"
            fill="white"
          />
          <path
            d="M17.209 4.208C17.209 3.841 17.479 3.75 18.209 3.75H19.584C19.929 3.75 20.041 3.841 20.041 4.208V19.792C20.041 20.159 19.929 20.25 19.584 20.25H18.209C17.479 20.25 17.209 20.159 17.209 19.792V4.208Z"
            fill="white"
          />
        </svg>
      ),
      url: "https://notion.so",
    },
    {
      name: "Gmail",
      icon: (
        <svg
          className="w-6 h-6"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M22.0001 5.25L12.0001 13.75L2.00006 5.25H22.0001Z"
            fill="#EA4335"
          />
          <path
            d="M2.00006 5.25V18.75H5.00006V9.5L12.0001 13.75L19.0001 9.5V18.75H22.0001V5.25L12.0001 13.75L2.00006 5.25Z"
            fill="white"
          />
        </svg>
      ),
      url: "https://mail.google.com",
    },
  ];

  return (
    <Card className="bg-zinc-800 border-zinc-700 text-zinc-100 transition-all duration-300 hover:bg-zinc-750">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-medium flex items-center">
          <svg
            className="w-5 h-5 mr-2"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M13.5 10.5L21 3"
              stroke="white"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M16 3H21V8"
              stroke="white"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M21 14V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H10"
              stroke="white"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          Quick Links
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoaded ? (
          <div className="grid grid-cols-3 gap-2">
            {links.map((link, index) => (
              <a
                key={link.name}
                href={link.url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex flex-col items-center justify-center p-3 bg-zinc-700 rounded-md border border-zinc-600 hover-scale transition-all duration-300 hover:border-zinc-500 hover:bg-zinc-650"
                style={{ animationDelay: `${index * 50}ms` }}
              >
                <div className="mb-2">{link.icon}</div>
                <span className="text-xs font-medium text-zinc-300">{link.name}</span>
              </a>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-3 gap-2">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div
                key={i}
                className="flex flex-col items-center justify-center p-3 bg-zinc-700 rounded-md border border-zinc-600 relative overflow-hidden"
              >
                <Skeleton className="h-6 w-6 mb-2 rounded-full bg-zinc-600" />
                <Skeleton className="h-3 w-12 bg-zinc-600" />
                <div className="absolute inset-0 animate-shimmer bg-gradient-to-r from-transparent via-zinc-600/10 to-transparent" />
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
