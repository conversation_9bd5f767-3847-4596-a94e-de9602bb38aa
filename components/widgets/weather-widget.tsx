"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

interface WeatherWidgetProps {
  isLoaded: boolean;
}

export function WeatherWidget({ isLoaded }: WeatherWidgetProps) {
  // Mock data - in a real app, this would come from an API
  const weatherData = {
    location: "Ho Chi Minh City",
    temperature: 32,
    condition: "Partly Cloudy",
    humidity: 75,
    windSpeed: 12,
    forecast: [
      { day: "Wed", temp: 33, condition: "Sunny" },
      { day: "Thu", temp: 32, condition: "Cloudy" },
      { day: "Fri", temp: 31, condition: "Rain" },
    ],
  };

  const getWeatherIcon = (condition: string) => {
    switch (condition.toLowerCase()) {
      case "sunny":
        return (
          <svg
            className="w-8 h-8 text-yellow-500"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <circle
              cx="12"
              cy="12"
              r="5"
            />
            <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42" />
          </svg>
        );
      case "cloudy":
        return (
          <svg
            className="w-8 h-8 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z"
            />
          </svg>
        );
      case "partly cloudy":
        return (
          <svg
            className="w-8 h-8"
            viewBox="0 0 24 24"
          >
            <path
              fill="#f59e0b"
              d="M12 3v1M12 20v1M5.3 5.3l.7.7M18 6l.7-.7M4 12H3M21 12h-1"
            />
            <path
              fill="#9ca3af"
              d="M16 18H6a4 4 0 110-8 4.9 4.9 0 017.9-3.8A5 5 0 1116 18z"
            />
          </svg>
        );
      case "rain":
        return (
          <svg
            className="w-8 h-8"
            viewBox="0 0 24 24"
          >
            <path
              fill="#9ca3af"
              d="M16 18H6a4 4 0 110-8 4.9 4.9 0 017.9-3.8A5 5 0 1116 18z"
            />
            <path
              fill="#3b82f6"
              d="M8 18v2M12 18v4M16 18v2"
            />
          </svg>
        );
      default:
        return (
          <svg
            className="w-8 h-8 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z"
            />
          </svg>
        );
    }
  };

  const getSmallWeatherIcon = (condition: string) => {
    switch (condition.toLowerCase()) {
      case "sunny":
        return (
          <svg
            className="w-5 h-5 text-yellow-500"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <circle
              cx="12"
              cy="12"
              r="5"
            />
            <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42" />
          </svg>
        );
      case "cloudy":
        return (
          <svg
            className="w-5 h-5 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z"
            />
          </svg>
        );
      case "rain":
        return (
          <svg
            className="w-5 h-5"
            viewBox="0 0 24 24"
          >
            <path
              fill="#9ca3af"
              d="M16 18H6a4 4 0 110-8 4.9 4.9 0 017.9-3.8A5 5 0 1116 18z"
            />
            <path
              fill="#3b82f6"
              d="M8 18v2M12 18v4M16 18v2"
            />
          </svg>
        );
      default:
        return (
          <svg
            className="w-5 h-5 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z"
            />
          </svg>
        );
    }
  };

  return (
    <Card className="transition-all duration-300 hover:shadow-md">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-medium flex items-center">
          <svg
            className="w-5 h-5 mr-2"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M3 15C3 17.2091 4.79086 19 7 19H16C18.7614 19 21 16.7614 21 14C21 11.2386 18.7614 9 16 9C15.9666 9 15.9334 9.00033 15.9002 9.00098C15.4373 6.71825 13.4193 5 11 5C8.23858 5 6 7.23858 6 10C6 10.3768 6.04169 10.7439 6.12071 11.097C4.32252 11.4976 3 13.0929 3 15Z"
              fill="#60A5FA"
            />
            <path
              d="M7 19H16C18.7614 19 21 16.7614 21 14C21 11.2386 18.7614 9 16 9C15.9666 9 15.9334 9.00033 15.9002 9.00098C15.4373 6.71825 13.4193 5 11 5C8.23858 5 6 7.23858 6 10C6 10.3768 6.04169 10.7439 6.12071 11.097C4.32252 11.4976 3 13.0929 3 15C3 17.2091 4.79086 19 7 19Z"
              stroke="#2563EB"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          Weather
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoaded ? (
          <div className="animate-fade-in">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="text-sm font-medium">{weatherData.location}</h3>
                <p className="text-xs text-gray-500">{weatherData.condition}</p>
              </div>
              <div className="transition-all duration-500 hover:rotate-12">{getWeatherIcon(weatherData.condition)}</div>
            </div>
            <div className="mt-2">
              <span className="text-3xl font-bold">{weatherData.temperature}°C</span>
            </div>
            <div className="mt-2 grid grid-cols-2 gap-2 text-xs text-gray-500">
              <div className="flex items-center">
                <svg
                  className="w-4 h-4 mr-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 14l-7 7m0 0l-7-7m7 7V3"
                  />
                </svg>
                Humidity: {weatherData.humidity}%
              </div>
              <div className="flex items-center">
                <svg
                  className="w-4 h-4 mr-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"
                  />
                </svg>
                Wind: {weatherData.windSpeed} km/h
              </div>
            </div>
            <div className="mt-4 border-t pt-2">
              <h4 className="text-xs font-medium mb-2">3-Day Forecast</h4>
              <div className="flex justify-between">
                {weatherData.forecast.map((day, index) => (
                  <div
                    key={index}
                    className="text-center transition-all duration-300 hover:scale-110"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <p className="text-xs font-medium">{day.day}</p>
                    <div className="my-1">{getSmallWeatherIcon(day.condition)}</div>
                    <p className="text-xs">{day.temp}°</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ) : (
          <div>
            <div className="flex justify-between items-center">
              <div>
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-3 w-16 mt-1" />
              </div>
              <Skeleton className="h-8 w-8 rounded-full" />
            </div>
            <Skeleton className="h-8 w-16 mt-2" />
            <div className="mt-2 grid grid-cols-2 gap-2">
              <Skeleton className="h-3 w-full" />
              <Skeleton className="h-3 w-full" />
            </div>
            <div className="mt-4 border-t pt-2">
              <Skeleton className="h-3 w-24 mb-2" />
              <div className="flex justify-between">
                {[1, 2, 3].map((i) => (
                  <div
                    key={i}
                    className="text-center"
                  >
                    <Skeleton className="h-3 w-8 mx-auto" />
                    <Skeleton className="h-5 w-5 mx-auto my-1 rounded-full" />
                    <Skeleton className="h-3 w-6 mx-auto" />
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
