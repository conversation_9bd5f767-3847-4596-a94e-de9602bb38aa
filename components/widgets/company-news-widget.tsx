"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

interface NewsItem {
  id: string;
  title: string;
  date: string;
  author: string;
  summary: string;
}

interface CompanyNewsWidgetProps {
  isLoaded: boolean;
}

export function CompanyNewsWidget({ isLoaded }: CompanyNewsWidgetProps) {
  // Mock data - in a real app, this would come from an API
  const [news] = useState<NewsItem[]>([
    {
      id: "1",
      title: "Q2 Company Goals Announced",
      date: "2025-05-10",
      author: "<PERSON>, CEO",
      summary: "Our focus for Q2 will be expanding market reach and improving customer satisfaction metrics.",
    },
    {
      id: "2",
      title: "New Office Opening in Singapore",
      date: "2025-05-08",
      author: "<PERSON>, <PERSON>",
      summary: "We're excited to announce our new APAC headquarters opening next month.",
    },
    {
      id: "3",
      title: "Product Team Achievements",
      date: "2025-05-05",
      author: "<PERSON>, <PERSON><PERSON>",
      summary: "The product team successfully launched 3 new features last month with positive user feedback.",
    },
  ]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString([], { month: "short", day: "numeric", year: "numeric" });
  };

  return (
    <Card className="bg-zinc-800 border-zinc-700 text-zinc-100 transition-all duration-300 hover:bg-zinc-750">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-medium flex items-center">
          <svg
            className="w-5 h-5 mr-2"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19 5V19H5V5H19ZM21 3H3V21H21V3ZM17 17H7V16H17V17ZM17 15H7V14H17V15ZM17 12H7V7H17V12Z"
              fill="white"
            />
          </svg>
          Company News
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoaded ? (
          <div className="space-y-2">
            {news.map((item, index) => (
              <div
                key={item.id}
                className="p-3 bg-zinc-700 rounded-md border border-zinc-600 hover-scale transition-all duration-300 hover:border-zinc-500"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="flex justify-between items-start">
                  <h3 className="text-sm font-medium text-zinc-100">{item.title}</h3>
                  <span className="text-xs text-zinc-400">{formatDate(item.date)}</span>
                </div>
                <div className="mt-1 text-xs text-zinc-400">{item.author}</div>
                <p className="mt-2 text-xs text-zinc-300 line-clamp-2">{item.summary}</p>
              </div>
            ))}
            <a
              href="#"
              className="block text-xs text-center mt-2 text-blue-400 hover:underline transition-all duration-300 hover:text-blue-300"
            >
              View all news →
            </a>
          </div>
        ) : (
          <div className="space-y-2">
            {[1, 2, 3].map((i) => (
              <div
                key={i}
                className="p-3 bg-zinc-700 rounded-md border border-zinc-600 relative overflow-hidden"
              >
                <div className="flex justify-between">
                  <Skeleton className="h-4 w-1/2 bg-zinc-600" />
                  <Skeleton className="h-3 w-16 bg-zinc-600" />
                </div>
                <Skeleton className="h-3 w-1/3 mt-1 bg-zinc-600" />
                <Skeleton className="h-3 w-full mt-2 bg-zinc-600" />
                <Skeleton className="h-3 w-full mt-1 bg-zinc-600" />
                <div className="absolute inset-0 animate-shimmer bg-gradient-to-r from-transparent via-zinc-600/10 to-transparent" />
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
