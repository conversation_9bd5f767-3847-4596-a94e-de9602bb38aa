"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

interface CalendarEvent {
  id: string;
  title: string;
  startTime: string;
  endTime: string;
  location?: string;
  attendees: number;
}

interface CalendarWidgetProps {
  isLoaded: boolean;
}

export function CalendarWidget({ isLoaded }: CalendarWidgetProps) {
  // Mock data - in a real app, this would come from an API
  const [events] = useState<CalendarEvent[]>([
    {
      id: "1",
      title: "Weekly Team Standup",
      startTime: "2025-05-13T09:00:00",
      endTime: "2025-05-13T09:30:00",
      location: "Meeting Room A",
      attendees: 8,
    },
    {
      id: "2",
      title: "Product Review",
      startTime: "2025-05-13T11:00:00",
      endTime: "2025-05-13T12:00:00",
      location: "Zoom",
      attendees: 5,
    },
    {
      id: "3",
      title: "Client Meeting - Acme Corp",
      startTime: "2025-05-13T14:00:00",
      endTime: "2025-05-13T15:30:00",
      location: "Conference Room",
      attendees: 4,
    },
    {
      id: "4",
      title: "Sprint Planning",
      startTime: "2025-05-14T10:00:00",
      endTime: "2025-05-14T11:30:00",
      location: "Meeting Room B",
      attendees: 10,
    },
  ]);

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString([], { weekday: "short", month: "short", day: "numeric" });
  };

  const isToday = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    return date.getDate() === today.getDate() && date.getMonth() === today.getMonth() && date.getFullYear() === today.getFullYear();
  };

  return (
    <Card className="bg-zinc-800 border-zinc-700 text-zinc-100 transition-all duration-300 hover:bg-zinc-750">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-medium flex items-center">
          <svg
            className="w-5 h-5 mr-2"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <rect
              x="3"
              y="4"
              width="18"
              height="18"
              rx="2"
              fill="#4285F4"
            />
            <path
              d="M16 2V6"
              stroke="white"
              strokeWidth="2"
              strokeLinecap="round"
            />
            <path
              d="M8 2V6"
              stroke="white"
              strokeWidth="2"
              strokeLinecap="round"
            />
            <path
              d="M3 10H21"
              stroke="white"
              strokeWidth="2"
            />
          </svg>
          Calendar Events
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoaded ? (
          <div className="space-y-2">
            {events.map((event, index) => (
              <div
                key={event.id}
                className="p-3 bg-zinc-700 rounded-md border border-zinc-600 hover-scale transition-all duration-300 hover:border-zinc-500"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="flex justify-between items-start">
                  <h3 className="text-sm font-medium text-zinc-100">{event.title}</h3>
                  {isToday(event.startTime) && <span className="text-xs bg-green-900/50 text-green-200 px-2 py-0.5 rounded-full">Today</span>}
                </div>
                <div className="mt-2 text-xs text-zinc-400 flex items-center">
                  <svg
                    className="w-3 h-3 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  {formatTime(event.startTime)} - {formatTime(event.endTime)}
                </div>
                <div className="mt-1 text-xs text-zinc-400 flex items-center">
                  <svg
                    className="w-3 h-3 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                  {event.location}
                </div>
                <div className="mt-1 text-xs text-zinc-400 flex items-center">
                  <svg
                    className="w-3 h-3 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
                    />
                  </svg>
                  {event.attendees} attendees
                </div>
                <div className="mt-1 text-xs text-zinc-400">{formatDate(event.startTime)}</div>
              </div>
            ))}
            <a
              href="https://calendar.google.com"
              target="_blank"
              rel="noopener noreferrer"
              className="block text-xs text-center mt-2 text-blue-400 hover:underline transition-all duration-300 hover:text-blue-300"
            >
              View calendar →
            </a>
          </div>
        ) : (
          <div className="space-y-2">
            {[1, 2, 3, 4].map((i) => (
              <div
                key={i}
                className="p-3 bg-zinc-700 rounded-md border border-zinc-600 relative overflow-hidden"
              >
                <Skeleton className="h-4 w-3/4 bg-zinc-600" />
                <div className="flex mt-2">
                  <Skeleton className="h-3 w-1/2 mr-2 bg-zinc-600" />
                </div>
                <div className="flex mt-1">
                  <Skeleton className="h-3 w-1/3 bg-zinc-600" />
                </div>
                <div className="flex mt-1">
                  <Skeleton className="h-3 w-1/4 bg-zinc-600" />
                </div>
                <div className="absolute inset-0 animate-shimmer bg-gradient-to-r from-transparent via-zinc-600/10 to-transparent" />
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
