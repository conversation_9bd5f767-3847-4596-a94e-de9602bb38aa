"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";

interface OKR {
  id: string;
  title: string;
  progress: number;
  dueDate: string;
  owner: string;
}

interface OKRsWidgetProps {
  isLoaded: boolean;
}

export function OKRsWidget({ isLoaded }: OKRsWidgetProps) {
  // Mock data - in a real app, this would come from an API
  const [okrs] = useState<OKR[]>([
    {
      id: "OKR-1",
      title: "Increase user engagement by 30%",
      progress: 65,
      dueDate: "2025-06-30",
      owner: "Marketing Team",
    },
    {
      id: "OKR-2",
      title: "Launch 3 new product features",
      progress: 33,
      dueDate: "2025-06-15",
      owner: "Product Team",
    },
    {
      id: "OKR-3",
      title: "Reduce customer support response time to < 2h",
      progress: 80,
      dueDate: "2025-05-31",
      owner: "Support Team",
    },
    {
      id: "OKR-4",
      title: "Achieve 99.9% system uptime",
      progress: 95,
      dueDate: "2025-05-31",
      owner: "DevOps Team",
    },
  ]);

  const getProgressColor = (progress: number) => {
    if (progress < 30) return "bg-red-500";
    if (progress < 70) return "bg-yellow-500";
    return "bg-green-500";
  };

  return (
    <Card className="bg-zinc-800 border-zinc-700 text-zinc-100 transition-all duration-300 hover:bg-zinc-750">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-medium flex items-center">
          <svg
            className="w-5 h-5 mr-2"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12 2L19 9L12 16L5 9L12 2Z"
              fill="#10B981"
            />
            <path
              d="M19 15L12 22L5 15"
              stroke="#10B981"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          OKRs
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoaded ? (
          <div className="space-y-3">
            {okrs.map((okr, index) => (
              <div
                key={okr.id}
                className="p-3 bg-zinc-700 rounded-md border border-zinc-600 hover-scale transition-all duration-300 hover:border-zinc-500"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="flex justify-between items-start">
                  <span className="text-xs font-medium text-zinc-400">{okr.id}</span>
                  <span className="text-xs font-medium text-zinc-400">{okr.owner}</span>
                </div>
                <h3 className="mt-1 text-sm font-medium text-zinc-100">{okr.title}</h3>
                <div className="mt-2 flex items-center gap-2">
                  <Progress
                    value={okr.progress}
                    className="h-2 bg-zinc-600"
                  >
                    <div
                      className={`h-full ${getProgressColor(okr.progress)} rounded-full transition-all`}
                      style={{ width: `${okr.progress}%` }}
                    />
                  </Progress>
                  <span className="text-xs font-medium text-zinc-300">{okr.progress}%</span>
                </div>
                <div className="mt-2 text-xs text-zinc-400">Due: {new Date(okr.dueDate).toLocaleDateString()}</div>
              </div>
            ))}
            <a
              href="#"
              className="block text-xs text-center mt-2 text-emerald-400 hover:underline transition-all duration-300 hover:text-emerald-300"
            >
              View all OKRs →
            </a>
          </div>
        ) : (
          <div className="space-y-3">
            {[1, 2, 3, 4].map((i) => (
              <div
                key={i}
                className="p-3 bg-zinc-700 rounded-md border border-zinc-600 relative overflow-hidden"
              >
                <div className="flex justify-between">
                  <Skeleton className="h-4 w-16 bg-zinc-600" />
                  <Skeleton className="h-4 w-24 bg-zinc-600" />
                </div>
                <Skeleton className="h-4 w-full mt-2 bg-zinc-600" />
                <Skeleton className="h-2 w-full mt-2 bg-zinc-600" />
                <Skeleton className="h-4 w-24 mt-2 bg-zinc-600" />
                <div className="absolute inset-0 animate-shimmer bg-gradient-to-r from-transparent via-zinc-600/10 to-transparent" />
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
