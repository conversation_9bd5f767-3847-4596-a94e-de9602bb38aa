"use client";

import type { JiraTask } from "@/components/jira-tasks-dialog";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { useJiraTasks } from "@/hooks/use-jira";
import { useModalsStore } from "@/lib/store/modals";

export function JiraTasksWidget() {
  const { setJiraTasksOpen } = useModalsStore();

  // Fetch tickets
  const { tasks: jiraTickets, isLoading: isLoadingTickets } = useJiraTasks({
    dateFilter: "month",
    statusFilter: "In Progress",
  });

  const getPriorityColor = (priority?: { id: string; name: string; iconUrl?: string } | null) => {
    if (!priority) return "text-gray-400";

    switch (priority.name) {
      case "Highest":
        return "text-red-500";
      case "High":
        return "text-orange-500";
      case "Medium":
        return "text-yellow-500";
      case "Low":
        return "text-blue-500";
      case "Lowest":
        return "text-gray-400";
      default:
        return "text-gray-400";
    }
  };

  const getTypeColor = (type?: string) => {
    switch (type) {
      case "bug":
        return "bg-red-900/50 text-red-200";
      case "task":
        return "bg-blue-900/50 text-blue-200";
      case "story":
        return "bg-green-900/50 text-green-200";
      case "epic":
        return "bg-purple-900/50 text-purple-200";
      default:
        return "bg-gray-900/50 text-gray-200";
    }
  };

  // Get appropriate status badge class
  const getStatusClass = (status?: { id: string; name: string; statusCategory?: string } | null) => {
    if (!status) return "border-gray-500";

    switch (status.name) {
      case "To Do":
        return "bg-gray-100 text-gray-800";
      case "In Progress":
        return "bg-blue-100 text-blue-800";
      case "Done":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Card className="bg-zinc-800 border-zinc-700 text-zinc-100 transition-all duration-300 hover:bg-zinc-750">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-medium flex items-center">
          <svg
            className="w-5 h-5 mr-2"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12 2L2 7L12 12L22 7L12 2Z"
              fill="#0052CC"
            />
            <path
              d="M2 17L12 22L22 17V7L12 12L2 7V17Z"
              fill="#0052CC"
            />
          </svg>
          Jira Tasks
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoadingTickets ? (
          <div className="space-y-2">
            {[1, 2, 3, 4].map((i) => (
              <div
                key={i}
                className="p-3 bg-zinc-700 rounded-md border border-zinc-600 relative overflow-hidden"
              >
                <div className="flex justify-between">
                  <Skeleton className="h-4 w-16 bg-zinc-600" />
                  <Skeleton className="h-4 w-12 bg-zinc-600" />
                </div>
                <Skeleton className="h-4 w-full mt-2 bg-zinc-600" />
                <Skeleton className="h-4 w-24 mt-2 bg-zinc-600" />
                <div className="absolute inset-0 animate-shimmer bg-gradient-to-r from-transparent via-zinc-600/10 to-transparent" />
              </div>
            ))}
          </div>
        ) : (
          <div className="space-y-2">
            {jiraTickets.map((task: JiraTask, index: number) => (
              <div
                key={task.id}
                className="p-3 bg-zinc-700 rounded-md border border-zinc-600 hover-scale transition-all duration-300 hover:border-zinc-500"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="flex justify-between items-start">
                  <div className="flex items-center">
                    <div className={`w-2 h-2 rounded-full ${getPriorityColor(task.priority)} mr-2`} />
                    <span className="text-xs font-medium text-zinc-400">{task.key}</span>
                  </div>
                </div>
                <h3 className="mt-1 text-sm font-medium text-zinc-100">{task.title}</h3>
                {task.dueDate && <div className="mt-2 text-xs text-zinc-400">Due: {new Date(task.dueDate).toLocaleDateString()}</div>}
              </div>
            ))}
            <a
              href="https://bravebits.atlassian.net"
              target="_blank"
              rel="noopener noreferrer"
              className="block text-xs text-center mt-2 text-blue-400 hover:underline transition-all duration-300 hover:text-blue-300"
            >
              View all in Jira →
            </a>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
