import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { extractNormalText } from "@/lib/editor/utils";
import { File, FileX } from "lucide-react";

// Add animated collapsible for knowledgeBase results
export function KnowledgeBaseCollapse({ result }: { result: any[] }) {
  const documents = Array.isArray(result) ? result : [];

  // Always show the badge, but with different content based on results
  const hasDocuments = documents && documents.length > 0;

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Badge
          className="cursor-pointer mt-0.5 py-1.5 px-2.5 rounded-full"
          variant={hasDocuments ? "secondary" : "outline"}
        >
          {hasDocuments ? (
            <>
              <File className="w-3 h-3 mr-1" />
              Đ<PERSON> đọc {documents.length} tài liệu
            </>
          ) : (
            <>
              <FileX className="w-3 h-3 mr-1" />
              Không tìm thấy tài liệu
            </>
          )}
        </Badge>
      </DialogTrigger>
      <DialogContent className="overflow-x-hidden max-w-3xl">
        <DialogHeader>
          <DialogTitle>Tài liệu liên quan</DialogTitle>
          <DialogDescription>
            {hasDocuments ? `${documents.length} tài liệu liên quan` : "Không tìm thấy tài liệu liên quan với câu hỏi của bạn"}
          </DialogDescription>
        </DialogHeader>
        <div className="mt-2.5 space-y-2 max-h-[50dvh] overflow-y-auto w-fit">
          {hasDocuments ? (
            documents.map((item: any, index: number) => {
              const doc = item.documents;
              const { id, content, metadata } = doc;
              const rawDescription = extractNormalText(content);
              const sanitized = rawDescription.replace(/\s+/g, " ").trim();
              const shortDescription = sanitized.length > 200 ? `${sanitized.slice(0, 200)}...` : sanitized;
              return (
                <Card
                  key={`${id}-${index}`}
                  className="rounded-xl bg-transparent hover:bg-muted transition-all gap-2 p-4 max-w-md w-fit"
                >
                  <CardHeader
                    className="cursor-pointer group p-0"
                    onClick={() => {
                      window.open(metadata.url, "_blank");
                    }}
                  >
                    <CardTitle className="flex items-center gap-1 text-base group-hover:underline">
                      <File className="w-4 h-4" />
                      {metadata.file_title || `Doc #${id}`}
                    </CardTitle>
                    <CardDescription>
                      {metadata.loc?.lines && (
                        <span>
                          Đoạn {metadata.loc.lines.from}-{metadata.loc.lines.to}
                        </span>
                      )}
                      <span className="px-2">|</span>
                      {metadata.department && <span>Phòng ban: {metadata.department}</span>}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="p-0 text-sm break-words whitespace-normal">{shortDescription}</CardContent>
                </Card>
              );
            })
          ) : (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <FileX className="w-12 h-12 text-muted-foreground mb-3" />
              <p className="text-sm text-muted-foreground font-medium mb-1">Không tìm thấy tài liệu liên quan</p>
              <p className="text-xs text-muted-foreground">Thử diễn đạt lại câu hỏi hoặc hỏi về chủ đề khác</p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
