"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { useModalsStore } from "@/lib/store/modals";
import { File, Loader2 } from "lucide-react";
import { useState, useEffect } from "react";

interface DocumentBadgeProps {
  isLoading?: boolean;
  args?: {
    source?: "personal" | "company";
    search?: string;
  };
  result?: {
    files: Array<any>;
    count: number;
    source: string;
    error?: string;
  };
}

export function DocumentBadgeLoading() {
  return (
    <div className="inline-flex items-center gap-2 bg-muted px-2.5 py-1.5 rounded-md text-sm">
      <Loader2 className="h-4 w-4 animate-spin" />
      <span>Loading documents...</span>
    </div>
  );
}

export function DocumentBadge({ isLoading, args, result }: DocumentBadgeProps) {
  const { setFileManagerOpen } = useModalsStore();
  const [fileCount, setFileCount] = useState<number>(0);

  // Handle file manager tab change when clicked
  const handleOpenFileManager = () => {
    // Get the global function from the window object
    const setFileManagerTab = (window as any).__setFileManagerTab;

    // Set the active tab based on source if the function exists
    if (typeof setFileManagerTab === "function" && args?.source) {
      setFileManagerTab(args.source);
    }

    // Open the file manager modal
    setFileManagerOpen(true);
  };

  useEffect(() => {
    if (result?.count) {
      setFileCount(result.count);
    }
  }, [result]);

  if (isLoading) {
    return <DocumentBadgeLoading />;
  }

  if (result?.error) {
    return (
      <div className="inline-flex items-center gap-2 bg-destructive/10 text-destructive px-2.5 py-1.5 rounded-md text-sm">
        <File className="h-4 w-4" />
        <span>Error loading documents</span>
      </div>
    );
  }

  const source = args?.source || "company";
  const searchTerm = args?.search ? ` matching "${args.search}"` : "";

  return (
    <Button
      variant="outline"
      size="sm"
      className="inline-flex items-center gap-2"
      onClick={handleOpenFileManager}
    >
      <File className="h-4 w-4" />
      <span>
        {fileCount} {source} document{fileCount !== 1 ? "s" : ""}
        {searchTerm}
      </span>
    </Button>
  );
}
