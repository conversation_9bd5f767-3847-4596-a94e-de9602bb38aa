"use client";

import { useEffect, useState } from "react";
import { WorkflowCard } from "./workflow-card";
import { WorkflowDetailModal } from "./workflow-detail-modal";

interface Workflow {
  id: string;
  name: string;
  creator: string;
  status: "active" | "inactive";
  lastRun: string;
  successRate: number;
  nodesCount: number;
  purpose: string;
  nodes: string[];
  executions: number;
  createdAt: string;
  updatedAt: string;
  tags: string[];
}

interface ApiWorkflow {
  id: string;
  name: string;
  active: boolean;
  nodes: Array<{
    type: string;
    name: string;
  }>;
  createdAt: string;
  updatedAt: string;
  tags: Array<{
    name: string;
  }>;
}

function transformWorkflowData(apiWorkflow: ApiWorkflow): Workflow {
  // Extract node types from the nodes array
  const nodeTypes = apiWorkflow.nodes.map((node) => {
    const type = node.type.split(".")[1] || node.type;
    return type.charAt(0).toUpperCase() + type.slice(1);
  });

  // Get unique node types
  const uniqueNodeTypes = [...new Set(nodeTypes)];

  return {
    id: apiWorkflow.id,
    name: apiWorkflow.name,
    creator: "System", // Default value since API doesn't provide this
    status: apiWorkflow.active ? "active" : "inactive",
    lastRun: "Never", // Default value since API doesn't provide this
    successRate: 100, // Default value since API doesn't provide this
    nodesCount: apiWorkflow.nodes.length,
    purpose: apiWorkflow.name, // Using name as purpose since API doesn't provide this
    nodes: uniqueNodeTypes,
    executions: 0, // Default value since API doesn't provide this
    createdAt: apiWorkflow.createdAt,
    updatedAt: apiWorkflow.updatedAt,
    tags: apiWorkflow.tags.map((tag) => tag.name),
  };
}

export default function Dashboard() {
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [selectedWorkflow, setSelectedWorkflow] = useState<any | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchWorkflows = async () => {
      try {
        const response = await fetch(
          "https://workflow.bravebits.co/webhook/get-workflows"
        );
        const data = await response.json();
        const transformedWorkflows = data.map(transformWorkflowData);
        setWorkflows(transformedWorkflows);
      } catch (error) {
        console.error("Error fetching workflows:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchWorkflows();
  }, []);

  const openWorkflowDetails = (workflow: any) => {
    console.log("workflow", workflow);
    setSelectedWorkflow(workflow);
    setIsModalOpen(true);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
      </div>
    );
  }

  return (
    <div className="flex flex-col">
      <div className="flex flex-1">
        <main className="flex-1 p-6 pt-0">
          <div className="mb-6 flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
            <div>
              <h2 className="text-2xl font-bold tracking-tight">Workflows</h2>
              <p className="text-muted-foreground">
                Manage and monitor your n8n workflows
              </p>
            </div>
            {/* <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search workflows..."
                  className="w-full pl-8 sm:w-[300px] md:w-[200px] lg:w-[300px]"
                />
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    <Filter className="mr-2 h-6 w-6" />
                    Filter
                    <ChevronDown className="ml-2 h-6 w-6" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[200px]">
                  <DropdownMenuItem>Active Workflows</DropdownMenuItem>
                  <DropdownMenuItem>Inactive Workflows</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div> */}
          </div>

          <div
            className={
              viewMode === "grid"
                ? "grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
                : "flex flex-col gap-4"
            }
          >
            {workflows.map((workflow) => (
              <WorkflowCard
                key={workflow.id}
                workflow={workflow}
                viewMode={viewMode}
                onClick={() => openWorkflowDetails(workflow)}
              />
            ))}
          </div>
        </main>
      </div>

      {selectedWorkflow && (
        <WorkflowDetailModal
          workflow={selectedWorkflow}
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
        />
      )}
    </div>
  );
}
