"use client";

import { <PERSON><PERSON> } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Switch } from "@/components/ui/switch";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import dayjs from "dayjs";
import {
  AlertCircle,
  Calendar,
  Code2,
  FileJson,
  Info,
  User,
} from "lucide-react";
import { useState } from "react";

interface WorkflowDetailModalProps {
  workflow: any;
  isOpen: boolean;
  onClose: () => void;
}

export function WorkflowDetailModal({
  workflow,
  isOpen,
  onClose,
}: WorkflowDetailModalProps) {
  const [activeTab, setActiveTab] = useState("overview");

  // Mock JSON for the workflow
  const mockJson = {
    id: workflow.id,
    name: workflow.name,
    active: workflow.status === "active",
    nodes: workflow.nodes
      .map((node: string, index: number) => ({
        id: `node_${index + 1}`,
        name: node,
        type: node.toLowerCase().replace(/\s+/g, ""),
        position: [index * 200, 100],
        parameters: {
          // Mock parameters
          operation: "GET",
          url: "https://example.com/api",
          authentication: "OAuth2",
        },
        connections: {
          main: [
            [
              {
                node: `node_${index + 2}`,
                type: "main",
                index: 0,
              },
            ],
          ],
        },
      }))
      .slice(0, -1),
    connections: {
      // Mock connections
    },
    settings: {
      executionOrder: "sequential",
      saveExecutionProgress: true,
      saveManualExecutions: true,
      timezone: "UTC",
    },
    staticData: {},
    pinData: {},
    versionId: "v4.12.0",
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="min-w-4xl">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl flex items-center gap-2">
              {workflow.name}
              <Switch checked={workflow.status === "active"} />
            </DialogTitle>
          </div>
          <DialogDescription>
            <p>Workflow ID: {workflow.id}</p>
            <p>
              Created on {dayjs(workflow.createdAt).format("HH:mm DD/MM/YYYY")}
            </p>
          </DialogDescription>
        </DialogHeader>

        <Tabs
          defaultValue="overview"
          value={activeTab}
          onValueChange={setActiveTab}
        >
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="nodes">Nodes</TabsTrigger>
            <TabsTrigger value="executions">Executions</TabsTrigger>
            <TabsTrigger value="json">JSON</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4 py-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-4">
                <div>
                  <h3 className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <Info className="h-4 w-4" />
                    Purpose
                  </h3>
                  <p className="mt-1">{workflow.purpose}</p>
                </div>

                <div>
                  <h3 className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <User className="h-4 w-4" />
                    Creator
                  </h3>
                  <p className="mt-1">{workflow.creator}</p>
                </div>

                <div>
                  <h3 className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <Calendar className="h-4 w-4" />
                    Created
                  </h3>
                  <p className="mt-1">{workflow.created}</p>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <h3 className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <Code2 className="h-4 w-4" />
                    Nodes in Use ({workflow.nodes.length})
                  </h3>
                  <div className="mt-2 flex flex-wrap gap-2">
                    {workflow.nodes.map((node: string, index: number) => (
                      <Badge key={index} variant="outline">
                        {node}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <AlertCircle className="h-4 w-4" />
                    Statistics
                  </h3>
                  <div className="mt-2 grid grid-cols-2 gap-2">
                    <div className="rounded-md border p-2">
                      <div className="text-sm text-muted-foreground">
                        Success Rate
                      </div>
                      <div className="text-lg font-medium">
                        {workflow.successRate}%
                      </div>
                    </div>
                    <div className="rounded-md border p-2">
                      <div className="text-sm text-muted-foreground">
                        Total Executions
                      </div>
                      <div className="text-lg font-medium">
                        {workflow.executions.toLocaleString()}
                      </div>
                    </div>
                    <div className="rounded-md border p-2">
                      <div className="text-sm text-muted-foreground">
                        Last Run
                      </div>
                      <div className="text-lg font-medium">
                        {workflow.lastRun}
                      </div>
                    </div>
                    <div className="rounded-md border p-2">
                      <div className="text-sm text-muted-foreground">
                        Category
                      </div>
                      <div className="text-lg font-medium">
                        {workflow.category}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="nodes" className="py-4">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Workflow Nodes</h3>
              <div className="rounded-md border">
                <div className="grid grid-cols-4 border-b bg-muted/50 p-2 text-sm font-medium">
                  <div>Node Name</div>
                  <div>Type</div>
                  <div>Position</div>
                  <div>Connections</div>
                </div>
                {workflow.nodes.map((node: string, index: number) => (
                  <div
                    key={index}
                    className="grid grid-cols-4 border-b p-2 text-sm last:border-0"
                  >
                    <div>{node}</div>
                    <div>{node.split(" ")[0]}</div>
                    <div>Position {index + 1}</div>
                    <div>
                      {index < workflow.nodes.length - 1
                        ? `Connected to ${workflow.nodes[index + 1]}`
                        : "End node"}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="executions" className="py-4">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Recent Executions</h3>
              <div className="rounded-md border">
                <div className="grid grid-cols-5 border-b bg-muted/50 p-2 text-sm font-medium">
                  <div>Execution ID</div>
                  <div>Status</div>
                  <div>Started</div>
                  <div>Duration</div>
                  <div>Triggered By</div>
                </div>
                {Array.from({ length: 5 }).map((_, index) => (
                  <div
                    key={index}
                    className="grid grid-cols-5 border-b p-2 text-sm last:border-0"
                  >
                    <div>exec_{Math.floor(Math.random() * 10000)}</div>
                    <div>
                      <Badge
                        variant={
                          Math.random() > 0.2 ? "default" : "destructive"
                        }
                      >
                        {Math.random() > 0.2 ? "Success" : "Failed"}
                      </Badge>
                    </div>
                    <div>
                      {index === 0 ? "Just now" : `${index * 2} hours ago`}
                    </div>
                    <div>{Math.floor(Math.random() * 60)} seconds</div>
                    <div>{Math.random() > 0.5 ? "Schedule" : "Manual"}</div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="json" className="py-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="flex items-center gap-2 text-lg font-medium">
                  <FileJson className="h-5 w-5" />
                  Workflow JSON
                </h3>
                <Button variant="outline" size="sm">
                  Copy JSON
                </Button>
              </div>
              <div className="max-h-[400px] overflow-auto rounded-md border bg-muted p-4">
                <pre className="text-xs">
                  {JSON.stringify(mockJson, null, 2)}
                </pre>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="settings" className="py-4">
            <div className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Execution Settings</h3>
                  <div className="flex items-center justify-between rounded-md border p-3">
                    <div className="space-y-0.5">
                      <div className="text-sm">Execution Order</div>
                      <div className="text-xs text-muted-foreground">
                        How nodes are executed
                      </div>
                    </div>
                    <Badge>Sequential</Badge>
                  </div>
                  <div className="flex items-center justify-between rounded-md border p-3">
                    <div className="space-y-0.5">
                      <div className="text-sm">Timeout</div>
                      <div className="text-xs text-muted-foreground">
                        Maximum execution time
                      </div>
                    </div>
                    <Badge>3 minutes</Badge>
                  </div>
                </div>

                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Workflow Settings</h3>
                  <div className="flex items-center justify-between rounded-md border p-3">
                    <div className="space-y-0.5">
                      <div className="text-sm">Save Manual Executions</div>
                      <div className="text-xs text-muted-foreground">
                        Save data for manual runs
                      </div>
                    </div>
                    <Switch checked={true} />
                  </div>
                  <div className="flex items-center justify-between rounded-md border p-3">
                    <div className="space-y-0.5">
                      <div className="text-sm">Save Execution Progress</div>
                      <div className="text-xs text-muted-foreground">
                        Save intermediate results
                      </div>
                    </div>
                    <Switch checked={true} />
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-sm font-medium">Access Control</h3>
                <div className="rounded-md border">
                  <div className="grid grid-cols-3 border-b bg-muted/50 p-2 text-sm font-medium">
                    <div>User</div>
                    <div>Role</div>
                    <div>Last Access</div>
                  </div>
                  <div className="grid grid-cols-3 border-b p-2 text-sm">
                    <div>{workflow.creator}</div>
                    <div>Owner</div>
                    <div>Today</div>
                  </div>
                  <div className="grid grid-cols-3 border-b p-2 text-sm">
                    <div>Admin Team</div>
                    <div>Editor</div>
                    <div>Yesterday</div>
                  </div>
                  <div className="grid grid-cols-3 p-2 text-sm">
                    <div>Workflow Viewers</div>
                    <div>Viewer</div>
                    <div>Last week</div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          {/* <Button>Edit Workflow</Button> */}
        </div>
      </DialogContent>
    </Dialog>
  );
}
