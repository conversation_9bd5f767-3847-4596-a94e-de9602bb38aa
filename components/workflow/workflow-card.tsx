"use client";

import { Badge } from "@/components/ui/badge";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Activity, ArrowUpRight, Clock, Code2 } from "lucide-react";

interface WorkflowCardProps {
  workflow: any;
  viewMode: "grid" | "list";
  onClick: () => void;
}

export function WorkflowCard({
  workflow,
  viewMode,
  onClick,
}: WorkflowCardProps) {
  // const [isActive, setIsActive] = useState(workflow.status === "active");

  // const handleToggleStatus = (e: React.MouseEvent) => {
  //   e.stopPropagation();
  //   // In a real app, this would update the workflow status
  //   console.log(`Toggle workflow ${workflow.id} status`);
  //   // setIsActive(!isActive);
  // };

  if (viewMode === "list") {
    return (
      <Card
        className="cursor-pointer transition-all hover:bg-accent/50"
        onClick={onClick}
      >
        <div className="flex items-center px-4">
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <h3 className="font-medium">{workflow.name}</h3>
              <Badge
                variant={workflow.status === "active" ? "default" : "secondary"}
              >
                {workflow.status === "active" ? "Active" : "Inactive"}
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground">
              Created by {workflow.creator}
            </p>
          </div>

          <div className="flex items-center gap-6">
            <div className="flex flex-col items-end">
              <div className="flex items-center gap-1">
                <Activity className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">
                  {workflow.successRate}%
                </span>
              </div>
              <div className="flex items-center gap-1">
                <Code2 className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{workflow.nodesCount} nodes</span>
              </div>
            </div>

            <div className="flex items-center gap-1">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{workflow.lastRun}</span>
            </div>

            {/* <div onClick={handleToggleStatus}>
              <Switch checked={isActive} />
            </div> */}

            <ArrowUpRight className="h-4 w-4" />
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card
      className="cursor-pointer transition-all hover:bg-accent/50"
      onClick={onClick}
    >
      <CardHeader className="flex items-center justify-between gap-2">
        <CardTitle className="truncate flex-1">{workflow.name}</CardTitle>
        {/* <Switch
          onClick={handleToggleStatus}
          checked={isActive}
          className="w-8 cursor-pointer"
        /> */}
      </CardHeader>
      <CardContent>
        <div className="mb-2 flex justify-between gap-2 text-sm">
          <div className="flex items-center gap-1">
            <Activity className="h-4 w-4 text-muted-foreground" />
            <span>{workflow.successRate}%</span>
          </div>
          <div className="flex items-center gap-1">
            <Code2 className="h-4 w-4 text-muted-foreground" />
            <span>{workflow.nodesCount} nodes</span>
          </div>
        </div>
        <div className="line-clamp-2 text-sm text-muted-foreground">
          {workflow.purpose.substring(0, 80)}...
        </div>
      </CardContent>
      <CardFooter className="flex justify-between border-t text-xs text-muted-foreground">
        <span>By {workflow.creator}</span>
        {/* <span>{workflow.lastRun}</span> */}
      </CardFooter>
    </Card>
  );
}
