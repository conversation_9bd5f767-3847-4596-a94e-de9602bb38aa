"use client";

import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { SidebarMenu, SidebarMenuButton, SidebarMenuItem, useSidebar } from "@/components/ui/sidebar";
import { WORKSPACES } from "@/lib/constants";
import { useChatSettingsStore } from "@/lib/store/chat-settings-store";
import { ChevronsUpDown } from "lucide-react";
import Image from "next/image";

export function WorkspaceSwitcher() {
  const { isMobile } = useSidebar();
  const { workspace, setWorkspace } = useChatSettingsStore();

  const workspaceList = Object.values(WORKSPACES);

  if (!workspace) {
    return null;
  }

  const renderIcon = (workspace: any, className: string) => {
    if (!workspace.icon) {
      return <div className={`${className} bg-gray-400 rounded`} />;
    }

    try {
      const isReactComponent =
        typeof workspace.icon === "function" || (typeof workspace.icon === "object" && workspace.icon && workspace.icon.$$typeof);

      if (isReactComponent) {
        const IconComponent = workspace.icon;
        return <IconComponent className={className} />;
      } else if (typeof workspace.icon === "object" && workspace.icon && workspace.icon.src && workspace.icon.src.trim() !== "") {
        return (
          <Image
            src={workspace.icon}
            alt={workspace.label}
            width={24}
            height={24}
            className={`${className} object-contain`}
            onError={(e) => {
              console.error("Image failed to load for", workspace.label, ":", e);
            }}
          />
        );
      } else if (typeof workspace.icon === "string" && workspace.icon.trim() !== "") {
        return (
          <Image
            src={workspace.icon}
            alt={workspace.label}
            width={24}
            height={24}
            className={`${className} object-contain`}
            onError={(e) => {
              console.error("Image failed to load for", workspace.label, ":", e);
            }}
          />
        );
      } else {
        return <div className={`${className} bg-gray-400 rounded`} />;
      }
    } catch (error) {
      console.error("Error rendering icon for workspace:", workspace.label, error);
      return <div className={`${className} bg-gray-400 rounded`} />;
    }
  };

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              variant={"outline"}
              className={`data-[state=open]:bg-${workspace.color}-200 data-[state=open]:text-sidebar-accent-foreground`}
            >
              <div
                className={`flex aspect-square size-8 items-center justify-center rounded-md bg-${workspace.color}-200 text-${workspace.color}-800`}
              >
                {renderIcon(workspace, "size-4")}
              </div>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">{workspace.label}</span>
                {/* <span className="truncate text-xs">{workspace.la}</span> */}
              </div>
              <ChevronsUpDown className="ml-auto" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
            align="start"
            side={isMobile ? "bottom" : "right"}
            sideOffset={4}
          >
            <DropdownMenuLabel className="text-xs text-muted-foreground">Workspaces</DropdownMenuLabel>
            {workspaceList.map((workspace) => (
              <DropdownMenuItem
                key={workspace.id}
                onClick={() => setWorkspace(workspace)}
                className="gap-2 p-2"
              >
                <div className={`flex size-6 items-center justify-center rounded-sm border`}>
                  {renderIcon(workspace, `size-4 shrink-0 text-${workspace?.color || "zinc"}-200`)}
                </div>
                {workspace.label}
                {/* <DropdownMenuShortcut>⌘{index + 1}</DropdownMenuShortcut> */}
              </DropdownMenuItem>
            ))}
            {/* <DropdownMenuSeparator /> */}
            {/* <DropdownMenuItem className="gap-2 p-2">
              <div className="flex size-6 items-center justify-center rounded-md border bg-background">
                <Plus className="size-4" />
              </div>
              <div className="font-medium text-muted-foreground">Add workspace</div>
            </DropdownMenuItem> */}
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
