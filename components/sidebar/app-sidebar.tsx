"use client";

import { NavProjects } from "@/components/sidebar/nav-project";
import { WorkspaceSwitcher } from "@/components/sidebar/workspace-switcher";
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarRail } from "@/components/ui/sidebar";
import { UserButton } from "@clerk/nextjs";
import { dark } from "@clerk/themes";
import { CommandIcon, MoonIcon, Route, SquareTerminal, SunIcon } from "lucide-react";
import { useTheme } from "next-themes";

const data = {
  // user: {
  //   name: "shadcn",
  //   email: "<EMAIL>",
  //   avatar: "/avatars/shadcn.jpg",
  // },
  navMain: [
    {
      title: "Productivity",
      url: "#",
      icon: SquareTerminal,
      isActive: true,
      // items: [
      //   {
      //     title: "History",
      //     url: "#",
      //   },
      //   {
      //     title: "Starr<PERSON>",
      //     url: "#",
      //   },
      //   {
      //     title: "Settings",
      //     url: "#",
      //   },
      // ],
    },
    // {
    //   title: "Models",
    //   url: "#",
    //   icon: Bot,
    //   items: [
    //     {
    //       title: "Genesis",
    //       url: "#",
    //     },
    //     {
    //       title: "Explorer",
    //       url: "#",
    //     },
    //     {
    //       title: "Quantum",
    //       url: "#",
    //     },
    //   ],
    // },
    // {
    //   title: "Documentation",
    //   url: "#",
    //   icon: BookOpen,
    //   items: [
    //     {
    //       title: "Introduction",
    //       url: "#",
    //     },
    //     {
    //       title: "Get Started",
    //       url: "#",
    //     },
    //     {
    //       title: "Tutorials",
    //       url: "#",
    //     },
    //     {
    //       title: "Changelog",
    //       url: "#",
    //     },
    //   ],
    // },
    // {
    //   title: "Settings",
    //   url: "#",
    //   icon: Settings2,
    //   items: [
    //     {
    //       title: "General",
    //       url: "#",
    //     },
    //     {
    //       title: "Team",
    //       url: "#",
    //     },
    //     {
    //       title: "Billing",
    //       url: "#",
    //     },
    //     {
    //       title: "Limits",
    //       url: "#",
    //     },
    //   ],
    // },
  ],
  projects: [
    {
      name: "Optimus",
      url: "/",
      icon: CommandIcon,
    },
    // {
    //   name: "Dashboard",
    //   url: "/dashboard",
    //   icon: ChartArea,
    // },
    {
      name: "Workflows",
      url: "/workflows",
      icon: Route,
    },
  ],
};

export function AppSidebar() {
  const { setTheme, theme } = useTheme();

  return (
    <Sidebar
      className="group-data-[side=left]:border-r-0"
      collapsible="icon"
    >
      <SidebarHeader>
        <WorkspaceSwitcher />
      </SidebarHeader>
      <SidebarContent>
        {/* <NavMain items={data.navMain} /> */}
        <NavProjects projects={data.projects} />
      </SidebarContent>
      <SidebarFooter>
        <UserButton
          appearance={{
            baseTheme: theme === "dark" ? dark : undefined,
          }}
        >
          <UserButton.MenuItems>
            <UserButton.Action
              label={`Toggle ${theme === "light" ? "dark" : "light"} mode`}
              labelIcon={
                theme === "dark" ? (
                  <SunIcon
                    fill={"#e5e7eb"}
                    size={16}
                  />
                ) : (
                  <MoonIcon
                    fill={"#e5e7eb"}
                    size={16}
                  />
                )
              }
              onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
            />
          </UserButton.MenuItems>
        </UserButton>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
