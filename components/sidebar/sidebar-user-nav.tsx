"use client";

import { SignInButton, useUser } from "@clerk/nextjs";
import { useTheme } from "next-themes";
import { useRouter } from "next/navigation";

import { SidebarMenu, SidebarMenuItem } from "@/components/ui/sidebar";

export function SidebarUserNav() {
  const router = useRouter();
  const { setTheme, theme } = useTheme();
  const { isSignedIn, user: clerkUser } = useUser();

  if (!isSignedIn) {
    return (
      <SidebarMenu>
        <SidebarMenuItem>
          <SignInButton>
            <button className="w-full text-left px-1 py-0.5">Sign in</button>
          </SignInButton>
        </SidebarMenuItem>
      </SidebarMenu>
    );
  }
  return <></>;
}
