<a href="https://chat.vercel.ai/">
  <img alt="Next.js 14 and App Router-ready AI chatbot." src="app/(chat)/opengraph-image.png">
  <h1 align="center">Chat SDK</h1>
</a>

<p align="center">
    Chat SDK is a free, open-source template built with Next.js and the AI SDK that helps you quickly build powerful chatbot applications.
</p>

<p align="center">
  <a href="https://chat-sdk.dev"><strong>Read Docs</strong></a> ·
  <a href="#features"><strong>Features</strong></a> ·
  <a href="#model-providers"><strong>Model Providers</strong></a> ·
  <a href="#deploy-your-own"><strong>Deploy Your Own</strong></a> ·
  <a href="#running-locally"><strong>Running locally</strong></a>
</p>
<br/>

## Features

- [Next.js](https://nextjs.org) App Router
  - Advanced routing for seamless navigation and performance
  - React Server Components (RSCs) and Server Actions for server-side rendering and increased performance
- [AI SDK](https://sdk.vercel.ai/docs)
  - Unified API for generating text, structured objects, and tool calls with LLMs
  - Hooks for building dynamic chat and generative user interfaces
  - Supports xAI (default), OpenAI, Fireworks, and other model providers
- [shadcn/ui](https://ui.shadcn.com)
  - Styling with [Tailwind CSS](https://tailwindcss.com)
  - Component primitives from [Radix UI](https://radix-ui.com) for accessibility and flexibility
- Data Persistence
  - [Neon Serverless Postgres](https://vercel.com/marketplace/neon) for saving chat history and user data
  - [Vercel Blob](https://vercel.com/storage/blob) for efficient file storage
- [Auth.js](https://authjs.dev)
  - Simple and secure authentication
- Multiple AI model support
- Real-time streaming responses
- Document attachments
- Memory system
- **MCP (Model Context Protocol) Integration** - Connect external tools and services

## Model Providers

This template ships with [xAI](https://x.ai) `grok-2-1212` as the default chat model. However, with the [AI SDK](https://sdk.vercel.ai/docs), you can switch LLM providers to [OpenAI](https://openai.com), [Anthropic](https://anthropic.com), [Cohere](https://cohere.com/), and [many more](https://sdk.vercel.ai/providers/ai-sdk-providers) with just a few lines of code.

## Deploy Your Own

You can deploy your own version of the Next.js AI Chatbot to Vercel with one click:

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fvercel%2Fai-chatbot&env=AUTH_SECRET&envDescription=Generate%20a%20random%20secret%20to%20use%20for%20authentication&envLink=https%3A%2F%2Fgenerate-secret.vercel.app%2F32&project-name=my-awesome-chatbot&repository-name=my-awesome-chatbot&demo-title=AI%20Chatbot&demo-description=An%20Open-Source%20AI%20Chatbot%20Template%20Built%20With%20Next.js%20and%20the%20AI%20SDK%20by%20Vercel&demo-url=https%3A%2F%2Fchat.vercel.ai&products=%5B%7B%22type%22%3A%22integration%22%2C%22protocol%22%3A%22ai%22%2C%22productSlug%22%3A%22grok%22%2C%22integrationSlug%22%3A%22xai%22%7D%2C%7B%22type%22%3A%22integration%22%2C%22protocol%22%3A%22storage%22%2C%22productSlug%22%3A%22neon%22%2C%22integrationSlug%22%3A%22neon%22%7D%2C%7B%22type%22%3A%22blob%22%7D%5D)

## Running locally

You will need to use the environment variables [defined in `.env.example`](.env.example) to run Next.js AI Chatbot. It's recommended you use [Vercel Environment Variables](https://vercel.com/docs/projects/environment-variables) for this, but a `.env` file is all that is necessary.

> Note: You should not commit your `.env` file or it will expose secrets that will allow others to control access to your various AI and authentication provider accounts.

1. Install Vercel CLI: `npm i -g vercel`
2. Link local instance with Vercel and GitHub accounts (creates `.vercel` directory): `vercel link`
3. Download your environment variables: `vercel env pull`

## Supabase Integration

This project uses Supabase as a database with NextAuth integration. To set up:

1. Create a Supabase project at [supabase.com](https://supabase.com)
2. Set the following environment variables in your `.env.local` file:

```
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
SUPABASE_JWT_SECRET=your_supabase_jwt_secret
```

3. Run the SQL script in `lib/db/migrations/auth_supabase_setup.sql` to set up the NextAuth schema in your Supabase database.

4. In the Supabase dashboard, go to Authentication -> API Settings and check the "Enable Row Level Security (RLS)" option.

5. In API settings, expose the `next_auth` schema via the Serverless API by adding `next_auth` to the "Exposed schemas" list.

Row Level Security (RLS) is automatically configured by the SQL script. This ensures users can only access their own data.

## RAG System with Supabase

This project includes a full Retrieval-Augmented Generation (RAG) system implemented with Supabase PostgreSQL. The system features:

- Document storage with ownership and sharing capabilities
- Vector embeddings for semantic search (using pgvector)
- Team-based access control
- Chat history tracking
- Row-Level Security (RLS) for data protection

### Setting Up the RAG System

After setting up the basic Supabase integration:

1. Make sure you have the required extensions enabled in your Supabase project:

   - Go to the SQL Editor in your Supabase dashboard
   - Run `CREATE EXTENSION IF NOT EXISTS vector;` to enable vector support

2. Run the RAG schema setup script:

   - Execute the SQL in `lib/db/migrations/rag_system_setup.sql` to create all necessary tables, functions, and policies

3. The system includes:
   - Document management: Upload, index, and search documents
   - Document sharing: Share with individual users or teams
   - Vector search: Find semantically similar content across documents
   - Access control: Fine-grained permissions with Row-Level Security

### Key API Functions

```typescript
// Search for documents using embeddings
const results = await searchDocuments({
  queryEmbedding: embedding, // Your vector embedding
  matchCount: 10, // Number of results
  similarityThreshold: 0.7, // Minimum similarity score
});

// Create a document with sections
const docId = await createDocument({
  name: "Document Title",
  description: "Document description",
  sections: ["Section 1 text", "Section 2 text"],
  embeddings: [section1Embedding, section2Embedding],
});

// Share documents
await shareDocumentWithUser({
  documentId: docId,
  userEmail: "<EMAIL>",
  permissionLevel: "read", // "read", "edit", or "admin"
});
```

## MCP Configuration

The chatbot supports Model Context Protocol (MCP) servers, allowing you to connect external tools and services. You can configure MCP servers through the Tools dialog:

### Accessing MCP Settings

1. Click the **Tools** button (wrench icon) in the chat header
2. Click **MCP Settings** in the tools dialog
3. Add your MCP server configuration

### Supported Transport Types

#### SSE (Server-Sent Events)

For remote MCP servers:

```json
{
  "transport": {
    "type": "sse",
    "url": "https://your-mcp-server.com/sse",
    "headers": {
      "Authorization": "Bearer your-api-key"
    }
  }
}
```

#### Stdio (Local Command)

For local MCP servers:

```json
{
  "transport": {
    "type": "stdio",
    "command": "npx",
    "args": ["-y", "firecrawl-mcp"]
  }
}
```

### Example MCP Servers

- **Firecrawl**: Web scraping and crawling
- **Filesystem**: File system operations
- **Git**: Git repository management
- **PostgreSQL**: Database operations

### Configuration Steps

1. Choose a transport type (SSE or Stdio)
2. Configure the connection details:
   - **SSE**: Server URL and optional headers
   - **Stdio**: Command and arguments
3. Optionally add an API key/credential
4. Save and activate the configuration

The configured MCP tools will be available in your chat conversations automatically.

## Getting Started

[Rest of your existing README content...]

```bash
pnpm install
pnpm dev
```
