<PRD>
# Optimus Project - Product Requirements Document

## 1. Introduction

This Product Requirements Document (PRD) outlines the specifications, features, and implementation plan for <PERSON><PERSON><PERSON>, an internal AI-powered productivity assistant for BraveBits/SellerSmith. The document serves as a comprehensive guide for the product team to develop Optimus with the necessary integrations, features, and performance standards to meet organizational needs. It identifies the core requirements, technical architecture, and phased implementation approach to create an effective AI employee system that enhances workflow productivity across the organization.

## 2. Product overview

Optimus is an AI-powered internal assistant designed to serve as a virtual employee for BraveBits/SellerSmith staff. It integrates with the company's knowledge base and productivity tools to provide contextual assistance, automate routine tasks, and enhance workflow efficiency. The system aims to create AI Employees (AIE) that can perform their jobs autonomously, thereby significantly boosting organizational productivity.

### 2.1. Current status

Optimus currently exists as a basic application with the following components in place:
- Basic Next.js application with Clerk authentication
- Vercel AI SDK for chat implementation
- Supabase for vector store and embeddings
- n8n workflow for document embedding
- Basic chat interface and knowledge base integration

## 3. Goals and objectives

### 3.1. Primary goal

To create autonomous AI Employees (AIE) that can perform jobs independently and enhance the productivity of the entire BraveBits/SellerSmith organization.

### 3.2. Specific objectives

- Develop a responsive, high-performance internal chatbot system (latency <200ms)
- Seamlessly integrate company knowledge base and productivity tools
- Create customizable AI roles tailored to specific products and functions
- Enable user-created AI assistants with customizable instructions
- Achieve high availability (24/7) and scalability (100+ concurrent users)
- Deliver an MVP quickly for CEO evaluation and iterative improvement
- Establish an extensible architecture for future enhancements

### 3.3. Success metrics

- System availability: >99.9% uptime
- Query response time: <200ms average
- User adoption: >80% of staff using the system weekly
- Productivity enhancement: >20% reduction in time spent on routine tasks
- User satisfaction: >90% positive feedback

## 4. Target audience

### 4.1. Primary users

BraveBits/SellerSmith internal staff across different departments and roles, including:

- Product managers and developers
- Marketing and sales teams
- Customer support representatives
- Management and leadership
- Administrative staff

### 4.2. User needs and pain points

- Spending excessive time searching for company information
- Context switching between multiple productivity tools
- Repetitive tasks that could be automated
- Maintaining awareness of project status and deadlines
- Accessing relevant information specific to different product lines
- Scheduling and calendar management inefficiencies

## 5. Features and requirements

### 5.1. Core requirements

#### 5.1.1. Internal chatbot functionality

- Natural language processing capabilities
- Context awareness within conversations
- Multi-turn conversation support
- Proactive suggestions based on user context
- Chat history management and retrieval

#### 5.1.2. Knowledge base integration

- Google Drive text file integration
- Document indexing and retrieval
- Semantic search capabilities
- Automatic context inclusion in responses
- Regular knowledge base updates via n8n workflows

#### 5.1.3. Productivity tools integration

- Google Calendar integration
- Jira integration
- Drive integration
- OKR management integration
- Contextual awareness across integrated tools

#### 5.1.4. AI roles customization

- Product-specific AI assistants (PageFly, TailorKit, etc.)
- Customizable instructions and behavior
- Role-based access to relevant information
- User-created AI assistants with defined parameters

### 5.2. Non-functional requirements

#### 5.2.1. Performance

- Response latency: <200ms
- Concurrent users: Support for 100+ simultaneous users
- Availability: 24/7 system uptime with minimal maintenance windows

#### 5.2.2. Security

- Secure authentication via Clerk
- Role-based access control
- Data encryption at rest and in transit
- Compliance with company security policies

#### 5.2.3. Scalability

- Horizontal scaling capability
- Efficient resource utilization
- Performance monitoring and optimization

#### 5.2.4. Usability

- Intuitive chat interface
- Clear information presentation
- Minimal training requirements
- Consistent response format and quality

## 6. Implementation plan

### 6.1. Phase 1: Core integrations

#### 6.1.1. Google Calendar integration (TAP-44)

- Calendar events retrieval for AI context
- Basic event creation capabilities
- Schedule awareness for planning and recommendations
- Meeting suggestion and management

#### 6.1.2. Jira integration (TAP-43)

- Task retrieval for AI context
- Basic ticket updates and creation
- Project and sprint awareness
- Task prioritization assistance

#### 6.1.3. Memory system (TAP-50)

- Conversation history management
- Context persistence across sessions
- Relevant information recall
- User preference learning

### 6.2. Phase 2: Enhanced features

#### 6.2.1. Enhanced RAG with file management (TAP-59)

- File upload system implementation
- File and folder tagging (similar to Cursor)
- Tag-based context enhancement
- Improved retrieval accuracy

#### 6.2.2. Custom AI roles system (TAP-58, TAP-64)

- Product-specific AIs for PageFly, TailorKit, etc.
- Role configuration framework
- User-created AI assistants
- Role-specific knowledge and capabilities

### 6.3. Phase 3: Advanced features

#### 6.3.1. Analytics and monitoring

- Usage tracking implementation
- Performance monitoring dashboard
- User feedback collection system
- Continuous improvement framework

#### 6.3.2. Advanced tools and integrations

- OKR management integration
- Additional productivity tool connections
- Enhanced search capabilities
- Workflow automation features

## 7. User stories and acceptance criteria

### 7.1. Authentication and access

#### ST-101: User login
**As a** BraveBits staff member,  
**I want to** log into the Optimus system with my company credentials,  
**So that** I can access the chatbot and its integrated features securely.

**Acceptance criteria:**
- User can log in using Clerk authentication
- Authentication integrates with existing company credentials
- Failed login attempts are handled appropriately with clear error messages
- Session management follows security best practices

#### ST-102: Role-based access
**As a** system administrator,  
**I want to** assign different access levels based on user roles,  
**So that** users only access information relevant to their position.

**Acceptance criteria:**
- Admin interface for role management is available
- Roles can be assigned to users individually or by group
- System enforces access restrictions based on assigned roles
- Role changes take effect immediately

### 7.2. Chat interface

#### ST-201: Basic chat interaction
**As a** BraveBits employee,  
**I want to** interact with Optimus through a chat interface,  
**So that** I can ask questions and receive relevant information.

**Acceptance criteria:**
- Chat interface is accessible after login
- User can type messages and receive responses
- System displays typing indicators during response generation
- Chat supports text formatting for better readability

#### ST-202: Chat history
**As a** BraveBits employee,  
**I want to** view my past conversations with Optimus,  
**So that** I can refer back to previously shared information.

**Acceptance criteria:**
- Chat history is retained and accessible
- User can scroll through previous conversations
- System provides date and time stamps for messages
- Search functionality for finding specific past interactions

#### ST-203: Multi-turn conversations
**As a** BraveBits employee,  
**I want to** have multi-turn conversations with contextual awareness,  
**So that** I don't have to repeat information in follow-up questions.

**Acceptance criteria:**
- System maintains context across multiple turns
- References to previous messages are correctly understood
- Context window extends to an appropriate number of previous messages
- System can reference information from earlier in the conversation

### 7.3. Knowledge base integration

#### ST-301: Knowledge retrieval
**As a** BraveBits employee,  
**I want to** ask questions about company information,  
**So that** I can quickly find relevant documentation without searching manually.

**Acceptance criteria:**
- System accurately retrieves information from company knowledge base
- Responses include source attribution
- Search covers all indexed documentation
- Response latency remains under 200ms

#### ST-302: Knowledge base updates
**As a** content manager,  
**I want to** have new documentation automatically indexed,  
**So that** Optimus always has access to the most current information.

**Acceptance criteria:**
- n8n workflows regularly scan for new documents
- New documents are properly embedded and indexed
- System shows when information was last updated
- Document updates are reflected in search results within 15 minutes

#### ST-303: Document upload
**As a** BraveBits employee,  
**I want to** upload documents directly to Optimus for processing,  
**So that** I can quickly get information from specific files.

**Acceptance criteria:**
- Interface supports drag-and-drop document uploads
- System accepts common file formats (PDF, DOCX, TXT, etc.)
- Uploaded documents are processed for immediate reference
- User can specify whether to save uploaded documents permanently

### 7.4. Calendar integration

#### ST-401: Calendar awareness
**As a** BraveBits employee,  
**I want to** ask Optimus about my upcoming meetings and events,  
**So that** I can stay informed about my schedule.

**Acceptance criteria:**
- System connects to user's Google Calendar
- Calendar queries return accurate and relevant information
- System provides context-aware responses about schedule
- Time zone handling is appropriate for user location

#### ST-402: Event creation
**As a** BraveBits employee,  
**I want to** create calendar events through Optimus,  
**So that** I can schedule meetings without switching to another application.

**Acceptance criteria:**
- System can create events with title, time, description, and participants
- Created events appear correctly in Google Calendar
- System confirms event creation and provides summary
- Conflict detection warns about scheduling conflicts

#### ST-403: Schedule optimization
**As a** BraveBits employee,  
**I want to** get suggestions for optimal meeting times,  
**So that** I can schedule efficiently based on availability.

**Acceptance criteria:**
- System analyzes available time slots across participants
- Suggestions account for working hours and preferences
- Multiple time options are provided when requested
- System considers meeting duration and buffer times

### 7.5. Jira integration

#### ST-501: Task retrieval
**As a** BraveBits employee,  
**I want to** ask Optimus about my assigned tasks and tickets,  
**So that** I can quickly check my work priorities.

**Acceptance criteria:**
- System connects to Jira and retrieves user-specific tickets
- Task information includes status, priority, and deadlines
- Queries can filter by project, sprint, or status
- Response formatting is clear and scannable

#### ST-502: Ticket updates
**As a** BraveBits employee,  
**I want to** update ticket status through Optimus,  
**So that** I can manage my workflow without context switching.

**Acceptance criteria:**
- System can update ticket status, priority, and comments
- Updates are reflected in Jira immediately
- System confirms successful updates
- Error handling for failed updates with clear messaging

#### ST-503: Project awareness
**As a** project manager,  
**I want to** get project status summaries from Optimus,  
**So that** I can quickly assess progress without manual reporting.

**Acceptance criteria:**
- System provides sprint progress and burndown information
- Project summaries include key metrics and blockers
- Visualizations are available for progress tracking
- Information can be filtered by team or project

### 7.6. Memory system

#### ST-601: Conversation memory
**As a** BraveBits employee,  
**I want to** have Optimus remember details from previous conversations,  
**So that** I don't have to repeat context in future interactions.

**Acceptance criteria:**
- System retains important information from past interactions
- Memory persists across sessions and logouts
- Referenced information is accurately recalled when relevant
- Memory can be explicitly invoked or implicitly applied

#### ST-602: Preference learning
**As a** BraveBits employee,  
**I want to** have Optimus learn my preferences over time,  
**So that** interactions become more personalized and efficient.

**Acceptance criteria:**
- System tracks and remembers user preferences
- Personalization improves over time with usage
- User can view and edit stored preferences
- Preferences are applied consistently across features

#### ST-603: Relevant recall
**As a** BraveBits employee,  
**I want to** have Optimus bring up relevant past information when appropriate,  
**So that** I benefit from contextual assistance.

**Acceptance criteria:**
- System proactively surfaces relevant past information
- Recall is contextually appropriate and helpful
- User can dismiss suggestions that aren't relevant
- Balance between new information and recalled context

### 7.7. Custom AI roles

#### ST-701: Product-specific assistance
**As a** PageFly developer,  
**I want to** interact with a PageFly-specialized AI assistant,  
**So that** I get product-specific knowledge and assistance.

**Acceptance criteria:**
- AI roles exist for each major product (PageFly, TailorKit, etc.)
- Product-specific knowledge is accurately represented
- Interface indicates which AI role is currently active
- Responses include product-specific context and terminology

#### ST-702: Role creation
**As a** team leader,  
**I want to** create custom AI roles for my team,  
**So that** we have specialized assistance for our workflow.

**Acceptance criteria:**
- Interface for defining new AI roles
- Role configuration includes knowledge sources and behaviors
- Created roles are available to specified team members
- Roles can be edited and updated after creation

#### ST-703: Role switching
**As a** BraveBits employee,  
**I want to** switch between different AI roles,  
**So that** I can get the most relevant assistance for my current task.

**Acceptance criteria:**
- User can easily switch between available AI roles
- Interface clearly indicates active role
- Context is maintained appropriately during role switches
- Frequently used roles are easily accessible

### 7.8. File management

#### ST-801: File tagging
**As a** BraveBits employee,  
**I want to** tag files and folders with relevant metadata,  
**So that** Optimus can better understand context when retrieving information.

**Acceptance criteria:**
- Interface for adding and managing tags
- Tags are saved and associated with correct files
- Search functionality supports tag-based filtering
- Tag suggestions based on file content and usage

#### ST-802: File system navigation
**As a** BraveBits employee,  
**I want to** ask Optimus to find specific files or folders,  
**So that** I can quickly locate needed resources.

**Acceptance criteria:**
- System can search and retrieve files based on descriptions
- Results include file location, type, and last modified date
- Preview functionality for compatible file types
- Direct links to open files in appropriate applications

#### ST-803: File content queries
**As a** BraveBits employee,  
**I want to** ask questions about file contents,  
**So that** I can get information without opening the file.

**Acceptance criteria:**
- System can analyze and extract information from files
- Responses include source file attribution
- Support for multiple file formats
- Context-aware understanding of file content

### 7.9. Analytics and feedback

#### ST-901: Usage analytics
**As a** system administrator,  
**I want to** view usage statistics for Optimus,  
**So that** I can assess adoption and identify improvement areas.

**Acceptance criteria:**
- Dashboard shows key usage metrics
- Data can be filtered by date range, department, or feature
- Export functionality for further analysis
- Privacy controls for user data

#### ST-902: User feedback
**As a** BraveBits employee,  
**I want to** provide feedback on Optimus's responses,  
**So that** the system can improve over time.

**Acceptance criteria:**
- Simple feedback mechanism in the chat interface
- Options for rating responses and providing comments
- Feedback is stored and accessible for review
- Clear indication that feedback was submitted successfully

#### ST-903: Performance monitoring
**As a** system administrator,  
**I want to** monitor Optimus's technical performance,  
**So that** I can ensure it meets service level requirements.

**Acceptance criteria:**
- Real-time monitoring of system performance
- Alerts for performance degradation
- Historical performance trend visualization
- Resource utilization tracking

### 7.10. Database modeling

#### ST-1001: Database schema
**As a** developer,  
**I want to** have a well-designed database schema,  
**So that** data is organized efficiently and performance is optimized.

**Acceptance criteria:**
- Schema supports all required data types and relationships
- Indexing strategy optimizes query performance
- Schema documentation is complete and accessible
- Migration path for future schema changes

#### ST-1002: Vector store implementation
**As a** developer,  
**I want to** use Supabase for efficient vector store operations,  
**So that** semantic search performs with low latency.

**Acceptance criteria:**
- Vector embeddings are stored efficiently
- Query performance meets latency requirements (<200ms)
- Storage scales with increasing document volume
- Backup and recovery processes are in place

#### ST-1003: Data synchronization
**As a** system administrator,  
**I want to** ensure data consistency across systems,  
**So that** Optimus always has the most current information.

**Acceptance criteria:**
- Automated synchronization between data sources
- Conflict resolution mechanisms
- Logging of synchronization activities
- Manual override capabilities for exceptional cases

## 8. Technical requirements / stack

### 8.1. Frontend

- Next.js for the web application framework
- React for component-based UI development
- Vercel AI SDK for chat implementation
- Responsive design for desktop and mobile access
- Modern CSS framework for styling

### 8.2. Backend

- Node.js runtime environment
- Supabase for vector store and database functionality
- n8n for workflow automation and document processing
- RESTful API design for service integrations
- Serverless functions for specific operations

### 8.3. Authentication

- Clerk for user authentication and session management
- Role-based access control implementation
- Secure token handling and validation
- Integration with company SSO if applicable

### 8.4. Integrations

- Google Calendar API for calendar functionality
- Jira API for task management
- Google Drive API for document access
- Custom API clients for additional services
- Webhook support for real-time updates

### 8.5. AI and machine learning

- Integration with appropriate LLM providers
- Vector embeddings for semantic search
- Context management system
- Memory and retrieval mechanisms
- Learning systems for personalization

### 8.6. Deployment and DevOps

- CI/CD pipeline for automated testing and deployment
- Containerization for consistent environments
- Infrastructure as code for easy replication
- Monitoring and logging infrastructure
- Backup and disaster recovery solutions

## 9. Design and user interface

### 9.1. Chat interface

- Clean, minimal design focused on conversation
- Clear distinction between user and AI messages
- Typing indicators and loading states
- Support for rich text formatting and media display
- Mobile-responsive layout

### 9.2. File manager

- Intuitive navigation for file browsing
- Drag-and-drop functionality for uploads
- Preview capabilities for common file types
- Tagging interface with autocomplete
- Search and filter capabilities

### 9.3. Settings and preferences

- User profile management
- Notification preferences
- Display and accessibility options
- AI role selection and configuration
- Integration connection management

### 9.4. Analytics dashboard

- Key metrics visualization
- Usage patterns and trends
- Filter controls for data segmentation
- Export functionality for reports
- Administrative controls and settings

### 9.5. Accessibility considerations

- WCAG 2.1 AA compliance
- Keyboard navigation support
- Screen reader compatibility
- Sufficient color contrast
- Resizable text and responsive layouts
</PRD>

# Jira Ticket Mention Feature PRD

## Overview
Implement a feature where Jira tickets can be mentioned within the chat input box like badges (similar to Cursor's mention system) rather than immediately being sent as messages. When a Jira ticket is mentioned, its data should be attached to the message context when sent to the AI.

## Requirements

### Core Functionality
1. When a user selects a Jira ticket from the tasks dialog, instead of immediately sending it as a message, insert a mention badge in the multimodal-input text box.
2. The mention badge should display the Jira ticket key and be visually distinct.
3. Users can continue typing their message after the mention.
4. When the user sends the message, the full Jira ticket details should be included with the message context sent to the AI.
5. The AI should have access to all ticket fields (title, description, status, priority, etc.) to provide more informed responses.

### User Experience
1. Show a success notification when a ticket is mentioned.
2. Allow multiple ticket mentions in a single message.
3. Support removing ticket mentions before sending.
4. Make the ticket mention visually similar to Cursor's mention system for familiarity.

### Technical Details
1. Update the Jira tasks dialog to insert mentions rather than send messages.
2. Modify the multimodal-input component to support ticket mention badges.
3. Update the message handling to include ticket context when sending to the AI.
4. Enhance the API to fetch complete ticket details for mentioned tickets.
5. Update system prompts to inform the AI how to handle ticket mentions.

## Success Criteria
1. Users can mention tickets in messages without interrupting their typing flow.
2. The AI can reference and understand the complete context of mentioned tickets.
3. The UI clearly shows mentioned tickets with appropriate styling.
4. The feature improves productivity by providing direct ticket context in the conversation.

## Implementation Phases
The feature should be delivered in a single, cohesive implementation with backward compatibility for existing workflows.

# Product Requirements Document: Refactor File Mention Mechanism

## Overview
Refactor the current File Mention mechanism to treat mentioned files as actual message attachments instead of injecting them into the system prompt. This will provide a more consistent and intuitive experience.

## Current Implementation Problems
- Mentioned files are passed through `mentionedItems` in the request body
- Files are injected into the system prompt instead of being attached to the message
- This creates inconsistency with regular file attachments
- Potentially increases prompt size unnecessarily

## New Architecture Requirements

### Core Changes
1. **File Mention to Attachment Conversion**: When a file is mentioned via @ selector, convert it to a proper message attachment
2. **Maintain Ticket Mentions**: Keep Jira ticket mentions in the current prompt injection approach (they don't need to be attachments)
3. **Unified Attachment Handling**: Mentioned files should be processed the same way as drag-and-drop or file picker attachments
4. **Visual Consistency**: Mentioned files should appear in the attachments preview area

### Technical Requirements
1. **Attachment Structure**: Use the existing `ExtendedAttachment` interface for mentioned files
2. **File Metadata**: Preserve all file metadata when converting mentions to attachments
3. **State Management**: Update mention store to separate file mentions from ticket mentions
4. **API Changes**: Remove file mentions from `mentionedItems` in the API request body
5. **Backward Compatibility**: Ensure the change doesn't break existing functionality

### User Experience Requirements
1. **Visual Feedback**: Show mentioned files in the attachment preview area
2. **Removal**: Allow users to remove mentioned files from the attachment preview
3. **Clear Distinction**: Visually differentiate between mentioned files and uploaded files (optional)
4. **Consistent Behavior**: Mentioned files should behave identically to uploaded files

### Implementation Scope
- Modify `useMention` hook to handle file-to-attachment conversion
- Update `MultimodalInput` component to process mentioned files as attachments
- Adjust mention store to separate file and ticket handling
- Update API to exclude file mentions from prompt injection
- Maintain existing ticket mention functionality
- Update UI components to show proper feedback

### Success Criteria
1. Mentioned files appear as attachments in the preview area
2. Mentioned files are sent as `experimental_attachments` in the message
3. Ticket mentions continue to work via prompt injection
4. No breaking changes to existing file upload functionality
5. Consistent user experience across all file attachment methods

### Technical Stack
- React with TypeScript
- Zustand for state management
- AI SDK for message handling
- Existing multimodal input architecture