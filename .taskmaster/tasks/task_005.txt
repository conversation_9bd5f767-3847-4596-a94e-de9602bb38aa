# Task ID: 5
# Title: Enhance AttachmentsPreviewMemo Component
# Status: done
# Dependencies: 3
# Priority: medium
# Description: Update the AttachmentsPreviewMemo component to display file previews with upload status indicators.
# Details:
Modify the `AttachmentsPreviewMemo` component to handle file upload states:

1. Update the component to receive the `uploadQueue` prop
2. Pass the upload status to each `PreviewAttachment` component

```tsx
// In components/multimodal/attachments-preview.tsx
import React from 'react';
import { ExtendedAttachment } from './types';
import { PreviewAttachment } from './preview-attachment';

interface AttachmentsPreviewProps {
  attachments: ExtendedAttachment[];
  onRemove: (id: string) => void;
  uploadQueue: string[]; // New prop
}

export const AttachmentsPreviewMemo = React.memo(
  function AttachmentsPreview({ attachments, onRemove, uploadQueue }: AttachmentsPreviewProps) {
    if (!attachments.length) return null;

    return (
      <div className="flex flex-wrap gap-2 mt-2">
        {attachments.map((attachment) => (
          <PreviewAttachment
            key={attachment.id || attachment.url}
            attachment={attachment}
            onRemove={onRemove}
            isUploading={attachment.id ? uploadQueue.includes(attachment.id) : false}
          />
        ))}
      </div>
    );
  }
);
```

Ensure the component is properly exported and imported in the MultimodalInput component.

# Test Strategy:
1. Verify the AttachmentsPreviewMemo component correctly receives and uses the uploadQueue prop
2. Test that the isUploading prop is correctly passed to PreviewAttachment components
3. Check that the component renders correctly with various combinations of attachments and upload states
