# Task ID: 4
# Title: Implement File Upload to Google Drive Function
# Status: done
# Dependencies: 2, 3
# Priority: high
# Description: Create a function to upload files to Google Drive via the backend API endpoint, handling the FormData creation and fetch request.
# Details:
Implement the `uploadFileToDrive` function in `components/multimodal/multimodal-input.tsx`:

```typescript
const uploadFileToDrive = async (file: File, localAttachmentId: string) => {
  try {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('localAttachmentId', localAttachmentId);
    formData.append('fileName', file.name);
    formData.append('contentType', file.type);
    formData.append('chatId', chatId); // Assuming chatId is available in the component

    const response = await fetch('/api/files/upload-to-drive', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to upload file');
    }

    const data = await response.json();
    
    // Update the attachment with the Google Drive URL and change status to uploaded
    setAttachments(prev => 
      prev.map(att => 
        att.id === localAttachmentId 
          ? { ...att, url: data.url, uploadState: 'uploaded' } 
          : att
      )
    );
    
    // Remove from upload queue
    setUploadQueue(prev => prev.filter(id => id !== localAttachmentId));
    
  } catch (error) {
    console.error('Error uploading file:', error);
    
    // Update attachment status to failed
    setAttachments(prev => 
      prev.map(att => 
        att.id === localAttachmentId 
          ? { ...att, uploadState: 'failed' } 
          : att
      )
    );
    
    // Remove from upload queue
    setUploadQueue(prev => prev.filter(id => id !== localAttachmentId));
    
    // Show error toast
    toast.error(`Failed to upload ${file.name}: ${error.message || 'Unknown error'}`);
  }
};
```

Make sure to import the toast library (react-hot-toast is recommended) for error notifications.

# Test Strategy:
1. Test successful file uploads to Google Drive
2. Verify error handling for failed uploads
3. Check that the attachment state is correctly updated after upload
4. Confirm the upload queue is properly managed
5. Test that error notifications are displayed when uploads fail
