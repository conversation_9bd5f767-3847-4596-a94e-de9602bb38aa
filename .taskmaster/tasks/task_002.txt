# Task ID: 2
# Title: Implement File Selection Handler
# Status: done
# Dependencies: 1
# Priority: high
# Description: Create a handler function that processes selected files and prepares them for upload by generating unique IDs and creating ExtendedAttachment objects.
# Details:
Implement the `handleFileSelection` function in `components/multimodal/multimodal-input.tsx`:

1. Use the latest uuid library (v9.0.0+) to generate unique IDs for each file
2. Create ExtendedAttachment objects for each selected file
3. Add these objects to the attachments state
4. Add the IDs to an uploadQueue state

```tsx
import { v4 as uuidv4 } from 'uuid';

// Add to component state
const [uploadQueue, setUploadQueue] = useState<string[]>([]);

const handleFileSelection = (e: React.ChangeEvent<HTMLInputElement>) => {
  const files = e.target.files;
  if (!files || files.length === 0) return;
  
  const newAttachments: ExtendedAttachment[] = [];
  const newUploadQueue: string[] = [];
  
  Array.from(files).forEach(file => {
    const id = uuidv4();
    newUploadQueue.push(id);
    
    const attachment: ExtendedAttachment = {
      id,
      name: file.name,
      contentType: file.type,
      size: file.size,
      url: URL.createObjectURL(file),
      attachmentType: "file",
      fromMention: false,
      uploadState: "uploading"
    };
    
    newAttachments.push(attachment);
  });
  
  setAttachments(prev => [...prev, ...newAttachments]);
  setUploadQueue(prev => [...prev, ...newUploadQueue]);
  
  // Start uploading each file
  newAttachments.forEach((attachment, index) => {
    uploadFileToDrive(files[index], attachment.id!);
  });
  
  // Reset the file input
  e.target.value = '';
};
```

# Test Strategy:
1. Test that unique IDs are generated for each file
2. Verify ExtendedAttachment objects are created correctly with all required properties
3. Confirm files are added to the attachments state
4. Check that file IDs are added to the uploadQueue
5. Ensure the file input is reset after selection
