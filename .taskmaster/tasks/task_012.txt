# Task ID: 12
# Title: Implement File Upload Progress Indicator
# Status: done
# Dependencies: 4, 6
# Priority: medium
# Description: Add a visual progress indicator for file uploads to provide better feedback to users during the upload process.
# Details:
Enhance the file upload process with a progress indicator:

1. First, update the `uploadFileToDrive` function to track upload progress:

```typescript
const uploadFileToDrive = async (file: File, localAttachmentId: string) => {
  const controller = new AbortController();
  
  // Store the controller
  setAbortControllers(prev => ({
    ...prev,
    [localAttachmentId]: controller
  }));
  
  try {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('localAttachmentId', localAttachmentId);
    formData.append('fileName', file.name);
    formData.append('contentType', file.type);
    formData.append('chatId', chatId);

    // Use XMLHttpRequest for upload progress tracking
    await new Promise<void>((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      
      // Track upload progress
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 100);
          
          // Update attachment with progress
          setAttachments(prev => 
            prev.map(att => 
              att.id === localAttachmentId 
                ? { ...att, uploadProgress: progress } 
                : att
            )
          );
        }
      });
      
      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          const response = JSON.parse(xhr.responseText);
          
          // Update attachment with Google Drive URL
          setAttachments(prev => 
            prev.map(att => 
              att.id === localAttachmentId 
                ? { 
                    ...att, 
                    url: response.url, 
                    uploadState: 'uploaded',
                    uploadProgress: 100 
                  } 
                : att
            )
          );
          
          // Remove from upload queue
          setUploadQueue(prev => prev.filter(id => id !== localAttachmentId));
          
          resolve();
        } else {
          reject(new Error(`HTTP error ${xhr.status}`));
        }
      });
      
      xhr.addEventListener('error', () => {
        reject(new Error('Network error'));
      });
      
      xhr.addEventListener('abort', () => {
        reject(new Error('Upload aborted'));
      });
      
      xhr.open('POST', '/api/files/upload-to-drive');
      xhr.send(formData);
      
      // Connect abort controller to XHR
      controller.signal.addEventListener('abort', () => {
        xhr.abort();
      });
    });
    
    // Clean up controller after successful upload
    setAbortControllers(prev => {
      const newControllers = { ...prev };
      delete newControllers[localAttachmentId];
      return newControllers;
    });
    
  } catch (error) {
    // Handle errors as before
    // ...
  }
};
```

2. Update the `ExtendedAttachment` interface to include the progress field:

```typescript
export interface ExtendedAttachment extends Attachment {
  id?: string;
  attachmentType?: "file" | "ticket";
  size?: number;
  supabasePath?: string;
  ticketData?: JiraTask;
  fromMention?: boolean;
  uploadState?: "uploading" | "uploaded" | "failed";
  uploadProgress?: number; // New field for tracking progress percentage
}
```

3. Update the `PreviewAttachment` component to show the progress:

```tsx
// In the PreviewAttachment component
{isUploading && attachment.uploadProgress !== undefined && (
  <div className="relative w-full h-1 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden mt-1">
    <div 
      className="absolute top-0 left-0 h-full bg-blue-500 dark:bg-blue-400 transition-all duration-300"
      style={{ width: `${attachment.uploadProgress}%` }}
    />
  </div>
)}
```

# Test Strategy:
1. Test that upload progress is correctly tracked and displayed
2. Verify the progress bar updates in real-time during uploads
3. Confirm the progress reaches 100% when uploads complete
4. Test with files of various sizes to ensure progress tracking works correctly
5. Check that the progress indicator is visually clear and intuitive
