# Task ID: 14
# Title: Implement Message Display with File Attachments
# Status: done
# Dependencies: 10, 13
# Priority: medium
# Description: Update the message display components to show file attachments in the chat history.
# Details:
Modify the message display components to show file attachments in the chat history:

1. Update the `Message` component (or create a new component) to display file attachments:

```tsx
// In components/message.tsx or a similar file
import { Message as MessageType } from 'ai';
import { Attachment } from './multimodal/types';

interface MessageProps {
  message: MessageType & { attachments?: Attachment[] };
  // ... other props
}

export function Message({ message, ...props }: MessageProps) {
  const hasAttachments = message.attachments && message.attachments.length > 0;
  
  return (
    <div className="message">
      {/* Existing message content */}
      <div className="message-content">
        {message.content}
      </div>
      
      {/* Attachments section */}
      {hasAttachments && (
        <div className="message-attachments mt-2">
          <div className="flex flex-wrap gap-2">
            {message.attachments!.map((attachment, index) => (
              <MessageAttachment key={index} attachment={attachment} />
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

// Component for displaying attachments in messages
function MessageAttachment({ attachment }: { attachment: Attachment }) {
  // Handle different file types
  const isImage = attachment.contentType?.startsWith('image/');
  
  if (isImage && attachment.url) {
    return (
      <a 
        href={attachment.url} 
        target="_blank" 
        rel="noopener noreferrer"
        className="block max-w-xs rounded overflow-hidden border border-gray-200 dark:border-gray-700 hover:opacity-90 transition-opacity"
      >
        <img 
          src={attachment.url} 
          alt={attachment.name || 'Image'} 
          className="max-h-48 object-contain"
        />
        <div className="p-2 text-sm truncate">{attachment.name}</div>
      </a>
    );
  }
  
  // For non-image files
  return (
    <a 
      href={attachment.url} 
      target="_blank" 
      rel="noopener noreferrer"
      className="flex items-center gap-2 p-2 rounded border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
    >
      <div className="w-8 h-8 flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded">
        <DocumentIcon className="h-4 w-4 text-gray-500 dark:text-gray-400" />
      </div>
      <span className="text-sm truncate max-w-[150px]">{attachment.name || 'File'}</span>
    </a>
  );
}
```

2. Ensure the Message component is properly used in the chat display.

# Test Strategy:
1. Test displaying messages with various types of file attachments
2. Verify image files are displayed as thumbnails
3. Confirm non-image files show appropriate icons
4. Test that clicking on attachments opens them in a new tab
5. Check that the attachment display is responsive on different screen sizes
6. Verify that messages without attachments display correctly
