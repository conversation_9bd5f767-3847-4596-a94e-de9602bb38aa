# Task ID: 13
# Title: Integrate File Attachments with Chat Component
# Status: done
# Dependencies: 2, 5, 8
# Priority: high
# Description: Update the Chat component to properly pass attachments and upload queue state to the MultimodalInput component.
# Details:
Modify the `Chat` component in `components/chat.tsx` to manage attachments and pass them to `MultimodalInput`:

```tsx
// In components/chat.tsx

// Add state for attachments
const [attachments, setAttachments] = useState<ExtendedAttachment[]>([]);

// In the useChat hook initialization
const { messages, append, reload, stop, isLoading, input, setInput } = useChat({
  // ... existing options ...
  onFinish: () => {
    // Clear attachments after message is sent and AI responds
    setAttachments([]);
  },
  experimental_prepareRequestBody: (data) => {
    // Include attachments in the request
    return {
      messages: data.messages,
      experimental_attachments: attachments
        .filter(att => att.uploadState === "uploaded" && att.attachmentType === "file")
        .map(att => ({
          name: att.name,
          contentType: att.contentType,
          url: att.url
        })),
      // ... other properties
    };
  }
});

// Handle form submission
const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
  e.preventDefault();
  
  // Don't submit if there are ongoing uploads
  if (uploadQueue.length > 0) return;
  
  // Append message with attachments
  append({
    content: input,
    role: "user",
    attachments: attachments.filter(att => att.uploadState === "uploaded")
  });
  
  // Clear input and attachments
  setInput("");
  setAttachments([]);
};

// Pass attachments to MultimodalInput
<form onSubmit={handleSubmit}>
  <MultimodalInput
    input={input}
    setInput={setInput}
    isLoading={isLoading}
    attachments={attachments}
    setAttachments={setAttachments}
    chatId={chatId} // Assuming this is available
  />
</form>
```

Update the `MultimodalInput` component props to accept these new props:

```tsx
interface MultimodalInputProps {
  input: string;
  setInput: (input: string) => void;
  isLoading: boolean;
  attachments: ExtendedAttachment[];
  setAttachments: React.Dispatch<React.SetStateAction<ExtendedAttachment[]>>;
  chatId: string;
}
```

# Test Strategy:
1. Verify attachments are correctly passed between Chat and MultimodalInput
2. Test that only uploaded files are included in the message submission
3. Confirm attachments are cleared after a message is sent
4. Check that the form submission is prevented when uploads are in progress
5. Test the integration with the useChat hook's experimental_prepareRequestBody function
