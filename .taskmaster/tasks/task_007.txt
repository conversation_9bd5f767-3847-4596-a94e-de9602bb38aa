# Task ID: 7
# Title: Implement File Removal Functionality
# Status: done
# Dependencies: 2, 4
# Priority: medium
# Description: Create a function to remove uploaded or uploading files from the attachments list and cancel ongoing uploads if necessary.
# Details:
Implement the file removal functionality in `components/multimodal/multimodal-input.tsx`:

```typescript
// Add to component state
const [abortControllers, setAbortControllers] = useState<Record<string, AbortController>>({});

// Update uploadFileToDrive to use AbortController
const uploadFileToDrive = async (file: File, localAttachmentId: string) => {
  const controller = new AbortController();
  
  // Store the controller
  setAbortControllers(prev => ({
    ...prev,
    [localAttachmentId]: controller
  }));
  
  try {
    // ... existing code ...
    
    const response = await fetch('/api/files/upload-to-drive', {
      method: 'POST',
      body: formData,
      signal: controller.signal
    });
    
    // ... existing code ...
    
    // Clean up controller after successful upload
    setAbortControllers(prev => {
      const newControllers = { ...prev };
      delete newControllers[localAttachmentId];
      return newControllers;
    });
    
  } catch (error) {
    // Check if this is an abort error
    if (error.name === 'AbortError') {
      console.log('Upload cancelled');
      return;
    }
    
    // ... existing error handling ...
    
    // Clean up controller after failed upload
    setAbortControllers(prev => {
      const newControllers = { ...prev };
      delete newControllers[localAttachmentId];
      return newControllers;
    });
  }
};

// Implement the remove attachment function
const handleRemoveAttachment = (id: string) => {
  // If the file is currently uploading, abort the upload
  if (uploadQueue.includes(id) && abortControllers[id]) {
    abortControllers[id].abort();
    
    // Clean up controller
    setAbortControllers(prev => {
      const newControllers = { ...prev };
      delete newControllers[id];
      return newControllers;
    });
    
    // Remove from upload queue
    setUploadQueue(prev => prev.filter(queueId => queueId !== id));
  }
  
  // Remove from attachments
  setAttachments(prev => prev.filter(att => att.id !== id));
};

// Pass this function to AttachmentsPreviewMemo
<AttachmentsPreviewMemo 
  attachments={attachments} 
  onRemove={handleRemoveAttachment} 
  uploadQueue={uploadQueue} 
/>
```

# Test Strategy:
1. Test removing files that are not yet uploaded
2. Verify that removing an uploading file cancels the upload request
3. Confirm the attachment is removed from the UI
4. Test that the upload queue is properly updated
5. Check that the abort controller is cleaned up correctly
