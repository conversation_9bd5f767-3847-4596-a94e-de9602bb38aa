# Task ID: 11
# Title: Implement File Size and Type Validation
# Status: done
# Dependencies: 2
# Priority: medium
# Description: Add validation for file size and type to prevent uploads of unsupported or excessively large files.
# Details:
Implement file validation in the `handleFileSelection` function:

```typescript
// Constants for file validation
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const SUPPORTED_FILE_TYPES = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'application/pdf',
  'text/plain',
  'text/csv',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // docx
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // xlsx
  // Add more supported types as needed
];

const handleFileSelection = (e: React.ChangeEvent<HTMLInputElement>) => {
  const files = e.target.files;
  if (!files || files.length === 0) return;
  
  const newAttachments: ExtendedAttachment[] = [];
  const newUploadQueue: string[] = [];
  const invalidFiles: { file: File; reason: string }[] = [];
  
  Array.from(files).forEach(file => {
    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      invalidFiles.push({ 
        file, 
        reason: `File size exceeds the maximum limit of ${formatFileSize(MAX_FILE_SIZE)}` 
      });
      return;
    }
    
    // Validate file type
    if (!SUPPORTED_FILE_TYPES.includes(file.type)) {
      invalidFiles.push({ 
        file, 
        reason: `File type '${file.type || 'unknown'}' is not supported` 
      });
      return;
    }
    
    // Process valid file
    const id = uuidv4();
    newUploadQueue.push(id);
    
    const attachment: ExtendedAttachment = {
      id,
      name: file.name,
      contentType: file.type,
      size: file.size,
      url: URL.createObjectURL(file),
      attachmentType: "file",
      fromMention: false,
      uploadState: "uploading"
    };
    
    newAttachments.push(attachment);
  });
  
  // Show errors for invalid files
  if (invalidFiles.length > 0) {
    invalidFiles.forEach(({ file, reason }) => {
      toast.error(
        <div>
          <p className="font-medium">Cannot upload {file.name}</p>
          <p className="text-sm">{reason}</p>
        </div>
      );
    });
  }
  
  // Only proceed if there are valid files
  if (newAttachments.length > 0) {
    setAttachments(prev => [...prev, ...newAttachments]);
    setUploadQueue(prev => [...prev, ...newUploadQueue]);
    
    // Start uploading each valid file
    newAttachments.forEach((attachment, index) => {
      const fileIndex = Array.from(files).findIndex(
        f => f.name === attachment.name && f.size === attachment.size
      );
      if (fileIndex !== -1) {
        uploadFileToDrive(files[fileIndex], attachment.id!);
      }
    });
  }
  
  // Reset the file input
  e.target.value = '';
};
```

# Test Strategy:
1. Test uploading files that exceed the size limit
2. Test uploading files with unsupported types
3. Verify error messages are displayed for invalid files
4. Confirm that valid files are still processed correctly
5. Test with a mix of valid and invalid files in a single selection
