# Task ID: 1
# Title: Create File Upload UI Component
# Status: done
# Dependencies: None
# Priority: high
# Description: Implement a file upload button (paperclip icon) in the MultimodalInput component that allows users to select files from their local computer.
# Details:
Modify `components/multimodal/multimodal-input.tsx` to add a file upload button:
1. Use React's useRef hook to create a hidden file input element
2. Create a paperclip icon button that triggers the hidden file input when clicked
3. Set the file input to accept multiple files
4. Style the button to match the existing UI design

Example implementation:
```tsx
import { PaperclipIcon } from '@heroicons/react/24/outline';

// Inside MultimodalInput component
const fileInputRef = useRef<HTMLInputElement>(null);

const handleFileButtonClick = () => {
  fileInputRef.current?.click();
};

// In the JSX return
<button
  type="button"
  onClick={handleFileButtonClick}
  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
  aria-label="Upload file"
>
  <PaperclipIcon className="h-5 w-5" />
</button>
<input
  type="file"
  ref={fileInputRef}
  className="hidden"
  multiple
  onChange={handleFileSelection}
/>
```

# Test Strategy:
1. Verify the file upload button appears in the chat input area
2. Confirm clicking the button opens the file selection dialog
3. Test that the file input accepts multiple files
4. Ensure the button is properly styled and accessible
