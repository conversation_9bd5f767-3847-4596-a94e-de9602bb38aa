# Task ID: 16
# Title: Migrate RAG embedding from PostgreSQL to Qdrant
# Status: pending
# Dependencies: None
# Priority: high
# Description: Refactor the RAG implementation to use Qdrant as the vector store instead of PostgreSQL with pgvector. This involves updating the similarity search logic, adding a Qdrant client, and planning for data migration.
# Details:


# Test Strategy:


# Subtasks:
## 1. Install Qdrant client library [done]
### Dependencies: None
### Description: Install the @qdrant/js-client package using npm.
### Details:


## 2. Set up Qdrant client and configuration [done]
### Dependencies: None
### Description: Create a new file for the Qdrant client instance, and add configuration for it, likely from environment variables for the Qdrant URL and API key.
### Details:


## 3. Refactor findRelevantContent to use Qdrant [done]
### Dependencies: None
### Description: Modify the findRelevantContent function in lib/ai/embedding.ts to perform similarity search using the new Qdrant client instead of PostgreSQL.
### Details:


## 4. Create data migration script from PostgreSQL to Qdrant [done]
### Dependencies: None
### Description: Create a standalone script to read all existing documents and their embeddings from the PostgreSQL database and upsert them into the Qdrant collection.
### Details:


## 5. Update document ingestion to use Qdrant [done]
### Dependencies: None
### Description: Find the code responsible for adding new documents to the database, and modify it to also upsert the document and its embedding into Qdrant.
### Details:


## 6. Remove old PostgreSQL RAG logic and dependencies [in-progress]
### Dependencies: None
### Description: Once the Qdrant implementation is complete and verified, remove the old PostgreSQL-based RAG logic from lib/ai/embedding.ts and uninstall any related, now unused, packages.
### Details:


