# Task ID: 6
# Title: Enhance PreviewAttachment Component
# Status: done
# Dependencies: 3, 5
# Priority: medium
# Description: Update the PreviewAttachment component to display upload status indicators and handle file previews.
# Details:
Modify the `PreviewAttachment` component to show upload status and appropriate file previews:

```tsx
// In components/multimodal/preview-attachment.tsx
import { LoaderIcon, XIcon } from 'lucide-react';
import { ExtendedAttachment } from './types';

interface PreviewAttachmentProps {
  attachment: ExtendedAttachment;
  onRemove: (id: string) => void;
  isUploading?: boolean; // New prop
}

export function PreviewAttachment({ attachment, onRemove, isUploading = false }: PreviewAttachmentProps) {
  const handleRemove = () => {
    if (attachment.id) {
      onRemove(attachment.id);
    } else if (attachment.url) {
      onRemove(attachment.url);
    }
  };

  // Determine file type icon based on contentType
  const getFileIcon = () => {
    if (attachment.contentType?.startsWith('image/')) {
      return (
        <div className="w-10 h-10 overflow-hidden rounded">
          <img 
            src={attachment.url} 
            alt={attachment.name || 'Image'} 
            className="w-full h-full object-cover"
          />
        </div>
      );
    }
    
    // Add more file type icons as needed
    return (
      <div className="w-10 h-10 flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded">
        <DocumentIcon className="h-6 w-6 text-gray-500 dark:text-gray-400" />
      </div>
    );
  };

  return (
    <div className={`flex items-center gap-2 p-2 rounded border ${attachment.uploadState === 'failed' ? 'border-red-300 bg-red-50 dark:bg-red-900/20' : 'border-gray-200 dark:border-gray-700'}`}>
      {getFileIcon()}
      
      <div className="flex flex-col overflow-hidden">
        <span className="text-sm font-medium truncate max-w-[150px]">
          {attachment.name || 'File'}
        </span>
        <span className="text-xs text-gray-500 dark:text-gray-400">
          {attachment.size ? formatFileSize(attachment.size) : ''}
        </span>
      </div>
      
      <div className="flex items-center ml-2">
        {isUploading ? (
          <LoaderIcon className="h-4 w-4 animate-spin text-gray-500 dark:text-gray-400" />
        ) : attachment.uploadState === 'failed' ? (
          <span className="text-xs text-red-500 mr-2">Failed</span>
        ) : null}
        
        <button
          type="button"
          onClick={handleRemove}
          className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          aria-label="Remove attachment"
        >
          <XIcon className="h-4 w-4" />
        </button>
      </div>
    </div>
  );
}

// Helper function to format file size
function formatFileSize(bytes: number): string {
  if (bytes < 1024) return bytes + ' B';
  if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
  return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
}

// You'll need to import or create this icon
function DocumentIcon(props) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
    </svg>
  );
}
```

# Test Strategy:
1. Test rendering different file types (images, documents, etc.)
2. Verify the upload status indicator appears correctly during uploads
3. Test the failed upload state styling
4. Confirm the remove button works correctly
5. Check that file size is formatted properly
6. Test with various screen sizes to ensure responsive behavior
