# Task ID: 10
# Title: Update useChat Hook for File Attachments
# Status: done
# Dependencies: 3, 4
# Priority: high
# Description: Modify the useChat hook to properly prepare file attachments for sending to the AI model.
# Details:
Update the `useChat` hook in `components/chat.tsx` to properly prepare file attachments for the AI model:

```typescript
// In the experimental_prepareRequestBody function
const experimental_prepareRequestBody = useCallback(
  (data: { messages: Message[]; attachments?: ExtendedAttachment[] }) => {
    // Filter for successfully uploaded file attachments
    const successfullyUploadedFiles = (data.attachments || []).filter(
      (att) => att.uploadState === "uploaded" && att.attachmentType === "file"
    );
    
    // Transform to the format expected by the API
    const formattedAttachments = successfullyUploadedFiles.map((att) => ({
      name: att.name,
      contentType: att.contentType,
      url: att.url,
      // Include any other fields required by the base Attachment type
    }));
    
    return {
      messages: data.messages,
      experimental_attachments: formattedAttachments.length > 0 ? formattedAttachments : undefined,
      // ... other properties
    };
  },
  []
);
```

This ensures that only successfully uploaded files are included in the request to the AI model, and that they are formatted correctly according to the API's expectations.

# Test Strategy:
1. Test that only successfully uploaded files are included in the request
2. Verify the attachment format matches the API requirements
3. Check that files with failed uploads are excluded
4. Test with various combinations of file types and upload states
5. Ensure the experimental_attachments field is only included when there are valid attachments
