# Task ID: 8
# Title: Implement Send Button Disabling During Uploads
# Status: done
# Dependencies: 2, 4
# Priority: medium
# Description: Modify the chat send button to be disabled while files are being uploaded to prevent premature message sending.
# Details:
Update the send button in `components/multimodal/multimodal-input.tsx` to be disabled during file uploads:

```tsx
// In the MultimodalInput component

// Determine if the send button should be disabled
const isSendDisabled = isLoading || (uploadQueue.length > 0) || (!inputValue.trim() && attachments.length === 0);

// Update the send button
<Button
  type="submit"
  size="icon"
  disabled={isSendDisabled}
  className={cn(
    "shrink-0",
    isSendDisabled ? "opacity-50 cursor-not-allowed" : "opacity-100"
  )}
>
  <SendIcon className="h-4 w-4" />
  <span className="sr-only">Send message</span>
</Button>
```

This ensures that users cannot send messages while files are still being uploaded, preventing potential issues with incomplete file references.

# Test Strategy:
1. Verify the send button is disabled when files are uploading
2. Confirm the button is re-enabled when all uploads complete
3. Test that the button is disabled when uploads fail but others are still in progress
4. Check that the button is re-enabled when all uploads complete or fail
5. Ensure the visual styling correctly indicates the disabled state
