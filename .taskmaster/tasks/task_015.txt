# Task ID: 15
# Title: Implement End-to-End Testing
# Status: pending
# Dependencies: 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14
# Priority: low
# Description: Create comprehensive end-to-end tests for the file upload and chat functionality to ensure all components work together correctly.
# Details:
Implement end-to-end tests using <PERSON><PERSON> or <PERSON><PERSON> to test the complete file upload and chat functionality:

```typescript
// Using Playwright (example.spec.ts)
import { test, expect } from '@playwright/test';

test.describe('Chat with Uploaded Files', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the chat page
    await page.goto('/chat');
    // Wait for the page to load
    await page.waitForSelector('textarea[placeholder="Message"]');
  });

  test('should upload a file and send a message', async ({ page }) => {
    // Click the file upload button
    await page.click('button[aria-label="Upload file"]');
    
    // Select a file to upload
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('input[type="file"]');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles({
      name: 'test-file.txt',
      mimeType: 'text/plain',
      buffer: Buffer.from('This is a test file content')
    });
    
    // Wait for the upload to complete
    await page.waitForSelector('.message-attachments:not(:has(.animate-spin))');
    
    // Type a message
    await page.fill('textarea[placeholder="Message"]', 'Here is a file I want to discuss');
    
    // Send the message
    await page.click('button[type="submit"]');
    
    // Wait for the AI response
    await page.waitForSelector('.message[data-role="assistant"]');
    
    // Verify the file attachment is displayed in the message history
    const attachmentInHistory = await page.isVisible('.message-attachments a');
    expect(attachmentInHistory).toBeTruthy();
    
    // Verify the AI response mentions the file
    const aiResponse = await page.textContent('.message[data-role="assistant"]');
    expect(aiResponse).toContain('test-file.txt');
  });

  test('should handle file upload errors', async ({ page }) => {
    // Mock a failed upload response
    await page.route('/api/files/upload-to-drive', async (route) => {
      await route.fulfill({
        status: 500,
        body: JSON.stringify({ message: 'Server error during upload' })
      });
    });
    
    // Click the file upload button and select a file
    await page.click('button[aria-label="Upload file"]');
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('input[type="file"]');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles({
      name: 'test-file.txt',
      mimeType: 'text/plain',
      buffer: Buffer.from('This is a test file content')
    });
    
    // Wait for the error toast to appear
    await page.waitForSelector('div[role="status"]');
    const toastText = await page.textContent('div[role="status"]');
    expect(toastText).toContain('Failed to upload');
    
    // Verify the send button is re-enabled
    const isButtonDisabled = await page.isDisabled('button[type="submit"]');
    expect(isButtonDisabled).toBeFalsy();
  });

  test('should remove an uploaded file', async ({ page }) => {
    // Upload a file first
    await page.click('button[aria-label="Upload file"]');
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('input[type="file"]');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles({
      name: 'test-file.txt',
      mimeType: 'text/plain',
      buffer: Buffer.from('This is a test file content')
    });
    
    // Wait for the upload to complete
    await page.waitForSelector('.message-attachments:not(:has(.animate-spin))');
    
    // Count the number of attachments
    const initialAttachmentCount = await page.locator('.message-attachments a').count();
    
    // Click the remove button on the attachment
    await page.click('button[aria-label="Remove attachment"]');
    
    // Verify the attachment was removed
    const finalAttachmentCount = await page.locator('.message-attachments a').count();
    expect(finalAttachmentCount).toBe(initialAttachmentCount - 1);
  });
});
```

This test suite covers the main user flows for the file upload feature, including successful uploads, error handling, and file removal.

# Test Strategy:
1. Run the end-to-end tests in a CI/CD pipeline
2. Test on multiple browsers (Chrome, Firefox, Safari) to ensure cross-browser compatibility
3. Test with various file types and sizes
4. Verify all user flows work as expected
5. Test edge cases such as network interruptions during upload
6. Ensure the tests are reliable and don't produce false positives/negatives
