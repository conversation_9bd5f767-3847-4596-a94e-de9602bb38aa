import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // experimental: {
  //   ppr: true,
  // },
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    remotePatterns: [
      {
        hostname: "avatar.vercel.sh",
      },
      {
        hostname: "t7dm8fxiewn5snhb.public.blob.vercel-storage.com",
      },
      {
        hostname: "drive.google.com",
      },
      {
        hostname: "*.googleusercontent.com",
      },
      {
        hostname: "dvjzjxutvmmhvcxdgkwr.supabase.co",
      },
    ],
  },
  env: {
    NEXT_PUBLIC_JIRA_BASE_URL: process.env.JIRA_BASE_URL,
  },
};

export default nextConfig;
