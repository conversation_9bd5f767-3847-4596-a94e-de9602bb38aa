---
description: 
globs: 
alwaysApply: false
---
# Full Database Schema Reference

This rule documents the complete database structure for the AI Chatbot, including all tables, columns, types, relationships, RLS policies, and Supabase/Drizzle helper functions.

## Tables & Columns

### public.users
- **Source**: [0007_add_users_and_alter_chat.sql](mdc:lib/db/migrations/0007_add_users_and_alter_chat.sql), [0008_fix_users_type.sql](mdc:lib/db/migrations/0008_fix_users_type.sql)
- id: TEXT PRIMARY KEY
- email: VARCHAR(255) NOT NULL
- name: VARCHAR(255)
- departments: TEXT[] NOT NULL DEFAULT '{}'
- created_at: TIMESTAMPTZ NOT NULL DEFAULT NOW()
- updated_at: TIMESTAMPTZ NOT NULL DEFAULT NOW()

### public.Chat
- **Source**: [schema.ts](mdc:lib/db/schema.ts)
- id: UUID PRIMARY KEY DEFAULT gen_random_uuid()
- createdAt: TIMESTAMP NOT NULL
- userId: TEXT NOT NULL  
  ↳ FK → public.users(id)
- title: TEXT NOT NULL
- visibility: VARCHAR ENUM ["public","private"] NOT NULL DEFAULT "private"

### public.Message_v2
- **Source**: [schema.ts](mdc:lib/db/schema.ts)
- id: UUID PRIMARY KEY DEFAULT gen_random_uuid()
- chatId: UUID NOT NULL  
  ↳ FK → public.Chat(id)
- role: VARCHAR NOT NULL
- parts: JSON NOT NULL
- attachments: JSON NOT NULL
- createdAt: TIMESTAMP NOT NULL

### public.Vote_v2
- **Source**: [schema.ts](mdc:lib/db/schema.ts)
- chatId: UUID NOT NULL  
  ↳ FK → public.Chat(id)
- messageId: UUID NOT NULL  
  ↳ FK → public.Message_v2(id)
- isUpvoted: BOOLEAN NOT NULL
- PRIMARY KEY(chatId, messageId)

### public.Document
- **Source**: [schema.ts](mdc:lib/db/schema.ts)
- id: UUID PRIMARY KEY DEFAULT gen_random_uuid()
- createdAt: TIMESTAMP NOT NULL
- title: TEXT NOT NULL
- content: TEXT
- kind: VARCHAR ENUM ["text","code","image","sheet"] NOT NULL DEFAULT "text"
- userId: TEXT NOT NULL  
  ↳ FK → public.users(id)
- metadata: JSONB NOT NULL DEFAULT '{}'

### public.Suggestion
- **Source**: [schema.ts](mdc:lib/db/schema.ts)
- id: UUID PRIMARY KEY DEFAULT gen_random_uuid()
- documentId: UUID NOT NULL
- documentCreatedAt: TIMESTAMP NOT NULL
- originalText: TEXT NOT NULL
- suggestedText: TEXT NOT NULL
- description: TEXT
- isResolved: BOOLEAN NOT NULL DEFAULT false
- userId: TEXT NOT NULL
- createdAt: TIMESTAMP NOT NULL
- FK → (documentId, documentCreatedAt) → Document(id, createdAt)

### public.Stream
- **Source**: [schema.ts](mdc:lib/db/schema.ts)
- id: UUID PRIMARY KEY DEFAULT gen_random_uuid()
- chatId: UUID NOT NULL  
  ↳ FK → public.Chat(id)
- createdAt: TIMESTAMP NOT NULL

### public.department_leaders
- **Source**: [schema.ts](mdc:lib/db/schema.ts)
- department: TEXT NOT NULL
- user_id: TEXT NOT NULL
- PRIMARY KEY(department, user_id)

## Relationships & Constraints
- Chat.userId → users(id)
- Message_v2.chatId → Chat(id)
- Vote_v2.chatId → Chat(id)
- Vote_v2.messageId → Message_v2(id)
- Suggestion → Document via composite columns
- Stream.chatId → Chat(id)
- department_leaders join table between departments and users

## RLS Policies
- **Chat** (public."Chat"):
  - ENABLED RLS
  - SELECT/INSERT/UPDATE/DELETE only if userId = auth.uid()::text

- **Document** (public."Document"):
  - ENABLED RLS
  - SELECT if userId = auth.uid()::text OR user is in department via users.departments or department_leaders
  - INSERT/UPDATE/DELETE only if userId = auth.uid()::text (owners) or department leader via department_leaders

(See applied policies in your migrations via MCP.)

## Helper Functions

### lib/supabase/users.ts
- `upsertUserProfile({ id, email, name, departments })`  
  Upserts Clerk profile into public.users table.

### lib/db/schema.ts
- Drizzle ORM table definitions matching the above structure.

---
*End of Database Schema Reference*
