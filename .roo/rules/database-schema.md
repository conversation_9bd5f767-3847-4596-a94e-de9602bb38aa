---
description: Read this when interact with database
globs: 
alwaysApply: false
---
# Supabase PostgreSQL Database

This is a Supabase PostgreSQL database for a chat-based application with document management capabilities.

## Key Elements

- Clerk integration for user authentication
- Document management with metadata
- Chat system with message history
- Voting system for messages
- Team and department-based access control

## Main Tables

### User Management
- **user**: Core user information
  ```typescript
  {
    id: uuid (PK)
    clerkId: varchar(64)
    email: varchar(64)
    name: text
    departments: text[]
    createdAt: timestamp
    updatedAt: timestamp
  }
  ```

### Chat System
- **chat**: Chat sessions
  ```typescript
  {
    id: uuid (PK)
    createdAt: timestamp
    title: text
    userId: uuid (FK -> user.id)
    visibility: varchar ("public" | "private")
  }
  ```

- **message**: Chat messages
  ```typescript
  {
    id: uuid (PK)
    chatId: uuid (FK -> chat.id)
    role: varchar
    parts: json
    attachments: json
    createdAt: timestamp
  }
  ```

- **vote**: Message voting system
  ```typescript
  {
    chatId: uuid (FK -> chat.id)
    messageId: uuid (FK -> message.id)
    isUpvoted: boolean
    PK: [chatId, messageId]
  }
  ```

### Document Management
- **ai_document**: User-created/uploaded documents
  ```typescript
  {
    id: uuid
    createdAt: timestamp
    title: text
    content: text
    kind: varchar ("text" | "code" | "image" | "sheet")
    userId: uuid
    metadata: jsonb
    PK: [id, createdAt]
  }
  ```

- **document_metadata**: Document metadata management
  ```typescript
  {
    id: text (PK)
    name: text
    fileType: text
    createdAt: timestamp
    updatedAt: timestamp
    owner: text
    path: text
    permissions: text[]
    url: text
    department: text
    schema: text
  }
  ```

- **documents**: Vector-enabled document storage
  ```typescript
  {
    id: bigserial (PK)
    content: text
    metadata: jsonb
    embedding: vector(768)
    file_id: text
  }
  ```

- **document_rows**: Document row data
  ```typescript
  {
    id: serial (PK)
    dataset_id: text (FK -> document_metadata.id)
    row_data: jsonb
  }
  ```

### Suggestions System
- **suggestion**: Document improvement suggestions
  ```typescript
  {
    id: uuid (PK)
    documentId: uuid
    documentCreatedAt: timestamp
    originalText: text
    suggestedText: text
    description: text
    isResolved: boolean
    userId: uuid
    createdAt: timestamp
    FK: [documentId, documentCreatedAt] -> [ai_document.id, ai_document.createdAt]
  }
  ```

### Stream Management
- **stream**: Chat stream tracking
  ```typescript
  {
    id: uuid (PK)
    chatId: uuid (FK -> chat.id)
    createdAt: timestamp
  }
  ```

### Department Management
- **department_leaders**: Department leadership assignments
  ```typescript
  {
    department: text
    user_id: uuid
    PK: [department, user_id]
  }
  ```

### Memory System
- **memory**: User memory storage
  ```typescript
  {
    id: uuid (PK)
    content: text
    createdAt: timestamp
    updatedAt: timestamp
    userId: uuid
  }
  ```

## Security Notes

1. **Row Level Security (RLS)**
   - All tables should have appropriate RLS policies
   - Access should be restricted based on user ID and permissions
   - Department-based access control through user departments array

2. **Permissions**
   - Document access controlled via permissions array in document_metadata
   - Team-based access through department associations
   - Personal documents restricted to owner only

3. **Authentication**
   - Clerk handles user authentication
   - User ClerkID required for all authenticated operations
   - Email verification through Clerk system

## Type Definitions

The database types are defined in [lib/db/schema.ts](mdc:lib/db/schema.ts) using Drizzle ORM.

## Important Functions

- Document access is controlled through permissions arrays
- Vector similarity search_files available for document content
- Automatic timestamp management for created_at/updated_at fields

## Best Practices

1. **Data Access**
   - Always use the appropriate user context
   - Respect RLS policies in all queries
   - Use proper type definitions from schema.ts

2. **Document Management**
   - Store large content in ai_document
   - Keep metadata separate in document_metadata
   - Use vector embeddings for search_files functionality

3. **Error Handling**
   - Implement proper error handling for database operations
   - Validate input data before insertion
   - Handle permission checks explicitly
