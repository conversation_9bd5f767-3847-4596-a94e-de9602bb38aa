---
description: 
globs: 
alwaysApply: false
---
# File Manager Implementation Guidelines

- **File Manager Architecture**
  - Uses a modal dialog with tabs for company vs. personal files
  - Connects to two different data sources: `document_metadata` (company) and `ai_document` (personal)
  - Implements sorting, filtering, and search_files functionality
  - Handles file uploads with appropriate destination routing

- **API Routes**
  - Located in `app/(chat)/api/files/` directory
  - Each route follows a consistent response format matching the `FileMetadata` interface
  - Company file routes require user email from Clerk authentication
  - Personal file routes use Clerk userId
  
- **Type Definitions**
  ```typescript
  // Key interface for file metadata used throughout the system
  export interface FileMetadata {
    id: string;
    name: string;
    type: string;
    size: number;
    createdAt: string;
    updatedAt: string;
    source: "company" | "personal";
    permissions?: string[];
    url?: string;
  }
  ```

- **Authentication & Authorization**
  - Uses Clerk for user authentication
  - Company files are filtered by user's email in the `permissions` array
  - Personal files are filtered by `userId`
  - Always include proper auth checks in API routes:
  ```typescript
  // Required auth check in API routes
  const { userId } = await auth();
  const user = await currentUser(); // For email access
  
  if (!userId || !user?.emailAddresses?.[0]?.emailAddress) {
    return new NextResponse("Unauthorized", { status: 401 });
  }
  ```

- **Database Schema**
  - Company files: `document_metadata` table
    - `permissions`: Array of emails who can access this file
    - `fileType`: String describing file type
    - `url`: Location where file is stored
  - Personal files: `ai_document` table
    - `userId`: Links file to specific user
    - `kind`: Type of content ("text", "code", "image", "sheet")
    - `metadata`: JSON field containing additional file properties

- **Component Organization**
  - `FileManagerModal`: Main container with tabs and upload functionality
  - `FileList`: Reusable component for displaying files with sorting/filtering
  - `useFileManager`: Custom hook for state management and data fetching

- **Data Fetching Strategy**
  - Uses SWR for efficient caching and revalidation
  - Only fetches data for the active tab when modal is open
  - Provides loading and error states
  - Auto-refreshes after file operations

- **File Upload Implementation**
  - Uses FormData to send files to the server
  - Destination determined by active tab or explicit parameter
  - Proper typing of file metadata across stack
  - Provides user feedback during upload process

- **Error Handling Approach**
  - API routes use consistent error response format
  - Frontend displays appropriate error messages
  - Fallback values for missing properties
  - Toast notifications for user actions
