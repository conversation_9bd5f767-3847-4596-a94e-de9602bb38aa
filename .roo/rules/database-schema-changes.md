---
description: 
globs: 
alwaysApply: false
---
# Database Schema & Access Changes

This rule documents all migrations, schema adjustments, helper functions, and RLS policies applied to support Clerk authentication and department-based access in Supabase.

## Supabase Migrations

- **0007_add_users_and_alter_chat.sql**: Created `public.users` (id TEXT, email, name, departments TEXT[], timestamps) and altered `Chat.userId` → TEXT.
  ([source](mdc:lib/db/migrations/0007_add_users_and_alter_chat.sql))
- **0008_fix_users_type.sql**: Re-created `public.users`, altered `Document.userId` → TEXT, and added FK `Chat.userId` → `users.id`.
  ([source](mdc:lib/db/migrations/0008_fix_users_type.sql))
- **department_leaders**: Created join table for department leaders (`department_leaders`).
  (DDL applied via MCP)

## Drizzle ORM Schema Updates

- `lib/db/schema.ts`:
  - `Chat`: changed `userId` column to `text`.
  - `Document`: added `metadata` JSONB column for department tags.
  - Added `department_leaders` table definition.
  ([source](mdc:lib/db/schema.ts))

## Supabase Helper

- `lib/supabase/users.ts`: `upsertUserProfile` upserts Clerk profile (id, email, name, departments) into `users` table.
  ([source](mdc:lib/supabase/users.ts))

## Next.js API Route Changes

- `app/(chat)/api/history/route.ts`: After `auth()`, use `await clerkClient()` to fetch user, then call `upsertUserProfile`. Once synced, proceed with `getChatsByUserId`.
  ([source](mdc:app/(chat)/api/history/route.ts))

## New Tables & Columns

- **public.users**: Stores Clerk users (id TEXT, departments TEXT[]).
- **public.department_leaders**: Maps `department` → `user_id` (leaders).
- **Document.metadata**: JSONB blob containing `departments` array.

## Row-Level Security (RLS)

### Chat
- Enabled RLS on `Chat`:
  - SELECT/INSERT/UPDATE/DELETE only when `userId = auth.uid()::text`.

### Document
- Enabled RLS on `Document`:
  - **SELECT**: `userId = auth.uid()::text` OR user's `departments` intersect `metadata->'departments'`.
  - **INSERT**: Owner only and document's `metadata->'departments'` intersect user's `departments`.
  - **UPDATE/DELETE**: `userId = auth.uid()::text` OR user is listed in `department_leaders` for one of the document's departments.

Use this rule as a reference when writing queries or policies related to authentication and department-based access control.
