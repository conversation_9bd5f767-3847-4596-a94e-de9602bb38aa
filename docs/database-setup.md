# Database Setup Guide

This guide explains how to set up the PostgreSQL database for the Optimus Claude project.

## Prerequisites

- PostgreSQL with pgvector extension support
- Node.js and pnpm installed

## Quick Setup (Recommended)

### For macOS with Postgres.app:

1. **Install prerequisites automatically:**

   ```bash
   pnpm db:setup
   ```

2. **Configure database URL in `.env.local`:**

   ```bash
   # Create .env.local file with:
   POSTGRES_URL=postgresql://your_mac_username@localhost:5432/your_mac_username
   ```

3. **Run migrations:**
   ```bash
   pnpm db:migrate
   ```

## Manual Setup

### 1. Install pgvector

**macOS (with Homebrew):**

```bash
brew install pgvector
```

**Ubuntu/Debian:**

```bash
sudo apt-get install postgresql-14-pgvector
```

**Other systems:**
See [pgvector installation guide](https://github.com/pgvector/pgvector#installation)

### 2. Configure Database URL

Create a `.env.local` file in the project root:

```bash
# For Postgres.app (default setup)
POSTGRES_URL=postgresql://your_username@localhost:5432/your_database

# Examples:
# POSTGRES_URL=postgresql://john@localhost:5432/john
# POSTGRES_URL=postgresql://postgres:password@localhost:5432/optimus_db
```

### 3. Run Migrations

The migration script automatically:

- Creates required extensions (pgvector, uuid-ossp)
- Runs all database migrations
- Sets up the complete schema

```bash
pnpm db:migrate
```

## Database Schema

The project uses these key tables:

- `documents` - Document storage with vector embeddings
- `chat`, `message` - Chat and messaging system
- `user` - User management
- `mcp_config` - MCP (Model Context Protocol) configurations

## Troubleshooting

### Error: "type vector does not exist"

- Run `pnpm db:setup` to install pgvector
- Or manually: `CREATE EXTENSION IF NOT EXISTS vector;` in your database

### Connection issues

- Check if PostgreSQL is running
- Verify POSTGRES_URL format
- For Postgres.app, ensure it's started and accepting connections

### Permission issues

- For Postgres.app, check app permissions in settings
- Ensure your user has database creation privileges

## Development Commands

```bash
# Setup database prerequisites
pnpm db:setup

# Generate new migrations
pnpm db:generate

# Run migrations
pnpm db:migrate

# Open database studio
pnpm db:studio

# Push schema changes (development only)
pnpm db:push
```

## Production Notes

For production deployments:

- Use a managed PostgreSQL service with pgvector support (e.g., Supabase, Neon)
- Ensure SSL is properly configured
- Use connection pooling for better performance
- Backup your database regularly
