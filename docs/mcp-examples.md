# MCP Configuration Examples

This document provides examples of MCP (Model Context Protocol) server configurations for the AI chatbot.

## Firecrawl MCP Server (Stdio)

Web scraping and crawling capabilities:

```json
{
  "name": "Firecrawl",
  "transport": {
    "type": "stdio",
    "command": "npx",
    "args": ["-y", "firecrawl-mcp"]
  },
  "credential": "your-firecrawl-api-key"
}
```

## Memory MCP Server (Stdio)

Persistent memory capabilities:

```json
{
  "name": "Memory",
  "transport": {
    "type": "stdio",
    "command": "npx",
    "args": ["-y", "@modelcontextprotocol/server-memory"]
  }
}
```

## Filesystem MCP Server (Stdio)

File system operations:

```json
{
  "name": "Filesystem",
  "transport": {
    "type": "stdio",
    "command": "npx",
    "args": ["-y", "@modelcontextprotocol/server-filesystem", "/path/to/allowed/directory"]
  }
}
```

## PostgreSQL MCP Server (Stdio)

Database operations:

```json
{
  "name": "PostgreSQL",
  "transport": {
    "type": "stdio",
    "command": "npx",
    "args": ["-y", "@modelcontextprotocol/server-postgres"]
  },
  "credential": "postgresql://user:password@localhost:5432/dbname"
}
```

## Remote SSE Server Example

Custom remote MCP server:

```json
{
  "name": "Custom API",
  "transport": {
    "type": "sse",
    "url": "https://your-mcp-server.com/sse",
    "headers": {
      "Authorization": "Bearer your-api-key",
      "Content-Type": "application/json"
    }
  }
}
```

## Environment Variables

Some MCP servers require environment variables. The credential field can be used to pass:

- API keys
- Database connection strings
- Authentication tokens
- Other sensitive configuration

The credential will be passed to stdio servers via the `API_KEY` environment variable.

## Testing Your Configuration

1. Save your MCP configuration
2. Start a new chat conversation
3. The MCP tools should appear automatically
4. Test by asking questions that would use the MCP tools

## Common Issues

- **Stdio servers not starting**: Check that the command and args are correct
- **Permission errors**: Ensure the MCP server has necessary permissions
- **Network issues**: For SSE servers, verify the URL is accessible
- **API key errors**: Double-check credentials are correct

## Popular MCP Servers

- [Firecrawl](https://github.com/mendableai/firecrawl): Web scraping
- [Filesystem](https://github.com/modelcontextprotocol/servers): File operations
- [PostgreSQL](https://github.com/modelcontextprotocol/servers): Database access
- [Memory](https://github.com/modelcontextprotocol/servers): Persistent memory
- [Git](https://github.com/modelcontextprotocol/servers): Git operations
