# MCP Testing Guide

This guide helps you test and debug your MCP (Model Context Protocol) configurations.

## Quick Test Guide

### 1. **Save and Test Your Configuration**

1. Open **Tools** → **MCP Settings** in the chat interface
2. **Choose your configuration method:**
   - **Auto-detect (Recommended)**: Paste your JSON configuration and let it parse automatically
   - **Manual**: Fill in fields individually
3. **For Auto-detect mode:**
   - Paste your MCP configuration JSON (supports multiple formats)
   - Click **"Parse Configuration"**
   - Review the auto-filled fields and adjust if needed
   - **Environment variables are automatically extracted** from `env` objects
4. **For Manual mode:**
   - Fill in the transport type, commands, URLs, and credentials manually
   - **Add environment variables** using the "Add Variable" button with name-value pairs
5. Click **Save** to store your configuration
6. Click **"Test Active MCP"** button to verify it works
7. Check the results in both the UI toast and browser console

#### **Supported Auto-detect Formats:**

The auto-detect mode supports these JSON configuration formats:

**1. MCP Servers Format (Standard):**

```json
{
  "mcpServers": {
    "mcp-server-firecrawl": {
      "command": "npx",
      "args": ["-y", "firecrawl-mcp"],
      "env": {
        "FIRECRAWL_API_KEY": "your-api-key"
      }
    }
  }
}
```

**2. Transport Format:**

```json
{
  "transport": {
    "type": "stdio",
    "command": "npx",
    "args": ["-y", "@modelcontextprotocol/server-memory"]
  }
}
```

**3. Direct Server Config:**

```json
{
  "command": "npx",
  "args": ["-y", "firecrawl-mcp"],
  "env": {
    "FIRECRAWL_API_KEY": "your-api-key",
    "ANOTHER_KEY": "another-value"
  }
}
```

> **Note:** Environment variables from the `env` object are automatically parsed into separate name-value pairs in the manual configuration mode.

### 2. **Check Server Logs**

The development server shows detailed MCP logs with emoji indicators:

```
[MCP] 🔍 Checking MCP config for user abc123
[MCP] 📋 Found active config: "Firecrawl" (ID: def456)
[MCP] 🚀 Transport type: stdio
[MCP] 🖥️  Initializing stdio transport
[MCP] 📦 Command: npx
[MCP] 📝 Args: [-y, firecrawl-mcp]
[MCP] ✅ Successfully initialized stdio MCP client
[MCP] 🔧 Available tools: [crawl_website, search_web, scrape_url]
```

### 3. **Test in Chat**

1. Start a conversation that would benefit from your MCP tools
2. Check the logs for tool execution:

```
[TOOLS] 🎯 2 tool calls executed in this conversation:
[TOOLS] 1. 🌐 crawl_website (MCP)
[TOOLS] 2. 🔧 search_documents (Built-in)
```

## Common Issues & Solutions

### ❌ **"Command not found" (ENOENT Error)**

**Problem:** The MCP server executable isn't installed or accessible.

**Solutions:**

1. For npm packages: Run `npx -y package-name` manually to verify installation
2. For local executables: Check the full path exists
3. For Python scripts: Use `python` or `python3` as command with script as argument

**Example Fix:**

```json
// Instead of:
{"command": "my-mcp-server"}

// Use:
{"command": "npx", "args": ["-y", "my-mcp-server"]}
// or
{"command": "python3", "args": ["./my-mcp-server.py"]}
```

### ⚠️ **"No tools available"**

**Problem:** MCP server connects but provides no tools.

**Possible Reasons:**

1. **Normal behavior** - Some MCP servers only provide resources/prompts, not tools
2. **Initialization issue** - Server needs time to load or has startup errors
3. **API key missing** - Server requires authentication

**Solutions:**

1. Check the server's documentation for expected tools
2. Verify API credentials are correctly set
3. Look for error messages in server output

### 🌐 **SSE Transport Issues**

**Problem:** SSE (Server-Sent Events) connection fails.

**Common fixes:**

1. Verify the URL is accessible: `curl https://your-server.com/sse`
2. Check CORS settings on the MCP server
3. Verify headers format:

```json
{
  "transport": {
    "type": "sse",
    "url": "https://your-server.com/sse",
    "headers": {
      "Authorization": "Bearer your-api-key",
      "Content-Type": "application/json"
    }
  }
}
```

### 🔒 **Permission Denied**

**Problem:** System prevents executing the MCP server command.

**Solutions:**

1. On macOS/Linux: `chmod +x /path/to/mcp-server`
2. For npm global packages: Ensure npm global bin is in PATH
3. Use full paths when possible

## Debugging Steps

### 1. **Test the MCP Server Directly**

Before configuring in the chat, test the server manually:

```bash
# For stdio servers
npx -y firecrawl-mcp

# For Python servers
python3 my-mcp-server.py

# For local executables
./my-mcp-server --help
```

### 2. **Check Network Connectivity**

For SSE servers:

```bash
curl -v https://your-mcp-server.com/sse
```

### 3. **Verify Configuration Format**

Use the test endpoint directly:

```bash
curl -X POST http://localhost:3000/api/mcp/test \
  -H "Authorization: Bearer your-session-token"
```

### 4. **Monitor Browser Console**

Open Developer Tools → Console to see detailed MCP logs:

- `[MCP]` prefixed logs show server initialization
- `[MCP-TEST]` prefixed logs show test results
- `[TOOLS]` prefixed logs show tool execution

## Example Working Configurations

### Firecrawl (Web Scraping)

```json
{
  "name": "Firecrawl",
  "transport": {
    "type": "stdio",
    "command": "npx",
    "args": ["-y", "firecrawl-mcp"]
  },
  "credential": "your-firecrawl-api-key"
}
```

### Memory Server

```json
{
  "name": "Memory",
  "transport": {
    "type": "stdio",
    "command": "npx",
    "args": ["-y", "@modelcontextprotocol/server-memory"]
  }
}
```

### Custom SSE Server

```json
{
  "name": "Custom API",
  "transport": {
    "type": "sse",
    "url": "https://api.example.com/mcp",
    "headers": {
      "Authorization": "Bearer your-api-key"
    }
  }
}
```

## Success Indicators

### ✅ **Everything Working**

- Test button shows: `✅ MCP "ServerName" works! Found 3 tools: tool1, tool2, tool3`
- Chat logs show: `[MCP] 🎉 Successfully loaded X MCP tools`
- Tools appear in chat conversations when relevant
- No error messages in console

### 🛠️ **Partial Success**

- Server connects but no tools: May be normal for resource-only servers
- Some tools fail: Check individual tool requirements

### 🆘 **Need Help?**

1. Check this guide's solutions for your specific error
2. Review the MCP server's documentation
3. Test the server outside the chat first
4. Verify all configuration details match the server's requirements
