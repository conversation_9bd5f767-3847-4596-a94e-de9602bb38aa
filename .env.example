# Generate a random secret: https://generate-secret.vercel.app/32 or `openssl rand -base64 32`
AUTH_SECRET=*****
NEXTAUTH_SECRET=*****
NEXTAUTH_URL=*****
# The following keys below are automatically created and
# added to your environment when you deploy on vercel

# Get your xAI API Key here for chat and image models: https://console.x.ai/
XAI_API_KEY=*****
GROQ_API_KEY=*****
GOOGLE_GENERATIVE_AI_API_KEY=*****
ANTHROPIC_API_KEY=*****
PERPLEXITY_API_KEY=*****

# Instructions to create a Vercel Blob Store here: https://vercel.com/docs/storage/vercel-blob
BLOB_READ_WRITE_TOKEN=*****

# Instructions to create a database here: https://vercel.com/docs/storage/vercel-postgres/quickstart
POSTGRES_URL=*****

NEXT_PUBLIC_SUPABASE_URL=*****
NEXT_PUBLIC_SUPABASE_ANON_KEY=*****
            
SUPABASE_URL=*****
SUPABASE_ANON_KEY=*****
SUPABASE_SERVICE_ROLE_KEY=*****
SUPABASE_JWT_SECRET=*****


AUTH_GOOGLE_ID=*****
AUTH_GOOGLE_SECRET=*****

NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=*****
CLERK_SECRET_KEY=*****
NEXT_PUBLIC_CLERK_SIGN_IN_URL=*****

REDIS_URL=*****

JIRA_BASE_URL=*****
JIRA_ADMIN_EMAIL=*****
JIRA_API_TOKEN=*****