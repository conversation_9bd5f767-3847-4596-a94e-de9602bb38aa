import { SiteHeader } from "@/components/dashboard/side-header";
import Dashboard from "@/components/workflow/dashboard";

const Page = () => {
  return (
    <>
      <SiteHeader title="Workflows" />
      <div className="flex flex-1 flex-col">
        <div className="@container/main flex flex-1 flex-col gap-2">
          <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
            <Dashboard />
            {/* <div className="flex flex-col items-center justify-center gap-2 mt-[40dvh]">
              <div className="text-2xl font-bold">Comming soon</div>
              <div className="text-sm text-muted-foreground">We are working on this feature. Please check back later.</div>
            </div> */}
          </div>
        </div>
      </div>
    </>
  );
};

export default Page;
