"use client";

import { SignIn, useClerk } from "@clerk/nextjs";
import { useEffect } from "react";

export default function Page() {
  // useClerk returns the Clerk instance (may be a proxy before loaded)
  const clerk = useClerk();

  // After a successful sign-in, notify the extension so it can refresh the sidepanel
  useEffect(() => {
    if (!clerk.loaded) return;

    const unsubscribe = clerk?.addListener?.((emission: any) => {
      if (emission?.session) {
        // Broadcast an auth-update event to any Optimus content script
        window.postMessage({ source: "optimus", type: "OPTIMUS_AUTH_UPDATE" }, "*");
      }
    });

    return () => {
      if (typeof unsubscribe === "function") unsubscribe();
    };
  }, [clerk]);

  return (
    <div className="flex h-dvh w-screen items-start pt-12 md:pt-0 md:items-center justify-center bg-background">
      <div className="w-full max-w-md overflow-hidden rounded-2xl flex flex-col gap-12">
        <SignIn forceRedirectUrl="/" />
      </div>
    </div>
  );
}
