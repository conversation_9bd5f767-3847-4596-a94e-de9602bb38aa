import { Chat } from "@/components/chat";
import { DataStreamHandler } from "@/components/data-stream-handler";
import { DEFAULT_CHAT_MODEL } from "@/lib/ai/models";
import { generateUUID } from "@/lib/utils";
import { Suspense } from "react";

export const dynamic = "force-dynamic";

export default function Page() {
  const id = generateUUID();

  return (
    <>
      <Suspense fallback={<div>Loading...</div>}>
        <Chat
          id={id}
          key={id}
          initialMessages={[]}
          initialChatModel={DEFAULT_CHAT_MODEL}
          initialVisibilityType="private"
          isReadonly={false}
          autoResume={false}
        />
      </Suspense>
      <DataStreamHandler id={id} />
    </>
  );
}
