"use client";

import Link from "next/link";

// import { useEffect } from "react";
// import { useRouter } from "next/navigation";

export default function NotFound() {
  // const router = useRouter();

  // useEffect(() => {
  //   // Redirect to homepage
  //   router.replace("/");
  // }, [router]);

  // Return minimal UI during redirect
  return (
    <div className="flex h-dvh w-screen items-center justify-center">
      <div className="text-center">
        <div>Page not found</div>
        <Link href="/">Go to home</Link>
      </div>
    </div>
  );
}
