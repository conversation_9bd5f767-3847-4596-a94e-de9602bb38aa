import type { NextRequest } from "next/server";
import { getUserData } from "@/lib/server/user-actions";

export async function GET(req: NextRequest) {
  try {
    const userData = await getUserData();
    if (!userData) {
      return Response.json({ success: false, error: "Unauthorized" }, { status: 401 });
    }

    return Response.json({
      success: true,
      id: userData.userId,
      email: userData.email,
      name: userData.name,
    });
  } catch (error) {
    console.error("Error fetching user:", error);
    return Response.json({ success: false, error: "Failed to fetch user" }, { status: 500 });
  }
}
