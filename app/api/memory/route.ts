export const runtime = "nodejs";
import type { NextRequest } from "next/server";
import { getMemoriesByUserId, createMemory, updateMemory, deleteMemory } from "@/lib/db/queries";
import { getUserId } from "@/lib/server/user-actions";

export async function GET(request: NextRequest) {
  const userId = await getUserId();
  if (!userId) {
    return Response.json("User not authenticated", { status: 401 });
  }
  const limit = Number.parseInt(request.nextUrl.searchParams.get("limit") || "20");
  try {
    const memories = await getMemoriesByUserId({ userId, limit });
    return Response.json(memories);
  } catch (error) {
    console.error("Failed to fetch memories:", error);
    return Response.json("Failed to fetch memories!", { status: 500 });
  }
}

export async function POST(request: Request) {
  const userId = await getUserId();
  if (!userId) {
    return Response.json("User not authenticated", { status: 401 });
  }
  try {
    const { content } = await request.json();
    if (!content) return Response.json("Content is required", { status: 400 });
    const memory = await createMemory({ userId, content });
    return Response.json(memory, { status: 201 });
  } catch (error) {
    console.error("Failed to create memory:", error);
    return Response.json("Failed to create memory!", { status: 500 });
  }
}

export async function PUT(request: Request) {
  const userId = await getUserId();
  if (!userId) {
    return Response.json("User not authenticated", { status: 401 });
  }
  try {
    const { memoryId, content } = await request.json();
    if (!memoryId) return Response.json("Memory ID is required", { status: 400 });
    if (!content) return Response.json("Content is required", { status: 400 });

    const updatedMemoryItem = await updateMemory({ memoryId, userId, content });
    if (!updatedMemoryItem) {
      return Response.json("Memory not found or user unauthorized", { status: 404 });
    }
    return Response.json(updatedMemoryItem);
  } catch (error) {
    console.error("Failed to update memory:", error);
    return Response.json("Failed to update memory!", { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  const userId = await getUserId();
  if (!userId) {
    return Response.json("User not authenticated", { status: 401 });
  }
  try {
    const memoryId = request.nextUrl.searchParams.get("memoryId");
    if (!memoryId) return Response.json("Memory ID is required in query parameters", { status: 400 });

    const deletedMemoryItem = await deleteMemory({ memoryId, userId });
    if (!deletedMemoryItem) {
      return Response.json("Memory not found or user unauthorized", { status: 404 });
    }
    return Response.json({ message: "Memory deleted successfully", id: memoryId });
  } catch (error) {
    console.error("Failed to delete memory:", error);
    return Response.json("Failed to delete memory!", { status: 500 });
  }
}
