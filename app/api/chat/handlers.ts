import { generateUUID } from "@/lib/utils";
import { saveMessages } from "@/lib/db/queries";
import type { Message as AIResponseMessage } from "ai";

// Extended types for tool interactions
interface ToolInvocation {
  toolCallId: string;
  toolName: string;
  args: Record<string, any>;
  result?: any; // Result is optional as it will be added later
}

// Extending AIResponseMessage to include tool messages
interface ToolMessage {
  id?: string;
  role: "assistant" | "tool";
  content:
    | Array<{
        type: string;
        toolCallId?: string;
        toolName?: string;
        result?: any;
        text?: string;
        [key: string]: any;
      }>
    | string;
  parts?: any[];
  experimental_attachments?: any[];
  attachments?: any[];
}

interface OnFinishParams {
  response: {
    messages: Array<AIResponseMessage | ToolMessage>;
  };
  streamId: string;
  chatId: string;
  userId: string;
}

/**
 * Handle the completion of an AI stream, saving messages and tool results to the database
 */
export async function handleStreamFinish({ response, streamId, chatId }: OnFinishParams) {
  try {
    const assistantMessages = response.messages.filter((msg) => msg.role === "assistant");
    const toolMessages = response.messages.filter((msg) => msg.role === "tool");

    if (assistantMessages.length === 0) {
      console.log(`handleStreamFinish: No assistant message found for streamId ${streamId}.`);
      return;
    }

    // Process all tool calls from all assistant messages
    const toolCalls: ToolInvocation[] = assistantMessages
      .flatMap((msg) => {
        if (Array.isArray(msg.content)) {
          return msg.content
            .filter(
              (part): part is { type: "tool-call"; toolName: string; toolCallId: string; args: Record<string, any> } =>
                part.type === "tool-call" && typeof part.toolCallId === "string" && typeof part.toolName === "string" && typeof part.args === "object"
            )
            .map((toolCallPart) => ({
              toolCallId: toolCallPart.toolCallId,
              toolName: toolCallPart.toolName,
              args: toolCallPart.args,
            }));
        }
        return [];
      })
      .filter((tc): tc is ToolInvocation => tc !== null);

    // Create a map of tool calls for easy lookup
    const toolCallMap = new Map<string, ToolInvocation>(toolCalls.map((tc) => [tc.toolCallId, tc]));

    // Add results to the tool calls from tool messages
    for (const toolMessage of toolMessages) {
      if (Array.isArray(toolMessage.content)) {
        for (const part of toolMessage.content) {
          if (part.type === "tool-result" && part.toolCallId && toolCallMap.has(part.toolCallId)) {
            const toolCall = toolCallMap.get(part.toolCallId)!;
            toolCall.result = part.result;
          }
        }
      }
    }

    // The final assistant message is the last one in the list
    const finalAssistantMessage = assistantMessages[assistantMessages.length - 1];
    const assistantId = finalAssistantMessage.id || generateUUID();

    // Extract text content from the final assistant message
    const textContent =
      typeof finalAssistantMessage.content === "string"
        ? finalAssistantMessage.content
        : finalAssistantMessage.content
            .filter((part) => part.type === "text")
            .map((part) => part.text)
            .join("");

    // Prepare parts for the database
    const finalToolInvocations = Array.from(toolCallMap.values());
    const dbParts = [
      ...(textContent ? [{ type: "text", text: textContent }] : []),
      ...finalToolInvocations.map((invocation) => ({
        type: "tool-invocation",
        toolInvocation: {
          ...invocation,
          state: "result", // Assuming all presented tools have a result
        },
      })),
    ];

    const messageToSave = {
      id: assistantId,
      chatId,
      role: "assistant" as const,
      content: textContent,
      parts: dbParts,
      attachments: (finalAssistantMessage.experimental_attachments || (finalAssistantMessage as any).attachments) ?? [],
      sources: null, // You may need to extract sources if they exist
      createdAt: new Date(),
    };

    await saveMessages({ messages: [messageToSave] });

    console.log(
      `handleStreamFinish: Saved assistant message ${assistantId} with ${finalToolInvocations.length} tool invocations for streamId ${streamId}`
    );
  } catch (error) {
    console.error(`handleStreamFinish: Error processing stream for streamId ${streamId}:`, error);
  }
}
