import { SUPPORTED_CONTENT_TYPES } from "@/components/multimodal/constants";
import { MODES } from "@/lib/constants";
import { z } from "zod";

const textPartSchema = z.object({
  text: z.string().min(1).max(2000),
  type: z.enum(["text"]),
});

// Define supported content types for attachments

export const postRequestBodySchema = z.object({
  id: z.string(),
  message: z.object({
    id: z.string().uuid(),
    createdAt: z.coerce.date(),
    role: z.enum(["user"]),
    content: z.string().min(1).max(2000),
    parts: z.array(textPartSchema),
    experimental_attachments: z
      .array(
        z.object({
          url: z.string().url(),
          name: z.string().min(1).max(2000),
          contentType: z.enum([...SUPPORTED_CONTENT_TYPES] as [string, ...string[]]),
        })
      )
      .optional(),
  }),
  selectedChatModel: z.enum(["chat-model", "chat-model-reasoning"]),
  selectedVisibilityType: z.enum(["public", "private", "unlisted"]),
  selectedWorkspace: z.string().optional(),
  data: z
    .object({
      forceDeepResearch: z.boolean().optional(),
      selectedOptions: z.record(z.string().optional()).optional(),
      mode: z.nativeEnum(MODES).default(MODES.DEFAULT),
      // mentionedItems: z.array(z.any()).optional(),
      // isPoAssistantMode: z.boolean().optional(),
    })
    .optional(),
});

export type PostRequestBody = z.infer<typeof postRequestBodySchema>;
