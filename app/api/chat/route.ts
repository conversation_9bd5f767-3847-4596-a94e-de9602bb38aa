import { loadMcpToolsForMode } from "@/lib/ai/mcp";
import { getSystemPrompt } from "@/lib/ai/prompts";
import { myProvider } from "@/lib/ai/providers";
import { getBuiltInToolsForContext } from "@/lib/ai/tools";
import { MODES, WORKSPACE_IDS, type Mode } from "@/lib/constants";
import {
  createStreamId,
  deleteChatById,
  getChatById,
  getMemoriesByUserId,
  getMessagesByChatId,
  getStreamIdsByChatId,
  saveChat,
  saveMessages,
  updateChatTitleById,
} from "@/lib/db/queries";
import type { Chat } from "@/lib/db/schema";
import { ChatSDKError } from "@/lib/errors";
import { getToolsForWorkspaceAndMode } from "@/lib/server/constants";
import { getUserData, getUserId } from "@/lib/server/user-actions";
import { generateUUID } from "@/lib/utils";
import type { Attachment } from "ai";
import { appendClientMessage, createDataStream, smoothStream, streamText } from "ai";
import { AISDKExporter } from "langsmith/vercel";
import { unstable_noStore as noStore } from "next/cache";
import { after } from "next/server";
import { createResumableStreamContext, type ResumableStreamContext } from "resumable-stream";
import { generateTitleFromUserMessage } from "../../(chat)/actions";
import { handleStreamFinish } from "./handlers";
import { postRequestBodySchema, type PostRequestBody } from "./schema";

export const runtime = "nodejs";

export const maxDuration = 60;

// Common headers for streaming responses
const STREAMING_HEADERS = {
  "Content-Type": "text/event-stream",
  "Cache-Control": "no-cache",
  Connection: "keep-alive",
  "Transfer-Encoding": "chunked",
} as const;

// Helper function to separate file attachments from ticket attachments
function separateAttachments(attachments: Attachment[] = []) {
  return attachments.reduce(
    (acc, attachment) => {
      if (attachment.url?.startsWith("jira:")) {
        // This is a ticket attachment
        acc.ticketAttachments.push({
          url: attachment.url,
          name: attachment.name || "Jira Ticket",
          contentType: attachment.contentType || "application/json",
        });
      } else if (attachment.url?.startsWith("http")) {
        // This is a real file attachment
        acc.fileAttachments.push(attachment);
      }
      return acc;
    },
    {
      fileAttachments: [] as Attachment[],
      ticketAttachments: [] as Array<{
        url: string;
        name: string;
        contentType: string;
      }>,
    }
  );
}

let globalStreamContext: ResumableStreamContext | null = null;

function getStreamContext() {
  if (!globalStreamContext) {
    try {
      // If process.env.REDIS_URL exists, this will use Redis for stream resumption
      // Otherwise, it will fall back to a non-Redis implementation
      globalStreamContext = createResumableStreamContext({
        waitUntil: after,
      });
      console.log("API: Successfully created resumable stream context");
    } catch (error: unknown) {
      if (error instanceof Error && error.message.includes("REDIS_URL")) {
        console.log(" > Resumable streams are disabled due to missing REDIS_URL");
      } else {
        console.error("Error creating resumable stream context:", error);
      }
    }
  }

  return globalStreamContext;
}

export async function POST(request: Request) {
  noStore();
  let requestBody: PostRequestBody;

  // Parse request body
  try {
    const json = await request.json();
    requestBody = postRequestBodySchema.parse(json);
    // console.log("API POST /chat: parsed requestBody.data:", requestBody?.data);
  } catch (error) {
    console.error("API POST /chat: Invalid request body error:", error);
    return new Response("Invalid request body", { status: 400 });
  }

  // Extract data from request body
  try {
    const { id, message, selectedChatModel, selectedVisibilityType, data } = requestBody;
    const mode = data?.mode;
    const forceDeepResearch = data?.forceDeepResearch;
    const selectedOptionsFromData = data?.selectedOptions;
    // const mentionedItems = data?.mentionedItems;
    // const isPoAssistantMode = data?.isPoAssistantMode;
    const workspace = selectedOptionsFromData?.workspaces; // Prioritize from request body

    // if (!workspace) {
    //   // If selectedWorkspace is not provided or falsy
    //   const cookiesHeader = request.headers.get("cookie");
    //   const workspaceFromCookies = parseJsonCookie(cookiesHeader, "workspaces");
    //   if (workspaceFromCookies) {
    //     workspace = workspaceFromCookies;
    //     console.log("API POST /chat: Using workspace from cookies:", workspace);
    //   } else {
    //     console.log("API POST /chat: No workspace found in request body or cookies. Consumers may use a default.");
    //   }
    // } else {
    //   console.log("API POST /chat: Using workspace from request body:", workspace);
    // }

    // Authenticate with Clerk (cached version with optimizations)
    const authStartTime = performance.now();
    const userSession = await getUserData();
    if (!userSession) {
      console.log("API POST /chat: Authentication failed, no valid userId found");
      return new Response("Unauthorized", { status: 401 });
    }
    const userId = userSession.userId;
    const authDuration = performance.now() - authStartTime;
    console.log(`🔐 API POST /chat: Authentication completed in ${authDuration.toFixed(2)}ms`);

    // 🗄️ Database Operations with timing
    const dbStartTime = performance.now();
    const [chat, previousMessages, userMemories] = await Promise.all([
      getChatById({ id }),
      getMessagesByChatId({ id }),
      getMemoriesByUserId({ userId, limit: 10 }),
    ]);
    const chatDuration = performance.now() - dbStartTime;
    console.log(`📊 getChatById: ${chatDuration.toFixed(2)}ms`);

    // Create new chat if it doesn't exist
    if (!chat) {
      const defaultTitle = "New Chat";
      await saveChat({
        id,
        userId,
        title: defaultTitle,
        visibility: selectedVisibilityType === "unlisted" ? "private" : selectedVisibilityType,
      });

      // 🔄 BACKGROUND TITLE GENERATION - Don't block the main flow
      generateTitleFromUserMessage({
        message: {
          ...message,
          content: message.content ?? (message.parts[0]?.text || ""),
        },
      })
        .then(async (title) => {
          if (title && title !== defaultTitle) {
            try {
              await updateChatTitleById({ chatId: id, title });
              console.log(`✅ Chat title updated to "${title}" for chat ${id}`);
            } catch (err) {
              console.warn(`⚠️ Failed to update chat title for chat ${id}:`, err);
            }
          }
        })
        .catch((err) => console.warn(`⚠️ Background title generation failed:`, err));
    } else {
      if (chat.userId !== userId) {
        console.error(`API POST /chat: Chat ${id} belongs to user ${chat.userId}, but request is from user ${userId}`);
        return new Response("Forbidden", { status: 403 });
      }
    }

    const messagesToAppend = appendClientMessage({
      // @ts-expect-error: todo add type conversion from DBMessage[] to UIMessage[]
      messages: previousMessages,
      message,
    });

    await saveMessages({
      messages: [
        {
          chatId: id,
          id: message.id,
          role: "user",
          parts: message.parts,
          attachments: message.experimental_attachments ?? [],
          sources: null,
          createdAt: new Date(),
        },
      ],
    });

    const streamId = generateUUID();
    await createStreamId({ streamId, chatId: id });

    // Existing AI model logic (only runs if not in PO Assistant mode)
    // Separate file attachments from ticket attachments
    const { fileAttachments, ticketAttachments } = separateAttachments(message.experimental_attachments);

    // Get tool IDs for this workspace and mode
    const { builtInTools: activeBuiltInToolIds } = getToolsForWorkspaceAndMode(workspace || WORKSPACE_IDS.DEFAULT, mode as Mode);

    // Load MCP tools based on mode and user configurations
    const { tools: mcpTools, clients: mcpClients } = await loadMcpToolsForMode(mode as Mode, userId, workspace);

    // Combine built-in and MCP tool names for the active list
    const allActiveToolIds = [...activeBuiltInToolIds, ...Object.keys(mcpTools)];

    const availableTools = forceDeepResearch
      ? {}
      : (() => {
          // Get built-in server tools for these tool IDs
          const builtInTools = getBuiltInToolsForContext(activeBuiltInToolIds);

          // Convert to tool object format expected by AI SDK
          const toolsObject: Record<string, any> = {};
          builtInTools.forEach((tool, index) => {
            // Use the tool IDs to map tools correctly
            if (index < activeBuiltInToolIds.length) {
              toolsObject[activeBuiltInToolIds[index]] = tool;
            }
          });

          const allTools = { ...toolsObject, ...mcpTools } as any;
          console.log("=mcpTools=", Object.keys(mcpTools).length > 0 ? Object.keys(mcpTools) : "No MCP tools loaded");
          return allTools;
        })();

    const systemPrompt = await getSystemPrompt({
      workspace: workspace || "default",
      selectedChatModel: forceDeepResearch ? "deep-research" : selectedChatModel,
      memories: userMemories?.map((m) => m.content || "") || [],
      attachments: fileAttachments, // Only pass real file attachments
      ticketAttachments: ticketAttachments, // Pass ticket info separately
      mode: mode || "default", // Pass the mode to enable PO Assistant prompts
      userEmail: userSession.email || "",
    });

    console.log("=activeToolIds=", allActiveToolIds);
    console.log("=systemPrompt=", systemPrompt);

    // 🎯 Final preparation timing summary
    const totalPreparationTime = performance.now() - authStartTime;
    console.log(`🎯 API POST /chat: Starting AI streaming after ${totalPreparationTime.toFixed(2)}ms preparation`);

    const stream = createDataStream({
      execute: (dataStream) => {
        const result = streamText({
          model: myProvider.languageModel(forceDeepResearch ? "deep-research" : selectedChatModel),
          system: systemPrompt,
          messages: messagesToAppend,
          maxSteps: forceDeepResearch ? 1 : 20,
          experimental_activeTools: forceDeepResearch ? undefined : allActiveToolIds, // Only pass activeToolIds if not using Perplexity directly for research
          experimental_transform: smoothStream({ chunking: "word" }),
          experimental_generateMessageId: generateUUID,
          experimental_toolCallStreaming: true,
          experimental_telemetry: AISDKExporter.getSettings({
            metadata: {
              workspace: workspace || "default",
              selectedChatModel: forceDeepResearch ? "deep-research" : selectedChatModel,
              mode: mode || MODES.DEFAULT,
              userId: userId,
              chatId: id,
              streamId: streamId,
              messageId: message.id,
              messageContent: message.content ?? (message.parts?.[0]?.text || ""),
              messageParts: message.parts.map((part) => part.text || ""),
            },
          }),
          headers: {
            "Helicone-User-Id": userId,
            "Helicone-Session-Id": streamId,
            // "Helicone-Session-Path": "/chat",
            // "Helicone-Session-Name": "Chatbot",
            // "Helicone-User-Email": ,
          },
          tools: availableTools,
          onFinish: async ({ response }) => {
            console.log("API POST /chat: onFinish for streamId:", streamId);

            // IMPORTANT: Close all MCP clients to release resources
            for (const client of mcpClients) {
              try {
                // client.name is not a property, using a generic log
                await client.close();
                console.log(`Closed an MCP client.`);
              } catch (err) {
                console.error(`Error closing an MCP client:`, err);
              }
            }

            if (userId) {
              await handleStreamFinish({
                response: { messages: response.messages as any[] }, // Temporary fix for type mismatch
                streamId,
                chatId: id,
                userId,
              });
            }
          },
        });

        result.consumeStream();

        result.mergeIntoDataStream(dataStream, {
          sendReasoning: true,
          sendSources: true,
          sendUsage: true,
        });
      },
      onError: (error) => {
        console.error("API POST /chat: error in createDataStream:", error);
        return "Oops, an error occurred!";
      },
    });

    const streamContext = getStreamContext();

    if (streamContext) {
      return new Response(await streamContext.resumableStream(streamId, () => stream), {
        status: 200,
        headers: STREAMING_HEADERS,
      });
    } else {
      return new Response(stream, {
        status: 200,
        headers: STREAMING_HEADERS,
      });
    }
  } catch (error) {
    if (error instanceof ChatSDKError) {
      return error.toResponse();
    }
  }
}

export async function GET(request: Request) {
  noStore();
  console.log("API GET /chat: request URL =", request.url);
  const streamContext = getStreamContext();

  if (!streamContext) {
    return new Response(null, { status: 204 });
  }

  const { searchParams } = new URL(request.url);
  const chatId = searchParams.get("chatId");
  console.log("API GET /chat: chatId =", chatId);

  if (!chatId) {
    return new Response("chatId is required", { status: 400 });
  }

  const userId = await getUserId();
  console.log("API GET /chat: userId =", userId);
  if (!userId) {
    console.log("API GET /chat: Authentication failed, no valid userId found");
    return new Response("Unauthorized", { status: 401 });
  }

  let chat: Chat;

  try {
    chat = await getChatById({ id: chatId });
  } catch (error) {
    console.error(`API GET /chat: Error getting chat ${chatId}:`, error);
    return new Response("Not found", { status: 404 });
  }

  if (!chat) {
    console.log(`API GET /chat: Chat ${chatId} not found`);
    return new Response("Not found", { status: 404 });
  }

  if (chat.visibility === "private" && chat.userId !== userId) {
    console.error(`API GET /chat: Chat ${chatId} belongs to user ${chat.userId}, but request is from user ${userId}`);
    return new Response("Forbidden", { status: 403 });
  }

  const streamIds = await getStreamIdsByChatId({ chatId });
  console.log("API GET /chat: streamIds =", streamIds);

  if (!streamIds.length) {
    return new Response("No streams found", { status: 404 });
  }

  const recentStreamId = streamIds.at(-1);
  console.log("API GET /chat: recentStreamId =", recentStreamId);

  if (!recentStreamId) {
    return new Response("No recent stream found", { status: 404 });
  }

  const emptyDataStream = createDataStream({
    execute: () => {},
  });

  try {
    console.log("API GET /chat: attempting to resume stream:", recentStreamId);
    return new Response(await streamContext.resumableStream(recentStreamId, () => emptyDataStream), {
      status: 200,
      headers: STREAMING_HEADERS,
    });
  } catch (error) {
    console.error("API GET /chat: error resuming stream:", error);
    return new Response("Error resuming stream", { status: 500 });
  }
}

export async function DELETE(request: Request) {
  noStore();
  const { searchParams } = new URL(request.url);
  const id = searchParams.get("id");

  if (!id) {
    return new Response("Not Found", { status: 404 });
  }

  const userId = await getUserId();
  if (!userId) {
    console.log("API DELETE /chat: Authentication failed, no valid userId found");
    return new Response("Unauthorized", { status: 401 });
  }

  try {
    const chat = await getChatById({ id });

    if (!chat) {
      console.log(`API DELETE /chat: Chat ${id} not found`);
      return new Response("Not Found", { status: 404 });
    }

    if (chat.userId !== userId) {
      console.error(`API DELETE /chat: Chat ${id} belongs to user ${chat.userId}, but request is from user ${userId}`);
      return new Response("Forbidden", { status: 403 });
    }

    const deletedChat = await deleteChatById({ id });
    console.log(`API DELETE /chat: Successfully deleted chat ${id} for user ${userId}`);

    return Response.json(deletedChat, { status: 200 });
  } catch (error) {
    console.error(`API DELETE /chat: Error deleting chat ${id}:`, error);
    return new Response("An error occurred while processing your request!", {
      status: 500,
    });
  }
}
