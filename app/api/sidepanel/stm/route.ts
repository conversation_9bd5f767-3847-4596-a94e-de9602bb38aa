import { getUserId } from "@/lib/server/user-actions";
import type { NextRequest } from "next/server";
import { createClient } from "redis";

export const runtime = "nodejs";
export const maxDuration = 10;

function buildCorsHeaders(origin: string | null = "*") {
  return {
    "Access-Control-Allow-Origin": origin ?? "*",
    "Access-Control-Allow-Methods": "POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
    "Access-Control-Allow-Credentials": "true",
  } as const;
}

// Reusable Redis client (singleton with lazy connect & auto-reconnect)
let redisClient: any | null = null;
async function getRedisSafe() {
  if (!process.env.REDIS_URL) return null;
  if (redisClient?.isOpen) return redisClient;
  if (redisClient && !redisClient.isOpen) {
    try {
      await redisClient.connect();
      return redisClient;
    } catch (err) {
      console.warn("⚠️ Redis reconnect failed", err);
      redisClient = null;
    }
  }
  try {
    redisClient = createClient({
      url: process.env.REDIS_URL,
      socket: {
        reconnectStrategy: (retries: number) => Math.min(retries * 100, 1000),
      },
    });
    redisClient.on("error", (err: Error) => {
      // Ignore benign socket close after quit / idle
      if (err.message.includes("Socket closed")) return;
      console.warn("⚠️ Redis client error (stm route):", err.message);
    });
    await redisClient.connect();
    console.log("🔄 Redis client connected (stm route)");
    return redisClient;
  } catch (err) {
    console.warn("⚠️ Redis initial connect failed", err);
    redisClient = null;
    return null;
  }
}

async function withRedis<T>(fn: (client: any) => Promise<T>): Promise<T | null> {
  const client = await getRedisSafe();
  if (!client) return null;

  try {
    const res = await fn(client);
    return res;
  } catch (err) {
    console.warn("⚠️ Redis op error (stm route):", err);
    return null;
  }
}

export async function OPTIONS(request: NextRequest) {
  const origin = request.headers.get("origin");
  return new Response(null, { headers: buildCorsHeaders(origin) });
}

export async function POST(request: NextRequest) {
  try {
    let body: { text?: string; userId?: string };
    try {
      body = await request.json();
    } catch {
      return new Response("Invalid JSON", { status: 400, headers: buildCorsHeaders() });
    }

    // Extract userId from payload if provided
    let userId: string | null = body.userId || null;
    if (!userId) {
      try {
        userId = await getUserId();
      } catch {}
    }

    // Fallback to anonymous key if not signed in (per-ip)
    if (!userId) {
      const ip = request.headers.get("x-forwarded-for")?.split(",")[0] || "anon";
      userId = `anon_${ip.replace(/[:.]/g, "_")}`;
    }

    const text = body.text?.trim();
    if (!text) {
      return new Response("'text' is required", { status: 400, headers: buildCorsHeaders() });
    }

    let stored = false;
    await withRedis(async (r) => {
      const key = `stm:${userId}`;
      await r.lPush(key, text);
      await r.lTrim(key, 0, 99);
      await r.expire(key, 60 * 60 * 24);
      stored = true;
      console.log("[API] Stored STM snippet for", userId, text.slice(0, 80));
    });

    const origin = request.headers.get("origin");
    return new Response(JSON.stringify({ success: stored }), {
      status: 200,
      headers: { ...buildCorsHeaders(origin), "Content-Type": "application/json" },
    });
  } catch (error) {
    console.error("[API] Knowledge store error:", error);
    return new Response("Internal Server Error", { status: 500, headers: buildCorsHeaders() });
  }
}
