import { google } from "@ai-sdk/google";
import { openai } from "@ai-sdk/openai";
import { anthropic } from "@ai-sdk/anthropic";
import { streamText, createDataStream, smoothStream } from "ai";
// import { getChatById, saveChat, saveMessages } from "@/lib/db/queries";
import { generateUUID } from "@/lib/utils";
import type { CoreMessage } from "ai";
import type { Message as AIResponseMessage } from "ai";
import { AISDKExporter } from "langsmith/vercel";

interface ChatRequest {
  message: string;
  chatId: string;
  conversationHistory?: Array<{
    role: string;
    content: string;
  }>;
  modelId?: string;

  context?: {
    primary: any;
    recent: any[];
    mentioned: any[];
    available: any[];
  };
}

interface ChatOptions {
  requireAuth: boolean;
  maxContextLength?: number;
  maxHistoryMessages?: number;
  enableCache?: boolean;
}

const DEFAULT_OPTIONS: ChatOptions = {
  requireAuth: true,
  maxContextLength: 10000, // Increased from 3000
  maxHistoryMessages: 20, // Increased from 5
  enableCache: true,
};

// Build system prompt with smart multi-context support
function promptBuilder(context?: any, maxLength = 10000): string {
  console.log("[API-HANDLER] promptBuilder called with context:", {
    hasContext: !!context,
    hasPrimary: !!context?.primary,
    primaryContent: context?.primary?.pageContent?.substring(0, 100),
    primaryContentLength: context?.primary?.pageContent?.length || 0
  });
  
  let systemPrompt = "You are a helpful AI assistant integrated with the user's browser. Provide concise, accurate, and helpful responses.";

  // Handle smart context structure
  if (context && context.primary) {
    const contextParts = [];

    // Primary context (current site)
    if (context.primary) {
      const primary = context.primary;
      contextParts.push(`CURRENT SITE: ${primary.title || "Unknown"}`);
      contextParts.push(`URL: ${primary.url || "Unknown"}`);

      if (primary.pageContent) {
        console.log("[API-HANDLER] Adding pageContent to prompt:", primary.pageContent.length, "chars");
        const truncatedContent =
          primary.pageContent.length > maxLength ? `${primary.pageContent.substring(0, maxLength)}\n[Content truncated...]` : primary.pageContent;
        contextParts.push(`Current page content:\n${truncatedContent}`);
      } else {
        console.warn("[API-HANDLER] No pageContent in primary context!");
      }

      if (primary.selectedText) {
        contextParts.push(`Selected text: "${primary.selectedText}"`);
      }
    }

    // Recent contexts (sites discussed recently)
    if (context.recent && Array.isArray(context.recent) && context.recent.length > 0) {
      contextParts.push(`\nRECENT SITES DISCUSSED:`);
      context.recent.forEach((site: any, index: number) => {
        if (site && site.title) {
          contextParts.push(`${index + 1}. ${site.title} (${site.domain || "Unknown domain"})`);
          if (site.pageContent && site.pageContent.length > 0) {
            const summary = site.pageContent.substring(0, 300) + "...";
            contextParts.push(`   Summary: ${summary}`);
          }
        }
      });
    }

    // Explicitly mentioned contexts
    if (context.mentioned && Array.isArray(context.mentioned) && context.mentioned.length > 0) {
      contextParts.push(`\nEXPLICITLY REFERENCED SITES:`);
      context.mentioned.forEach((site: any, index: number) => {
        if (site && site.title) {
          contextParts.push(`${index + 1}. ${site.title} (${site.domain || "Unknown domain"})`);
          if (site.pageContent) {
            const content = site.pageContent.length > 2000 ? `${site.pageContent.substring(0, 2000)}\n[Content truncated...]` : site.pageContent;
            contextParts.push(`   Content: ${content}`);
          }
        }
      });
    }

    if (contextParts.length > 0) {
      systemPrompt += `\n\nContext from the browser:\n${contextParts.join("\n")}`;
    }

    // Add guidance for multi-context responses
    const recentCount = context.recent ? context.recent.length : 0;
    const mentionedCount = context.mentioned ? context.mentioned.length : 0;
    if (recentCount > 0 || mentionedCount > 0) {
      systemPrompt += `\n\nWhen referencing multiple sites, clearly indicate which site you're referring to. You can compare information across the provided contexts.`;
    }

    return systemPrompt;
  }

  return systemPrompt;
}

// Get AI model based on modelId or default
function getAIModel(modelId?: string) {
  if (modelId?.startsWith("gpt")) {
    return openai(modelId);
  } else if (modelId?.startsWith("claude")) {
    return anthropic(modelId);
  }
  return google("gemini-2.5-flash");
}

// Simplified stream finish handler for sidepanel
async function handleSidepanelStreamFinish(response: { messages: AIResponseMessage[] }, streamId: string, chatId: string, userId: string) {
  try {
    const assistantMessages = response.messages.filter((msg) => msg.role === "assistant");

    if (assistantMessages.length === 0) {
      console.log(`handleSidepanelStreamFinish: No assistant message found for streamId ${streamId}.`);
      return;
    }

    // Get the final assistant message
    const finalAssistantMessage = assistantMessages[assistantMessages.length - 1];
    const assistantId = finalAssistantMessage.id || generateUUID();

    // Extract text content with proper typing
    let textContent = "";
    const content = finalAssistantMessage.content;

    if (typeof content === "string") {
      textContent = content;
    } else if (Array.isArray(content)) {
      textContent = (content as any[])
        .filter((part: any) => part.type === "text")
        .map((part: any) => part.text || "")
        .join("");
    }

    // Simple message structure for sidepanel
    // const messageToSave = {
    //   id: assistantId,
    //   chatId,
    //   role: "assistant" as const,
    //   parts: [{ type: "text", text: textContent }],
    //   attachments: [],
    //   sources: null,
    //   createdAt: new Date(),
    // };
    // await saveMessages({ messages: [messageToSave] });

    console.log(`handleSidepanelStreamFinish: Saved assistant message ${assistantId} for streamId ${streamId}`);
  } catch (error) {
    console.error(`handleSidepanelStreamFinish: Error processing stream for streamId ${streamId}:`, error);
  }
}

// Main handler function
export async function handleExtChat(request: ChatRequest, userId: string | null, options: Partial<ChatOptions> = {}): Promise<ReadableStream> {
  const opts = { ...DEFAULT_OPTIONS, ...options };

  // Check authentication if required
  if (opts.requireAuth && !userId) {
    throw new Error("Authentication required");
  }

  console.log("[API-HANDLER] handleExtChat received request:", {
    message: request.message,
    hasContext: !!request.context,
    contextStructure: request.context ? Object.keys(request.context) : null,
    primaryContent: request.context?.primary?.pageContent?.substring(0, 100),
    primaryContentLength: request.context?.primary?.pageContent?.length || 0
  });

  const { message, chatId, conversationHistory, modelId, context } = request;

  // Generate message IDs
  const userMessageId = generateUUID();
  const streamId = generateUUID();

  // Build messages array for AI
  const messages: CoreMessage[] = [];

  // Add conversation history
  if (conversationHistory && Array.isArray(conversationHistory)) {
    const historyLimit = opts.maxHistoryMessages || 20;
    const recentHistory = conversationHistory.slice(-historyLimit);

    for (const msg of recentHistory) {
      if (msg.role && msg.content && typeof msg.content === "string") {
        messages.push({
          role: msg.role as "user" | "assistant",
          content: msg.content,
        });
      }
    }
  }

  // Add current message
  messages.push({
    role: "user",
    content: message,
  });

  const stream = createDataStream({
    execute: (dataStream) => {
      const result = streamText({
        model: getAIModel(modelId),
        system: promptBuilder(context, opts.maxContextLength),
        messages,
        // maxSteps: forceDeepResearch ? 1 : 20,
        // experimental_activeTools: forceDeepResearch ? undefined : allActiveToolIds, // Only pass activeToolIds if not using Perplexity directly for research
        experimental_transform: smoothStream({ chunking: "word" }),
        experimental_generateMessageId: generateUUID,
        experimental_toolCallStreaming: true,
        experimental_telemetry: AISDKExporter.getSettings({
          metadata: {
            workspace: "sidepanel",
            selectedChatModel: modelId || "gemini-2.5-flash",
            userId: userId || "",
            chatId: chatId,
            streamId: userMessageId,
            messageId: userMessageId,
            messageContent: message,
            messageParts: [message],
            hasSmartContext: !!(context && context.primary),
            contextsCount: context ? (context.recent?.length || 0) + (context.mentioned?.length || 0) + 1 : 0,
          },
        }),
        headers: {
          "Helicone-User-Id": userId || "",
          "Helicone-Session-Id": chatId,
        },
        onFinish: async ({ response }) => {
          if (userId) {
            await handleSidepanelStreamFinish({ messages: response.messages as AIResponseMessage[] }, streamId, chatId, userId);
          }
        },
      });

      result.consumeStream();

      result.mergeIntoDataStream(dataStream, {
        sendReasoning: true,
        sendSources: true,
        sendUsage: true,
      });
    },
    onError: (error) => {
      console.error("Sidepanel chat error in createDataStream:", error);
      return "Oops, an error occurred!";
    },
  });

  return stream;
}
