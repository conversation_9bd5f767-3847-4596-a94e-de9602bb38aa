import { auth } from "@clerk/nextjs/server";
import { type NextRequest } from "next/server";
import { handleExtChat } from "./handler";

// CORS headers for Chrome extension - restrict to extension origin
const extensionId = process.env.NEXT_PUBLIC_EXTENSION_ID || "";
const corsHeaders = {
  "Access-Control-Allow-Origin": `chrome-extension://${extensionId}`,
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS, PATCH",
  "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With",
  "Access-Control-Allow-Credentials": "true", // Important for cookies
  // "Access-Control-Max-Age": "86400",
};

const streamHeaders = {
  "Content-Type": "text/event-stream",
  "Cache-Control": "no-cache",
  Connection: "keep-alive",
  "Transfer-Encoding": "chunked",
};

// export async function OPTIONS() {
//   return new Response(null, { headers: corsHeaders });
// }

export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();
    const body = await req.json();

    const stream = await handleExtChat(body, userId, { requireAuth: true });

    return new Response(stream, {
      headers: {
        ...corsHeaders,
        ...streamHeaders,
      },
    });
  } catch (error) {
    if (error instanceof Error && error.message === "Authentication required") {
      return Response.json({ error: "Unauthorized" }, { status: 401, headers: corsHeaders });
    }
    console.error("[Sidepanel Chat Error]", error);
    return Response.json({ error: (error as Error).message }, { status: 500, headers: corsHeaders });
  }
}

/**
 * Retrieves chats for the authenticated user.
 * If a `chatId` is provided as a query parameter, it fetches a specific chat.
 * Otherwise, it returns all chats for the user.
 *
 * @param {NextRequest} req - The Next.js request object.
 * @returns {Promise<Response>} A response object with the chat(s) or an error message.
 */
// export async function GET(req: NextRequest) {
//   try {
//     const userId = await getUserId();
//     if (!userId) {
//       return Response.json({ success: false, error: "Unauthorized" }, { status: 401, headers: corsHeaders });
//     }

//     const { searchParams } = new URL(req.url);
//     const chatId = searchParams.get("id");

//     if (chatId) {
//       // Get specific chat
//       const [specificChat] = await db
//         .select()
//         .from(chat)
//         .where(and(eq(chat.id, chatId), eq(chat.userId, userId)))
//         .limit(1);

//       if (!specificChat) {
//         return Response.json({ success: false, error: "Chat not found" }, { status: 404, headers: corsHeaders });
//       }

//       return Response.json(
//         {
//           success: true,
//           chat: specificChat,
//         },
//         { headers: corsHeaders }
//       );
//     } else {
//       // Get all chats for user
//       const userChats = await db.select().from(chat).where(eq(chat.userId, userId)).orderBy(chat.createdAt);

//       return Response.json(
//         {
//           success: true,
//           chats: userChats,
//         },
//         { headers: corsHeaders }
//       );
//     }
//   } catch (error) {
//     console.error("Error fetching chats:", error);
//     return Response.json({ success: false, error: "Failed to fetch chats" }, { status: 500, headers: corsHeaders });
//   }
// }

// Schema for chat creation/update
// const chatSchema = z.object({
//   id: z.string(),
//   title: z.string().optional(),
//   visibility: z.enum(["private", "public"]).optional(),
// });

/**
 * Handles updating an existing chat.
 * It expects a chat object in the request body. It updates the title and/or visibility.
 *
 * @param {NextRequest} req - The Next.js request object.
 * @returns {Promise<Response>} A response object with the updated chat or an error message.
 */
// export async function PATCH(req: NextRequest) {
//   try {
//     const userId = await getUserId();
//     if (!userId) {
//       return Response.json({ success: false, error: "Unauthorized" }, { status: 401, headers: corsHeaders });
//     }

//     const body = await req.json();
//     const validatedData = chatSchema.parse(body);
//     const { id, ...updateData } = validatedData;

//     if (Object.keys(updateData).length === 0) {
//       return Response.json({ success: false, error: "No update data provided" }, { status: 400, headers: corsHeaders });
//     }

//     const [resultChat] = await db
//       .update(chat)
//       .set(updateData)
//       .where(and(eq(chat.id, id), eq(chat.userId, userId)))
//       .returning();

//     if (!resultChat) {
//       return Response.json({ success: false, error: "Chat not found or unauthorized" }, { status: 404, headers: corsHeaders });
//     }

//     return Response.json(
//       {
//         success: true,
//         chat: resultChat,
//       },
//       { headers: corsHeaders }
//     );
//   } catch (error) {
//     console.error("Error updating chat:", error);

//     if (error instanceof z.ZodError) {
//       return Response.json({ success: false, error: "Invalid request data", details: error.errors }, { status: 400, headers: corsHeaders });
//     }

//     return Response.json({ success: false, error: "Failed to update chat" }, { status: 500, headers: corsHeaders });
//   }
// }

/**
 * Deletes a specific chat and all its associated messages.
 * It requires a `chatId` as a query parameter.
 *
 * @param {NextRequest} req - The Next.js request object.
 * @returns {Promise<Response>} A response object indicating success or failure.
 */
// export async function DELETE(req: NextRequest) {
//   try {
//     const userId = await getUserId();
//     if (!userId) {
//       return Response.json({ success: false, error: "Unauthorized" }, { status: 401, headers: corsHeaders });
//     }

//     const { searchParams } = new URL(req.url);
//     const chatId = searchParams.get("id");

//     if (!chatId) {
//       return Response.json({ success: false, error: "Chat ID is required" }, { status: 400, headers: corsHeaders });
//     }

//     // Delete all messages in the chat first
//     await db.delete(message).where(eq(message.chatId, chatId));

//     // Delete the chat
//     const result = await db
//       .delete(chat)
//       .where(and(eq(chat.id, chatId), eq(chat.userId, userId)))
//       .returning();

//     if (result.length === 0) {
//       return Response.json({ success: false, error: "Chat not found or unauthorized" }, { status: 404, headers: corsHeaders });
//     }

//     return Response.json(
//       {
//         success: true,
//         message: "Chat deleted successfully",
//       },
//       { headers: corsHeaders }
//     );
//   } catch (error) {
//     console.error("Error deleting chat:", error);
//     return Response.json({ success: false, error: "Failed to delete chat" }, { status: 500, headers: corsHeaders });
//   }
// }
