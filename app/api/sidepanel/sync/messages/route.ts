import type { NextRequest } from "next/server";
import { getUserId } from "@/lib/server/user-actions";
import { saveMessages } from "@/lib/db/queries";
import { z } from "zod";

// Schema for message validation
const messageSchema = z.object({
  id: z.string(),
  chatId: z.string(),
  role: z.enum(["user", "assistant", "system"]),
  parts: z.array(z.object({
    type: z.string(),
    content: z.string(),
  })),
  attachments: z.array(z.any()).default([]),
  sources: z.array(z.any()).default([]),
  createdAt: z.string().datetime(),
});

export async function POST(req: NextRequest) {
  try {
    const userId = await getUserId();
    if (!userId) {
      return Response.json({ success: false, error: "Unauthorized" }, { status: 401 });
    }

    const body = await req.json();
    const validatedData = messageSchema.parse(body);

    // Save the message
    await saveMessages({
      messages: [{
        id: validatedData.id,
        chatId: validatedData.chatId,
        role: validatedData.role,
        parts: validatedData.parts,
        attachments: validatedData.attachments,
        sources: validatedData.sources,
        createdAt: new Date(validatedData.createdAt),
      }],
    });

    return Response.json({ success: true });
  } catch (error) {
    console.error("Error saving message:", error);

    if (error instanceof z.ZodError) {
      return Response.json({ success: false, error: "Invalid request data", details: error.errors }, { status: 400 });
    }

    return Response.json({ success: false, error: "Failed to save message" }, { status: 500 });
  }
}