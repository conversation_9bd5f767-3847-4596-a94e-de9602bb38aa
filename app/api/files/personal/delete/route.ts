import { db } from "@/lib/db";
import { ai_document } from "@/lib/db/schema";
import { getUserId } from "@/lib/server/user-actions";
import { eq } from "drizzle-orm";
import { NextResponse } from "next/server";
import { clearDocumentCache } from "@/lib/cache";

export async function POST(req: Request) {
  try {
    const userId = await getUserId();

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { fileId } = await req.json();

    if (!fileId) {
      return new NextResponse("File ID is required", { status: 400 });
    }

    // Check if the file belongs to the user before deleting
    const fileRecords = await db.select().from(ai_document).where(eq(ai_document.id, fileId));

    if (fileRecords.length === 0) {
      return new NextResponse("File not found", { status: 404 });
    }

    const fileRecord = fileRecords[0];
    if (fileRecord.userId !== userId) {
      // This case should ideally not happen if IDs are unique and opaque
      // but as an extra security measure:
      return new NextResponse("Forbidden", { status: 403 });
    }

    await db.delete(ai_document).where(eq(ai_document.id, fileId));

    // Clear the cache for this user's personal files after successful deletion
    clearDocumentCache(userId, "personal");
    console.log(`[API] Cleared document cache for userId=${userId}, source=personal after deletion`);

    return NextResponse.json({ success: true, message: "File deleted successfully" });
  } catch (error) {
    console.error("Error deleting personal file:", error);
    if (error instanceof SyntaxError) {
      // Handle cases where req.json() fails
      return new NextResponse("Invalid request body", { status: 400 });
    }
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
