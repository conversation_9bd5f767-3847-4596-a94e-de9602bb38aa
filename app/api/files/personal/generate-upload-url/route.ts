import { getUserId } from "@/lib/server/user-actions";
import { createClient } from "@supabase/supabase-js";
import { NextResponse } from "next/server";
import { v4 as uuidv4 } from "uuid";

export async function POST(req: Request) {
  try {
    const userId = await getUserId();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { fileName, fileType } = await req.json();

    if (!fileName || !fileType) {
      return new NextResponse("fileName and fileType are required", { status: 400 });
    }

    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseServiceRoleKey) {
      console.error("Supabase URL or Service Role Key is not configured.");
      return new NextResponse("Storage configuration error", { status: 500 });
    }

    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey);

    const bucketName = "personal-knowledge-base";
    // Sanitize fileName and ensure a unique path, e.g., by prefixing with userId and a UUID
    const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9_.-]/g, "_");
    const uniqueFileId = uuidv4();
    const filePath = `${userId}/${uniqueFileId}-${sanitizedFileName}`;

    const { data, error } = await supabaseAdmin.storage.from(bucketName).createSignedUploadUrl(filePath);

    if (error) {
      console.error("Error generating signed upload URL:", error);
      return new NextResponse(`Supabase error: ${error.message}`, { status: 500 });
    }

    return NextResponse.json({
      presignedUrl: data.signedUrl, // The actual property name might be data.signedURL or data.signedUrl
      path: data.path, // The path within the bucket
      supabasePath: filePath, // Send back the full path we constructed
    });
  } catch (error) {
    console.error("Error in generate-upload-url endpoint:", error);
    if (error instanceof SyntaxError) {
      return new NextResponse("Invalid JSON in request body", { status: 400 });
    }
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
