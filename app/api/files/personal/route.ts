import { applyDocumentSearchFilter, createDocument<PERSON><PERSON><PERSON><PERSON>, documentCache } from "@/lib/cache";
import { getPersonalFiles } from "@/lib/db/queries";
import { getUserId } from "@/lib/server/user-actions";
import { createClient } from "@supabase/supabase-js"; // Import Supabase client
import { NextResponse } from "next/server";

interface FileMetadata {
  size?: number;
  type?: string;
  source?: string;
  supabase_path?: string; // Added supabase_path
  [key: string]: any;
}

const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;
const BUCKET_NAME = "personal-knowledge-base";
const SIGNED_URL_EXPIRES_IN = 300; // 5 minutes

let supabaseAdmin: ReturnType<typeof createClient>;

if (SUPABASE_URL && SUPABASE_SERVICE_ROLE_KEY) {
  supabaseAdmin = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
} else {
  console.error("Supabase admin client not initialized. Check SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY.");
}

export async function GET(request: Request) {
  try {
    const userId = await getUserId();

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    if (!supabaseAdmin) {
      return new NextResponse("Storage access is not configured on the server.", { status: 503 });
    }

    // Parse search param from URL if present
    const { searchParams } = new URL(request.url);
    const search = searchParams.get("search") || "";
    const useCache = searchParams.get("useCache") !== "false"; // Default to using cache

    // Create a cache key
    const cacheKey = createDocumentCacheKey(userId, "personal");

    // Try to get from cache if enabled
    if (useCache) {
      const cachedData = documentCache.get(cacheKey);
      if (cachedData) {
        console.log(`[API] Using cached personal files (${cachedData.count} files)`);

        // Apply search filtering if needed
        if (search) {
          const filteredFiles = applyDocumentSearchFilter(cachedData.files, search);
          return NextResponse.json({
            files: filteredFiles,
            fromCache: true,
          });
        }

        return NextResponse.json({
          files: cachedData.files,
          fromCache: true,
        });
      }
    }

    // No cache hit, fetch from database
    console.log(`[API] Fetching personal files from database`);
    const filesFromDb = await getPersonalFiles({ userId });

    const filesWithSignedUrls = await Promise.all(
      filesFromDb.map(async (file) => {
        let signedUrl = "";
        const metadata = file.metadata as FileMetadata;
        const supabasePath = metadata?.supabase_path;

        if (supabasePath) {
          const { data, error } = await supabaseAdmin.storage.from(BUCKET_NAME).createSignedUrl(supabasePath, SIGNED_URL_EXPIRES_IN);

          if (error) {
            console.error(`Error generating signed URL for ${supabasePath}:`, error.message);
            // Proceed without URL, or handle error differently
          } else {
            signedUrl = data.signedUrl;
          }
        }

        return {
          id: file.id,
          name: file.title,
          type: file.kind, // This is 'text', 'image', etc. from our db
          fileType: metadata?.type, // This is the actual mime type e.g. 'application/pdf'
          size: metadata?.size || 0,
          createdAt: file.createdAt?.toISOString() ?? new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          source: "personal" as const,
          isAiGenerated: metadata?.source === "ai",
          url: signedUrl, // The generated signed URL for download
          supabasePath: supabasePath, // Include for client if needed, though URL is primary
        };
      })
    );

    // Store in cache for future use
    documentCache.set(cacheKey, {
      files: filesWithSignedUrls,
      count: filesWithSignedUrls.length,
      source: "personal",
    });

    // Apply search filter if needed
    if (search) {
      const filteredFiles = applyDocumentSearchFilter(filesWithSignedUrls, search);
      return NextResponse.json({ files: filteredFiles });
    }

    return NextResponse.json({ files: filesWithSignedUrls });
  } catch (error) {
    console.error("Failed to fetch personal files with signed URLs:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
