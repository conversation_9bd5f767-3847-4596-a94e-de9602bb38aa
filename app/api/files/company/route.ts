import { getCompanyFiles } from "@/lib/db/queries";
import { getUserData } from "@/lib/server/user-actions";
import { NextResponse } from "next/server";
import { documentCache, createDocumentCacheKey, applyDocumentSearchFilter } from "@/lib/cache";

export async function GET(request: Request) {
  try {
    const userData = await getUserData();

    if (!userData) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { userId, email } = userData;

    // Parse search param from URL if present
    const { searchParams } = new URL(request.url);
    const search = searchParams.get("search") || "";
    const useCache = searchParams.get("useCache") !== "false"; // Default to using cache

    // Create a cache key
    const cacheKey = createDocumentCacheKey(userId, "company");

    // Try to get from cache if enabled
    if (useCache) {
      const cachedData = documentCache.get(cacheKey);
      if (cachedData) {
        console.log(`[API] Using cached company files (${cachedData.count} files)`);

        // Apply search filtering if needed
        if (search) {
          const filteredFiles = applyDocumentSearchFilter(cachedData.files, search);
          return NextResponse.json({
            files: filteredFiles,
            fromCache: true,
          });
        }

        return NextResponse.json({
          files: cachedData.files,
          fromCache: true,
        });
      }
    }

    // No cache hit, fetch from database
    console.log(`[API] Fetching company files from database`);
    const files = await getCompanyFiles({ email });

    // Store in cache for future use
    documentCache.set(cacheKey, {
      files: files,
      count: files.length,
      source: "company",
    });

    // Apply search filter if needed
    if (search) {
      const filteredFiles = applyDocumentSearchFilter(files, search);
      return NextResponse.json({ files: filteredFiles });
    }

    return NextResponse.json({
      files: files,
    });
  } catch (error) {
    console.error("Failed to fetch company files:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
