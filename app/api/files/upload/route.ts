import { SUPPORTED_CONTENT_TYPES } from "@/components/multimodal/constants";
import { sanitizeS3<PERSON>ey } from "@/lib/utils";
import { PutObjectCommand, S3Client } from "@aws-sdk/client-s3";
import { getUserId } from "@/lib/server/user-actions";
import { NextResponse } from "next/server";

export async function POST(req: Request) {
  const formData = await req.formData();
  let userId = formData.get("userId") as string;

  if (!userId) {
    const authUserId = await getUserId();
    if (!authUserId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }
    userId = authUserId;
  }

  if (req.body === null) {
    return new Response("Request body is empty", { status: 400 });
  }

  const requiredEnv = ["S3_REGION", "S3_ENDPOINT", "S3_ACCESS_KEY_ID", "S3_SECRET_ACCESS_KEY", "S3_PUBLIC_FILE_ENDPOINT"];
  for (const envKey of requiredEnv) {
    if (!process.env[envKey]) {
      return NextResponse.json({ error: `Missing required environment variable: ${envKey}` }, { status: 500 });
    }
  }

  try {
    const file = formData.get("file") as File;
    if (!file) {
      return NextResponse.json({ error: "No file uploaded" }, { status: 400 });
    }

    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json({ error: "File size should be less than 10MB" }, { status: 400 });
    }

    if (!SUPPORTED_CONTENT_TYPES.includes(file.type)) {
      return NextResponse.json({ error: `File type '${file.type}' is not allowed` }, { status: 400 });
    }

    const originalFilename = file.name;
    const sanitizedFilename = sanitizeS3Key(originalFilename);
    const fileBuffer = Buffer.from(await file.arrayBuffer());

    const s3 = new S3Client({
      region: process.env.S3_REGION!,
      endpoint: process.env.S3_ENDPOINT!,
      credentials: {
        accessKeyId: process.env.S3_ACCESS_KEY_ID!,
        secretAccessKey: process.env.S3_SECRET_ACCESS_KEY!,
      },
      forcePathStyle: true,
    });

    const bucket = "personal-knowledge-base";
    const key = sanitizedFilename;

    try {
      await s3.send(
        new PutObjectCommand({
          Bucket: bucket,
          Key: key,
          Body: fileBuffer,
          ContentType: file.type,
          ACL: "public-read",
          ContentLength: file.size,
        })
      );

      const supabaseUrl = process.env.S3_PUBLIC_FILE_ENDPOINT!;
      const publicUrl = `${supabaseUrl}/storage/v1/object/public/${bucket}/${encodeURIComponent(key)}`;
      return NextResponse.json({
        url: publicUrl,
        name: originalFilename,
        type: file.type,
        size: file.size,
      });
    } catch (error: any) {
      console.error("S3 Upload Error:", {
        message: error.message,
        name: error.name,
        stack: error.stack,
        keyUsed: key,
        originalFilename,
      });
      return NextResponse.json(
        {
          error: "Upload to storage failed",
          details: error?.message || String(error),
          keyUsed: key,
        },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error("Request Processing Error:", {
      message: error.message,
      name: error.name,
      stack: error.stack,
    });
    return NextResponse.json(
      {
        error: "Failed to process request",
        details: error?.message || String(error),
      },
      { status: 500 }
    );
  }
}
