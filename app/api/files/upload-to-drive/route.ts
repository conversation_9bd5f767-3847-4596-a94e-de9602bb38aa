import { type NextRequest, NextResponse } from "next/server";
import { getUserData } from "@/lib/server/user-actions";
import { google } from "googleapis";
import { db } from "@/lib/db";
import { document_metadata } from "@/lib/db/schema";
import { generateId } from "ai";
import { Readable } from "node:stream";
import { createHash } from "node:crypto";
import { eq } from "drizzle-orm";

// Calculate SHA256 hash of file content for duplicate detection
async function calculateFileHash(buffer: Buffer): Promise<string> {
  return createHash("sha256").update(buffer).digest("hex");
}

async function getUserFolder(drive: any, userId: string, userEmail: string, userName: string, rootFolderId: string) {
  // First, check if user folder already exists
  const folderName = userName || userEmail.split("@")[0]; // Use userName or email prefix as fallback

  try {
    const existingFolders = await drive.files.list({
      q: `name='${folderName}' and parents in '${rootFolderId}' and mimeType='application/vnd.google-apps.folder' and trashed=false`,
      fields: "files(id, name)",
    });

    if (existingFolders.data.files && existingFolders.data.files.length > 0) {
      // Folder exists, return its ID
      return existingFolders.data.files[0].id;
    }

    // Folder doesn't exist, create it
    const folderMetadata = {
      name: folderName,
      mimeType: "application/vnd.google-apps.folder",
      parents: [rootFolderId],
    };

    const folderResponse = await drive.files.create({
      requestBody: folderMetadata,
      fields: "id",
    });

    const folderId = folderResponse.data.id;

    // Set permissions for the user's folder (only the user can access it)
    await drive.permissions.create({
      fileId: folderId,
      requestBody: {
        role: "writer", // User can read/write their own folder
        type: "user",
        emailAddress: userEmail,
      },
    });

    console.log(`Created folder for user ${userName} (${userEmail}): ${folderId}`);
    return folderId;
  } catch (error) {
    console.error("Error managing user folder:", error);
    throw error;
  }
}

export async function POST(request: NextRequest) {
  try {
    const userData = await getUserData();

    if (!userData) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { userId, email: userEmail, name: userName } = userData;

    const formData = await request.formData();
    const file = formData.get("file") as File;
    const chatId = formData.get("chatId") as string;
    const localAttachmentId = formData.get("localAttachmentId") as string;
    const fileName = formData.get("fileName") as string;
    const contentType = formData.get("contentType") as string;

    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }

    console.log("Upload to Drive request:", {
      fileName,
      fileSize: file.size,
      contentType,
      chatId,
      localAttachmentId,
      userEmail,
      userName,
    });

    // Additional debugging after key processing
    const cleanedPrivateKey = process.env.PRIVATE_KEY
      ? process.env.PRIVATE_KEY.replace(/\\n/g, "\n")
          .replace(/^["']/, "")
          .replace(/["']$/, "")
          .replace(/["'],?$/, "")
          .trim()
      : undefined;

    if (!process.env.GOOGLE_SERVICE_ACCOUNT_KEY_BASE64) {
      console.log("Cleaned private key info:", {
        cleanedKeyLength: cleanedPrivateKey?.length || 0,
        cleanedKeyStartsWith: cleanedPrivateKey?.substring(0, 50) || "undefined",
        cleanedKeyEndsWith: cleanedPrivateKey?.substring(-50) || "undefined",
        hasBeginMarker: cleanedPrivateKey?.includes("-----BEGIN PRIVATE KEY-----") || false,
        hasEndMarker: cleanedPrivateKey?.includes("-----END PRIVATE KEY-----") || false,
      });
    }

    // Initialize Google Drive API with individual environment variables
    let serviceAccountKey;

    // Option 1: Use base64 encoded service account key (most reliable)
    if (process.env.GOOGLE_SERVICE_ACCOUNT_KEY_BASE64) {
      try {
        const decodedKey = Buffer.from(process.env.GOOGLE_SERVICE_ACCOUNT_KEY_BASE64, "base64").toString("utf8");
        serviceAccountKey = JSON.parse(decodedKey);
        console.log("Using base64 encoded service account key");
      } catch (error) {
        console.error("Failed to decode base64 service account key:", error);
        throw new Error("Invalid base64 encoded service account key");
      }
    } else {
      // Option 2: Use individual environment variables
      serviceAccountKey = {
        type: process.env.TYPE || "service_account",
        project_id: process.env.PROJECT_ID,
        private_key_id: process.env.PRIVATE_KEY_ID,
        // Properly format the private key - handle both encoded and raw formats
        private_key: process.env.PRIVATE_KEY
          ? process.env.PRIVATE_KEY.replace(/\\n/g, "\n")
              .replace(/^["']/, "") // Remove leading quote (double or single)
              .replace(/["']$/, "") // Remove trailing quote (double or single)
              .replace(/["'],?$/, "") // Remove trailing quote with optional comma
              .trim()
          : undefined,
        client_email: process.env.CLIENT_EMAIL,
        client_id: process.env.CLIENT_ID,
        auth_uri: process.env.AUTH_URI || "https://accounts.google.com/o/oauth2/auth",
        token_uri: process.env.TOKEN_URI || "https://oauth2.googleapis.com/token",
        auth_provider_x509_cert_url: process.env.AUTH_PROVIDER_X509_CERT_URL || "https://www.googleapis.com/oauth2/v1/certs",
        client_x509_cert_url: process.env.CLIENT_X509_CERT_URL,
        universe_domain: process.env.UNIVERSE_DOMAIN || "googleapis.com",
      };
    }

    // Validate that all required fields are present
    if (!serviceAccountKey.project_id || !serviceAccountKey.private_key || !serviceAccountKey.client_email) {
      throw new Error("Missing required Google Service Account credentials. Please check your environment variables.");
    }

    // Additional validation for private key format
    if (!serviceAccountKey.private_key.includes("BEGIN PRIVATE KEY")) {
      throw new Error('Private key appears to be malformed. It should start with "-----BEGIN PRIVATE KEY-----"');
    }

    const auth_client = new google.auth.GoogleAuth({
      credentials: serviceAccountKey,
      scopes: ["https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.metadata"],
    });

    const drive = google.drive({ version: "v3", auth: auth_client });

    // Get or create user-specific folder
    const rootFolderId = process.env.GOOGLE_DRIVE_FOLDER_ID;
    if (!rootFolderId) {
      throw new Error("GOOGLE_DRIVE_FOLDER_ID not configured");
    }

    const userFolderId = await getUserFolder(drive, userId, userEmail, userName || userEmail.split("@")[0], rootFolderId);

    // Convert File to Buffer
    const fileBuffer = Buffer.from(await file.arrayBuffer());

    // Calculate content hash for robust duplicate detection
    const contentHash = await calculateFileHash(fileBuffer);
    console.log(`File ${fileName} content hash: ${contentHash}`);

    // Check database for existing files with same content hash (most reliable)
    try {
      const existingDbFile = await db.select().from(document_metadata).where(eq(document_metadata.contentHash, contentHash)).limit(1);

      if (existingDbFile.length > 0) {
        const duplicate = existingDbFile[0];
        console.log(`File with identical content already exists in database: ${duplicate.id}`);

        return NextResponse.json({
          success: true,
          fileId: duplicate.id,
          url: duplicate.url || "",
          localAttachmentId,
          message: `Identical file already exists (detected by content)`,
          driveData: {
            name: duplicate.name,
            size: duplicate.size?.toString(),
            url: duplicate.url,
            folderId: userFolderId,
            folderName: userName || userEmail.split("@")[0],
            alreadyExisted: true,
            matchedBy: "content_hash", // Most reliable detection method
          },
        });
      }
    } catch (dbError) {
      console.log("Error checking database for content duplicates:", dbError);
      // Continue with Google Drive check if database check fails
    }

    // Check if file already exists in user's folder
    // We check by name + size combination for better duplicate detection
    try {
      const existingFiles = await drive.files.list({
        q: `name='${fileName}' and parents in '${userFolderId}' and trashed=false`,
        fields: "files(id, name, size, webViewLink, createdTime)",
      });

      if (existingFiles.data.files && existingFiles.data.files.length > 0) {
        // Check if any existing file has the same name AND size
        const duplicateFile = existingFiles.data.files.find((existingFile) => {
          const existingSize = Number.parseInt(existingFile.size || "0");
          const uploadingSize = file.size;
          return existingFile.name === fileName && existingSize === uploadingSize;
        });

        if (duplicateFile) {
          console.log(`File ${fileName} already exists in user folder with same size (${file.size} bytes): ${duplicateFile.id}`);

          const googleDriveUrl = `https://drive.google.com/file/d/${duplicateFile.id}/view`;

          return NextResponse.json({
            success: true,
            fileId: duplicateFile.id,
            url: googleDriveUrl,
            localAttachmentId,
            message: `File already exists in ${userName || userEmail.split("@")[0]}'s folder`,
            driveData: {
              name: duplicateFile.name,
              size: duplicateFile.size,
              webViewLink: duplicateFile.webViewLink,
              createdTime: duplicateFile.createdTime,
              folderId: userFolderId,
              folderName: userName || userEmail.split("@")[0],
              alreadyExisted: true,
              matchedBy: "name_and_size", // For debugging/analytics
            },
          });
        } else {
          // File with same name but different size exists
          console.log(`File ${fileName} exists with different size. Uploading as new file.`);
        }
      }
    } catch (checkError) {
      console.log("Error checking for existing files, proceeding with upload:", checkError);
      // Continue with upload if check fails
    }

    // Create a readable stream from the buffer for Google Drive
    const fileStream = new Readable();
    fileStream.push(fileBuffer);
    fileStream.push(null); // End the stream

    // Prepare file metadata - upload to user's specific folder
    const fileMetadata = {
      name: fileName,
      parents: [userFolderId],
    };

    // Upload file to Google Drive
    const driveResponse = await drive.files.create({
      requestBody: fileMetadata,
      media: {
        mimeType: contentType,
        body: fileStream,
      },
      fields: "id, name, webViewLink, webContentLink, parents",
    });

    const fileId = driveResponse.data.id!;

    // Set file permissions - only the user can access their files
    await drive.permissions.create({
      fileId: fileId,
      requestBody: {
        role: "reader",
        type: "user",
        emailAddress: userEmail,
      },
    });

    const googleDriveUrl = `https://drive.google.com/file/d/${fileId}/view`;

    // Save to document_metadata table and process for AI/RAG
    let documentId: string;
    try {
      documentId = generateId(); // Generate unique ID for database
      await db.insert(document_metadata).values({
        id: documentId,
        name: fileName,
        fileType: contentType,
        createdAt: new Date(),
        updatedAt: new Date(),
        owner: userId,
        url: googleDriveUrl,
        department: "personal", // Chat uploads are personal files
        permissions: [userId], // Only the user can access their files
        path: `chat-uploads/${userName || userEmail.split("@")[0]}/${fileName}`, // Virtual path for organization
        size: file.size, // Store file size for duplicate detection
        contentHash: contentHash,
      });

      console.log(`Saved file metadata to database: ${documentId} (size: ${file.size} bytes)`);

      // Process file for AI/RAG functionality if it's a text-based file
      const textBasedTypes = [
        "text/",
        "application/json",
        "application/pdf",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/msword",
      ];

      const isTextBased = textBasedTypes.some((type) => contentType.includes(type));

      if (isTextBased) {
        const { processFileForIngestion } = await import("@/lib/ai/document-ingestion");

        // For text files, we can process the content directly
        if (contentType.includes("text/") || contentType.includes("application/json")) {
          const fileContent = await file.text();
          await processFileForIngestion(file, fileContent, {
            file_id: documentId,
            chatId,
            userId,
            source: "chat-upload",
            googleDriveUrl,
          });
        } else {
          // For other file types (PDF, DOCX), pass the buffer for future processing
          await processFileForIngestion(file, fileBuffer.buffer, {
            file_id: documentId,
            chatId,
            userId,
            source: "chat-upload",
            googleDriveUrl,
          });
        }

        console.log(`File ${fileName} processed for AI/RAG functionality`);
      }
    } catch (dbError) {
      console.error("Failed to save file metadata to database or process for AI/RAG:", dbError);
      // Don't fail the entire upload if database save fails
      // The file is already in Google Drive
    }

    return NextResponse.json({
      success: true,
      fileId: fileId,
      url: googleDriveUrl,
      localAttachmentId,
      message: `File uploaded successfully to ${userName}'s folder`,
      driveData: {
        name: driveResponse.data.name,
        webViewLink: driveResponse.data.webViewLink,
        webContentLink: driveResponse.data.webContentLink,
        folderId: userFolderId,
        folderName: userName || userEmail.split("@")[0],
        alreadyExisted: false,
        matchedBy: null,
      },
    });
  } catch (error) {
    console.error("Error uploading file to Drive:", error);

    let errorMessage = "Failed to upload file to Drive";
    let statusCode = 500;

    if (error instanceof Error) {
      // Check for specific error types
      if (error.message.includes("quota")) {
        errorMessage = "Google Drive quota exceeded. Please try again later.";
        statusCode = 507; // Insufficient Storage
      } else if (error.message.includes("permission")) {
        errorMessage = "Permission denied. Please check Google Drive permissions.";
        statusCode = 403; // Forbidden
      } else if (error.message.includes("not found")) {
        errorMessage = "Google Drive folder not found. Please contact support.";
        statusCode = 404; // Not Found
      } else {
        errorMessage = `Upload failed: ${error.message}`;
      }
    }

    return NextResponse.json(
      {
        error: errorMessage,
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: statusCode }
    );
  }
}
