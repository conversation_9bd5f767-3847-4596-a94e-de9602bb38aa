import { getToolsForMcp } from "@/lib/ai/mcp";
import { MCP_CONFIGS, type McpConfig } from "@/lib/ai/mcp/config";
import type { NextRequest } from "next/server";
import { createMcpConfig, deleteMcpConfig, getMcpConfigsByUserId, setActiveMcpConfig, toggleMcpConfig } from "@/lib/db/queries";
import { getUserId } from "@/lib/server/user-actions";

export async function GET() {
  console.log("API GET /api/mcp: Received request.");
  const userId = await getUserId();
  if (!userId) {
    console.error("API GET /api/mcp: User not authenticated.");
    return Response.json("User not authenticated", { status: 401 });
  }
  try {
    const dbConfigs = await getMcpConfigsByUserId({ userId });
    const allConfigs = new Map<string, any>();

    // Add static configs first
    for (const [id, config] of Object.entries(MCP_CONFIGS)) {
      const staticConfig = config as McpConfig; // Type assertion
      allConfigs.set(staticConfig.name, {
        id,
        name: staticConfig.name,
        config: staticConfig.config,
        active: staticConfig.autoEnabled ?? false,
        isStatic: true,
        userId: null,
        credential: null,
        createdAt: null,
        updatedAt: null,
      });
    }

    // Overwrite with user's DB configs
    for (const dbConfig of dbConfigs) {
      allConfigs.set(dbConfig.name, { ...dbConfig, isStatic: false });
    }

    const initialConfigs = Array.from(allConfigs.values());

    // Fetch tools for each MCP
    const configsWithTools = await Promise.all(
      initialConfigs.map(async (config) => {
        if (!config.active) {
          return { ...config, tools: [] };
        }
        try {
          const tools = await getToolsForMcp(config);
          if (!tools) {
            return { ...config, tools: [] };
          }
          // Convert Record<string, Tool> to array of tool descriptions
          const toolArray = Object.entries(tools).map(([name, tool]) => ({
            name,
            description: tool.description || "",
            parameters: tool.parameters || {},
          }));

          return {
            ...config,
            tools: toolArray,
          };
        } catch (error) {
          console.error(`Failed to get tools for MCP ${config.name}:`, error);
          return { ...config, tools: [] };
        }
      })
    );

    console.log(`API GET /api/mcp: Found ${configsWithTools.length} total configs for user ${userId}, now with tools.`);
    return Response.json(configsWithTools);
  } catch (error) {
    console.error("Failed to fetch MCP configs:", error);
    return Response.json("Failed to fetch MCP configs!", { status: 500 });
  }
}

export async function POST(request: Request) {
  console.log("API POST /api/mcp: Received request.");
  const userId = await getUserId();
  if (!userId) {
    console.error("API POST /api/mcp: User not authenticated.");
    return Response.json("User not authenticated", { status: 401 });
  }
  try {
    const { name, config, credential } = await request.json();
    if (!name || !config) {
      return Response.json("Name and config are required", { status: 400 });
    }
    const cfg = await createMcpConfig({ userId, name, config, credential });
    console.log(`API POST /api/mcp: Created config for user ${userId}.`);
    return Response.json(cfg, { status: 201 });
  } catch (error) {
    console.error("Failed to create MCP config:", error);
    return Response.json("Failed to create MCP config!", { status: 500 });
  }
}

export async function PUT(request: Request) {
  console.log("API PUT /api/mcp: Received request.");
  const userId = await getUserId();
  if (!userId) {
    console.error("API PUT /api/mcp: User not authenticated.");
    return Response.json("User not authenticated", { status: 401 });
  }
  try {
    const { id } = await request.json();
    if (!id) return Response.json("ID is required", { status: 400 });
    const cfg = await toggleMcpConfig({ userId, id });
    console.log(`API PUT /api/mcp: Toggled config ${id} for user ${userId}.`);
    return Response.json(cfg);
  } catch (error) {
    console.error("Failed to toggle MCP config:", error);
    return Response.json("Failed to toggle MCP config!", { status: 500 });
  }
}

export async function PATCH(request: Request) {
  console.log("API PATCH /api/mcp: Received request.");
  const userId = await getUserId();
  if (!userId) {
    console.error("API PATCH /api/mcp: User not authenticated.");
    return Response.json("User not authenticated", { status: 401 });
  }
  try {
    const { id } = await request.json();
    if (!id) return Response.json("ID is required", { status: 400 });
    const cfg = await setActiveMcpConfig({ userId, id });
    console.log(`API PATCH /api/mcp: Set active config to ${id} for user ${userId}.`);
    return Response.json(cfg);
  } catch (error) {
    console.error("Failed to activate MCP config:", error);
    return Response.json("Failed to activate MCP config!", { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  console.log("API DELETE /api/mcp: Received request.");
  const userId = await getUserId();
  if (!userId) {
    console.error("API DELETE /api/mcp: User not authenticated.");
    return Response.json("User not authenticated", { status: 401 });
  }
  try {
    const id = request.nextUrl.searchParams.get("id");
    if (!id) return Response.json("ID is required in query parameters", { status: 400 });
    const cfg = await deleteMcpConfig({ userId, id });
    console.log(`API DELETE /api/mcp: Deleted config ${id} for user ${userId}.`);
    return Response.json(cfg ?? {});
  } catch (error) {
    console.error("Failed to delete MCP config:", error);
    return Response.json("Failed to delete MCP config!", { status: 500 });
  }
}
