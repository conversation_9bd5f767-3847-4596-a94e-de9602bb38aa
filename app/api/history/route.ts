// Add Node.js runtime for Clerk middleware support
export const runtime = "nodejs";
import { getChatsByUserId } from "@/lib/db/queries";
import { getUserId } from "@/lib/server/user-actions";
import type { NextRequest } from "next/server";

export async function GET(request: NextRequest) {
  const { searchParams } = request.nextUrl;

  const limit = Number.parseInt(searchParams.get("limit") || "10");
  const startingAfter = searchParams.get("starting_after");
  const endingBefore = searchParams.get("ending_before");

  if (startingAfter && endingBefore) {
    return Response.json("Only one of starting_after or ending_before can be provided!", { status: 400 });
  }

  const userId = await getUserId();

  if (!userId) {
    return Response.json("User not found!", { status: 404 });
  }

  try {
    const chats = await getChatsByUserId({
      id: userId,
      limit,
      startingAfter,
      endingBefore,
    });

    return Response.json(chats);
  } catch {
    return Response.json("Failed to fetch chats!", { status: 500 });
  }
}
