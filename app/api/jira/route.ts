import { type NextRequest, NextResponse } from "next/server";
import { Version3Client } from "jira.js";
import { getUserId } from "@/lib/server/user-actions";

// Get credentials from server environment variables
const JIRA_HOST = process.env.JIRA_BASE_URL;
const JIRA_EMAIL = process.env.JIRA_ADMIN_EMAIL;
const JIRA_API_TOKEN = process.env.JIRA_API_TOKEN;

if (!JIRA_HOST || !JIRA_EMAIL || !JIRA_API_TOKEN) {
  throw new Error("Jira environment variables are not properly configured");
}

// Create a Jira client with server-side credentials
const client = new Version3Client({
  host: JIRA_HOST,
  authentication: {
    basic: {
      email: JIRA_EMAIL,
      apiToken: JIRA_API_TOKEN,
    },
  },
});

// Helper function to build JQL queries
const buildJqlQuery = (params: URLSearchParams) => {
  const email = params.get("email");
  const jql = [`assignee = "${email}"`];

  // Add date filter if present
  const dateFilter = params.get("dateFilter");
  if (dateFilter === "today") {
    jql.push("updated >= startOfDay()");
  } else if (dateFilter === "week") {
    jql.push("updated >= startOfWeek()");
  } else if (dateFilter === "month") {
    jql.push("updated >= startOfMonth()");
  } else if (dateFilter === "all") {
    // No filter for "all"
  }

  // Add status filter if present
  const status = params.get("status");
  if (status && status !== "all") {
    jql.push(`status = "${status}"`);
  }

  // Add search term if present
  const search = params.get("search");
  if (search) {
    jql.push(`(summary ~ "${search}" OR description ~ "${search}")`);
  }

  // Build the base query with AND conditions
  let query = jql.join(" AND ");

  // Add sorting as a separate clause, not as part of the AND conditions
  query += " ORDER BY updated DESC";

  return query;
};

export async function GET(request: NextRequest) {
  // Check if the user is authenticated
  const userId = await getUserId();
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Get the query parameters
  const searchParams = request.nextUrl.searchParams;
  const email = searchParams.get("email");

  if (!email) {
    return NextResponse.json({ error: "Email parameter is required" }, { status: 400 });
  }

  try {
    // Build JQL query with filters
    const jql = buildJqlQuery(searchParams);

    console.log("Using JQL query:", jql); // Debug logging

    // Fields to fetch - now including additional details
    const fields = ["summary", "status", "priority", "duedate", "project", "description", "creator", "created", "updated"];

    const searchResult = await client.issueSearch.searchForIssuesUsingJqlPost({
      jql,
      fields,
    });

    if (!searchResult.issues || searchResult.issues.length === 0) {
      return NextResponse.json([]);
    }

    const tasks = searchResult.issues.map((issue: any) => ({
      id: issue.id,
      key: issue.key,
      title: issue.fields.summary,
      status: issue.fields.status
        ? {
            id: issue.fields.status.id,
            name: issue.fields.status.name,
            statusCategory: issue.fields.status.statusCategory?.name,
          }
        : null,
      priority: issue.fields.priority
        ? {
            id: issue.fields.priority.id,
            name: issue.fields.priority.name,
            iconUrl: issue.fields.priority.iconUrl,
          }
        : null,
      dueDate: issue.fields.duedate,
      project: issue.fields.project
        ? {
            id: issue.fields.project.id,
            key: issue.fields.project.key,
            name: issue.fields.project.name,
          }
        : null,
      description: issue.fields.description,
      created: issue.fields.created,
      updated: issue.fields.updated,
      creator: issue.fields.creator
        ? {
            displayName: issue.fields.creator.displayName,
            emailAddress: issue.fields.creator.emailAddress,
          }
        : null,
    }));

    return NextResponse.json(tasks);
  } catch (error: any) {
    console.error("Error fetching Jira tasks:", error);

    // Extract the specific Jira API error message if available
    let errorMessage = "Failed to fetch tasks";
    if (error.response?.data?.errorMessages?.length > 0) {
      errorMessage = error.response.data.errorMessages[0];
    } else if (error.message) {
      errorMessage = error.message;
    }

    return NextResponse.json({ error: errorMessage }, { status: error.status || 500 });
  }
}

// Add a POST route to get a single ticket's complete details
export async function POST(request: NextRequest) {
  // Check if the user is authenticated
  const userId = await getUserId();
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = await request.json();
    const { ticketKey } = body;

    if (!ticketKey) {
      return NextResponse.json({ error: "Ticket key is required" }, { status: 400 });
    }

    // Get complete ticket details including comments
    const issueDetails = await client.issues.getIssue({
      issueIdOrKey: ticketKey,
      fields: ["summary", "status", "priority", "duedate", "project", "description", "creator", "assignee", "created", "updated", "issuetype"],
    });

    // Get comments in a separate request
    const comments = await client.issueComments.getComments({
      issueIdOrKey: ticketKey,
    });

    // Combine all details into a single response
    const fullTicketDetails = {
      id: issueDetails.id,
      key: issueDetails.key,
      title: issueDetails.fields.summary,
      status: issueDetails.fields.status
        ? {
            id: issueDetails.fields.status.id,
            name: issueDetails.fields.status.name,
            statusCategory: issueDetails.fields.status.statusCategory?.name,
          }
        : null,
      priority: issueDetails.fields.priority
        ? {
            id: issueDetails.fields.priority.id,
            name: issueDetails.fields.priority.name,
            iconUrl: issueDetails.fields.priority.iconUrl,
          }
        : null,
      dueDate: issueDetails.fields.duedate,
      project: issueDetails.fields.project
        ? {
            id: issueDetails.fields.project.id,
            key: issueDetails.fields.project.key,
            name: issueDetails.fields.project.name,
          }
        : null,
      description: issueDetails.fields.description,
      created: issueDetails.fields.created,
      updated: issueDetails.fields.updated,
      issueType: issueDetails.fields.issuetype
        ? {
            id: issueDetails.fields.issuetype.id,
            name: issueDetails.fields.issuetype.name,
            iconUrl: issueDetails.fields.issuetype.iconUrl,
          }
        : null,
      creator: issueDetails.fields.creator
        ? {
            displayName: issueDetails.fields.creator.displayName,
            emailAddress: issueDetails.fields.creator.emailAddress,
          }
        : null,
      assignee: issueDetails.fields.assignee
        ? {
            displayName: issueDetails.fields.assignee.displayName,
            emailAddress: issueDetails.fields.assignee.emailAddress,
          }
        : null,
      comments: comments.comments || [],
    };

    return NextResponse.json(fullTicketDetails);
  } catch (error: any) {
    console.error("Error fetching Jira ticket details:", error);

    // Extract the specific Jira API error message if available
    let errorMessage = "Failed to fetch ticket details";
    if (error.response?.data?.errorMessages?.length > 0) {
      errorMessage = error.response.data.errorMessages[0];
    } else if (error.message) {
      errorMessage = error.message;
    }

    return NextResponse.json({ error: errorMessage }, { status: error.status || 500 });
  }
}
