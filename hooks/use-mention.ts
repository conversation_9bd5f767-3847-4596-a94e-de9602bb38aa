"use client";

import type { JiraTask } from "@/components/jira-tasks-dialog";
import { type ExtendedAttachment, createFileAttachmentFromMention, createTicketAttachmentFromMention } from "@/components/multimodal/types";
import { useCallback, useState } from "react";

/**
 * Custom hook for converting mentions (tickets and files) to attachments
 */
export function useMention() {
  // Local state for UI elements
  const [mentionSelectorVisible, setMentionSelectorVisible] = useState(false);
  const [mentionSelectorPosition, setMentionSelectorPosition] = useState<{ x: number; y: number } | null>(null);
  const [lastAtIndex, setLastAtIndex] = useState<number>(-1);

  // Generic handler for converting mentions to attachments
  const addMentionAsAttachment = useCallback(
    (mentionData: JiraTask | ExtendedAttachment, addToAttachments: (attachment: ExtendedAttachment) => void, type: "ticket" | "file") => {
      try {
        const attachment =
          type === "ticket"
            ? createTicketAttachmentFromMention(mentionData as JiraTask)
            : createFileAttachmentFromMention(mentionData as ExtendedAttachment);

        addToAttachments(attachment);
      } catch (error) {
        console.error(`Error adding ${type} mention as attachment:`, error);
      }
    },
    []
  );

  // Convert ticket mention to attachment and add to attachments
  const handleTicketMention = useCallback(
    (ticket: JiraTask, addToAttachments: (attachment: ExtendedAttachment) => void) => {
      addMentionAsAttachment(ticket, addToAttachments, "ticket");
    },
    [addMentionAsAttachment]
  );

  // Convert file mention to attachment and add to attachments
  const handleFileMention = useCallback(
    (file: ExtendedAttachment, addToAttachments: (attachment: ExtendedAttachment) => void) => {
      addMentionAsAttachment(file, addToAttachments, "file");
    },
    [addMentionAsAttachment]
  );

  // Open the mention selector at a specific position
  const openMentionSelector = useCallback((position: { x: number; y: number }, atIndex: number) => {
    setMentionSelectorPosition(position);
    setMentionSelectorVisible(true);
    setLastAtIndex(atIndex);
  }, []);

  // Close the mention selector
  const closeMentionSelector = useCallback(() => {
    setMentionSelectorVisible(false);
    setLastAtIndex(-1);
  }, []);

  return {
    handleTicketMention,
    handleFileMention,

    mentionSelectorVisible,
    mentionSelectorPosition,
    lastAtIndex,
    openMentionSelector,
    closeMentionSelector,
  };
}
