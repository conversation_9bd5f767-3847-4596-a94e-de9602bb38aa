"use client";
import type { JiraTask } from "@/components/jira-tasks-dialog";
import { useModalsStore } from "@/lib/store/modals";
import { fetcher, postFetcher } from "@/lib/utils";
import { useUser } from "@clerk/nextjs";
import { useCallback } from "react";
import useS<PERSON> from "swr";

// Type definitions
interface UseJiraTasksParams {
  searchTerm?: string;
  dateFilter?: string;
  statusFilter?: string;
}

interface UseJiraTicketParams {
  ticketKey?: string;
  initialData?: JiraTask;
}

interface UseJiraTicketsParams {
  ticketKeys?: string[];
  initialData?: Record<string, JiraTask>;
}

/**
 * Hook for fetching Jira tasks with filtering options
 * Gets the current user's email automatically
 */
export function useJiraTasks({ searchTerm, dateFilter, statusFilter }: UseJiraTasksParams = {}) {
  const { user, isLoaded: userIsLoaded } = useUser();
  const { jiraTasksOpen } = useModalsStore();

  const shouldFetch = jiraTasksOpen;

  // Get current user email from Clerk
  const userEmail = user?.primaryEmailAddress?.emailAddress;

  // Build the API URL with all query parameters
  const buildApiUrl = useCallback(() => {
    if (!userEmail || !shouldFetch) return null;

    try {
      const params = new URLSearchParams();
      params.append("email", userEmail);

      if (searchTerm) params.append("search", searchTerm);
      if (dateFilter && dateFilter !== "all") params.append("dateFilter", dateFilter);
      if (statusFilter && statusFilter !== "all") params.append("status", statusFilter);

      return `/api/jira?${params.toString()}`;
    } catch (error) {
      console.error("Failed to build Jira API URL:", error);
      return null;
    }
  }, [userEmail, searchTerm, dateFilter, statusFilter, shouldFetch]);

  const apiUrl = buildApiUrl();

  const { data, error, isLoading, mutate } = useSWR<JiraTask[]>(apiUrl, fetcher, {
    revalidateOnFocus: false,
    dedupingInterval: 30000, // 30 seconds
    errorRetryCount: 3,
  });

  // Provide a refetch function to manually trigger revalidation
  const refetch = useCallback(() => {
    try {
      return mutate();
    } catch (error) {
      console.error("Failed to refetch Jira tasks:", error);
      throw error;
    }
  }, [mutate]);

  return {
    tasks: data || [],
    error,
    isLoading: isLoading || !userIsLoaded || !apiUrl,
    refetch,
    userEmail,
  };
}

// Utility function to create a cache key for tickets
const createCacheKey = (ticketKey?: string) => {
  if (!ticketKey) return null;
  return `jira-ticket-${ticketKey}`;
};

/**
 * Hook for fetching and caching a single Jira ticket's details
 */
export function useJiraTicket({ ticketKey, initialData }: UseJiraTicketParams) {
  const cacheKey = createCacheKey(ticketKey);

  const { data, error, isLoading, mutate } = useSWR<JiraTask>(
    cacheKey,
    async () => {
      try {
        if (!ticketKey) {
          throw new Error("Ticket key is required");
        }

        return await postFetcher("/api/jira", { ticketKey });
      } catch (error) {
        console.error(`Failed to fetch Jira ticket details for ${ticketKey}:`, error);
        throw error;
      }
    },
    {
      fallbackData: initialData,
      revalidateOnFocus: false,
      dedupingInterval: 60000, // 1 minute
      errorRetryCount: 2,
    }
  );

  // Function to manually trigger a refresh
  const refetch = useCallback(() => {
    try {
      return mutate();
    } catch (error) {
      console.error(`Failed to refetch Jira ticket ${ticketKey}:`, error);
      throw error;
    }
  }, [mutate, ticketKey]);

  return {
    ticket: data,
    error,
    isLoading,
    refetch,
  };
}

/**
 * Hook for fetching and caching multiple Jira tickets
 */
export function useJiraTickets({ ticketKeys, initialData }: UseJiraTicketsParams) {
  // Generate a cache key for the set of tickets
  const cacheKey = ticketKeys?.length ? `jira-tickets-${ticketKeys.sort().join("-")}` : null;

  const { data, error, isLoading, mutate } = useSWR<Record<string, JiraTask>>(
    cacheKey,
    async () => {
      try {
        if (!ticketKeys?.length) return {};

        // Create a map to store results
        const results: Record<string, JiraTask> = {};

        // Fetch tickets in parallel
        const ticketPromises = ticketKeys.map(async (key) => {
          try {
            const data = await postFetcher("/api/jira", { ticketKey: key });
            return { key, data };
          } catch (error) {
            console.error(`Error fetching Jira ticket ${key}:`, error);
            return { key, error };
          }
        });

        const ticketResults = await Promise.all(ticketPromises);

        // Process results
        for (const result of ticketResults) {
          if ("data" in result && result.data) {
            results[result.key] = result.data;
          }
        }

        return results;
      } catch (error) {
        console.error("Failed to fetch multiple Jira tickets:", error);
        throw error;
      }
    },
    {
      fallbackData: initialData,
      revalidateOnFocus: false,
      dedupingInterval: 60000, // 1 minute
      errorRetryCount: 2,
    }
  );

  // Function to manually trigger a refresh
  const refetch = useCallback(() => {
    try {
      return mutate();
    } catch (error) {
      console.error("Failed to refetch multiple Jira tickets:", error);
      throw error;
    }
  }, [mutate]);

  // Convert the record to an array for easier consumption
  const tickets = data ? Object.values(data) : [];

  return {
    tickets,
    ticketsMap: data || {},
    error,
    isLoading,
    refetch,
  };
}

/**
 * Utility to extract ticket keys from text using regex
 */
export function extractTicketKeys(text: string): string[] {
  try {
    // Regex to match common Jira ticket formats like "PROJECT-123"
    const ticketRegex = /\b([A-Z]+-\d+)\b/g;
    const matches = text.match(ticketRegex) || [];

    // Return unique keys only
    return [...new Set(matches)];
  } catch (error) {
    console.error("Failed to extract Jira ticket keys from text:", error);
    return [];
  }
}
