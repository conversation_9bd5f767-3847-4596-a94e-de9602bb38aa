import { useEffect, useState } from "react";
import { parseJsonCookie } from "@/lib/utils";

/**
 * Hook to get and update a value from a JSON cookie
 * @param targetKey - The key to extract from the JSON cookie
 * @returns [value, setValue] tuple where value is the current cookie value and setValue updates it
 */
export function useCookieValue(targetKey: string): [string, (newValue: string) => void] {
  const [value, setValue] = useState<string>("");

  useEffect(() => {
    // Get initial value from cookie
    const cookieValue = parseJsonCookie(document.cookie, targetKey);
    setValue(cookieValue);
  }, [targetKey]);

  const updateValue = (newValue: string) => {
    // Update state
    setValue(newValue);

    try {
      // Get current cookie value
      const currentOptions = parseJsonCookie(document.cookie, targetKey);
      // Parse existing options or create new object
      const options = currentOptions ? JSON.parse(decodeURIComponent(currentOptions)) : {};

      // Update the target key
      options[targetKey] = newValue;

      // Save back to cookie
      document.cookie = `${JSON.stringify(options)}; path=/`;
    } catch (error) {
      console.error("Error updating cookie value:", error);
    }
  };

  return [value, updateValue];
}
