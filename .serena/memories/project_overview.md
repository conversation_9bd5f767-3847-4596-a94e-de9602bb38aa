# Project Overview

This is a monorepo containing two main projects:

## 1. Main Application (Root)
- **Name**: ai-chatbot (v3.0.19)
- **Description**: An AI chatbot application built with Next.js
- **Tech Stack**:
  - Framework: Next.js 15 with App Router
  - UI: Radix UI + shadcn/ui + Tailwind CSS
  - Database: PostgreSQL with Drizzle ORM
  - Authentication: Clerk
  - AI: AI SDK with multiple providers (Anthropic, OpenAI, Google, Perplexity, XAI)
  - State Management: Zustand + SWR
  - Testing: Playwright
  - Code Quality: Biome + ESLint

## 2. Chrome Extension (optimus-sidepanel)
- **Name**: optimus-sidepanel (v0.0.1)
- **Description**: Chrome extension providing sidepanel integration for the main Optimus application
- **Tech Stack**:
  - Framework: Plasmo (Chrome Extension framework)
  - UI: React 18 + TypeScript + Tailwind CSS
  - State Management: Zustand + Chrome Storage API
  - Authentication: Clerk Chrome Extension integration
  - Build Tool: Plasmo

The extension provides:
- AI chat in browser sidepanel
- Context extraction from web pages
- Floating action buttons for quick access
- Integration with the main Optimus API