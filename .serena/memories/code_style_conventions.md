# Code Style and Conventions

## TypeScript/JavaScript
- TypeScript is used throughout both projects
- React 19.1.0 for main app, React 18.2.0 for extension
- Functional components with hooks
- Type definitions in separate `.ts` files or inline

## Formatting and Linting
### Main Application
- **Biome** for formatting and linting (biome.jsonc configuration)
- **ESLint** for additional linting rules (.eslintrc.json)
- Run `pnpm lint` or `pnpm format` before committing

### Chrome Extension
- **Prettier** for formatting (.prettierrc.mjs)
- Import sorting with @ianvs/prettier-plugin-sort-imports

## File Organization
### Main App Structure
- `app/` - Next.js App Router pages and API routes
- `components/` - React components organized by feature
- `lib/` - Core business logic and utilities
- `hooks/` - Custom React hooks

### Extension Structure
- `src/background/` - Background script and message handlers
- `src/contents/` - Content scripts
- `src/components/` - React components
- `src/hooks/` - Custom hooks
- `src/utils/` - Utility functions
- `src/api.ts` - Consolidated API client

## Naming Conventions
- Components: PascalCase (e.g., `MessageItem.tsx`)
- Hooks: camelCase with 'use' prefix (e.g., `useChat.ts`)
- Utilities: camelCase (e.g., `contentExtraction.ts`)
- Constants: UPPER_SNAKE_CASE or camelCase
- Types/Interfaces: PascalCase

## Best Practices
- Keep components focused and single-purpose
- Extract complex logic to custom hooks
- Use TypeScript strict mode
- Avoid inline styles, use Tailwind CSS classes
- Handle errors gracefully with try-catch blocks