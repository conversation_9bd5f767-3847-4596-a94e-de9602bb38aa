# Task Completion Checklist

When completing any coding task in this project, follow these steps:

## 1. Code Quality Checks
### Main Application
- Run `pnpm lint` to check for linting errors
- Run `pnpm format` to ensure proper formatting
- Fix any TypeScript errors

### Chrome Extension (in optimus-sidepanel/)
- Run `pnpm build` to ensure the extension builds without errors
- Check for TypeScript compilation errors

## 2. Testing
- If tests exist for the modified code, run `pnpm test`
- For UI changes, manually test in the browser
- For extension changes, load the unpacked extension and test

## 3. Build Verification
- Run `pnpm build` to ensure production build works
- Check for any build warnings or errors

## 4. Git Workflow
- Review changes with `git status` and `git diff`
- Stage relevant files with `git add`
- Write clear, descriptive commit messages
- Do NOT commit unless explicitly asked by the user

## 5. Documentation
- Update relevant documentation if API changes
- Add comments for complex logic
- Update types/interfaces if data structures change

## Important Notes
- User has specified NOT to run `pnpm run dev` (already running)
- Always ask for approval before making significant changes
- Keep files under 450 lines of code
- Break large tasks into smaller steps