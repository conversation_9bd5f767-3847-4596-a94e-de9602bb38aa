# Chrome Extension Architecture

## Overview
The optimus-sidepanel is a Chrome extension built with Plasmo framework that provides AI chat functionality in the browser sidepanel.

## Key Components

### Background Script (`src/background/`)
- Central message routing hub
- Context caching (30-minute TTL)
- Handles communication between content scripts and sidepanel

### Content Scripts (`src/contents/`)
- `index.ts` - Main content script for context extraction
- `floating-actions.tsx` - Floating buttons for text selection

### Sidepanel (`src/sidepanel.tsx`)
- Main UI for AI chat interface
- Uses React with Tailwind CSS
- Manages conversation state with Zustand

### API Integration (`src/api.ts`)
- Consolidated API client
- Methods: `sendChatMessage()`, `storeToMemory()`, `handleStreamingResponse()`
- Cookie-based authentication with Clerk

### Message Types
Essential message types for communication:
- `PAGE_CONTEXT` - Content → Background
- `POST_MESSAGE_TO_CHAT` - Background → Sidepanel
- `REQUEST_CONTEXT` - Sidepanel → Background
- `CTX_UPDATE` - Background → Sidepanel
- `OPEN_SIDEPANEL`, `TOGGLE_SIDEPANEL` - Navigation

## Recent Refactoring
The extension was recently simplified:
- Removed complex sync service (401 lines)
- Consolidated API calls into single file
- Extracted chat logic into `useChat` hook
- Simplified storage to Zustand + Chrome Storage

## Development Flow
1. Run `pnpm dev` for hot reload development
2. Load extension from `build/chrome-mv3-dev`
3. Test in Chrome with developer mode enabled