# Suggested Commands

## Main Application Commands
### Development
- `pnpm dev` - Start development server
- `pnpm build` - Build for production
- `pnpm start` - Start production server

### Code Quality
- `pnpm lint` - Run ESLint and Biome linting with auto-fix
- `pnpm lint:fix` - Run linting with fixes
- `pnpm format` - Format code with Biome

### Database
- `pnpm db:generate` - Generate Drizzle schema migrations
- `pnpm db:migrate` - Run database migrations
- `pnpm db:studio` - Open Drizzle studio
- `pnpm db:push` - Push schema changes to database
- `pnpm db:pull` - Pull schema from database
- `pnpm db:check` - Check migration status

### Testing
- `pnpm test` - Run Playwright tests

## Chrome Extension Commands (in optimus-sidepanel/)
### Development
- `pnpm dev` - Start development server with hot reload
- `pnpm build` - Build extension for production
- `pnpm package` - Package extension for distribution

## System Commands (Darwin/macOS)
- `git` - Version control
- `ls` - List directory contents
- `cd` - Change directory
- `grep` or `rg` (ripgrep) - Search files
- `find` - Find files and directories

## Package Manager
- Using `pnpm` as the package manager (v9.12.3)