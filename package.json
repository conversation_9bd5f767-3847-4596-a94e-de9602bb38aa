{"name": "ai-chatbot", "version": "3.0.19", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build-tsx": "tsx lib/db/migrate && next build", "start": "next start", "lint": "next lint && biome lint --write --unsafe", "lint:fix": "next lint --fix && biome lint --write --unsafe", "format": "biome format --write", "db:setup": "bash scripts/setup-db.sh", "db:generate": "drizzle-kit generate", "db:migrate": "npx tsx lib/db/migrate.ts", "db:studio": "drizzle-kit studio", "db:push": "drizzle-kit push", "db:pull": "drizzle-kit pull", "db:check": "drizzle-kit check", "db:up": "drizzle-kit up", "test": "export PLAYWRIGHT=True && pnpm exec playwright test"}, "dependencies": {"@agentic/google-drive": "^7.6.4", "@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/google": "^1.2.22", "@ai-sdk/openai": "^1.3.23", "@ai-sdk/perplexity": "^1.1.9", "@ai-sdk/react": "^1.2.11", "@ai-sdk/xai": "^1.2.15", "@aws-sdk/client-s3": "^3.823.0", "@clerk/nextjs": "^6.19.1", "@clerk/themes": "^2.2.42", "@codemirror/lang-javascript": "^6.2.2", "@codemirror/lang-python": "^6.1.6", "@codemirror/state": "^6.5.0", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.35.3", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@qdrant/js-client-rest": "^1.14.1", "@qdrant/qdrant-js": "^1.14.1", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.6", "@radix-ui/react-visually-hidden": "^1.2.2", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-table": "^8.21.3", "@types/jsonwebtoken": "^9.0.9", "@types/react-window": "^1.8.8", "@upstash/ratelimit": "^2.0.5", "@vercel/analytics": "^1.3.1", "@vercel/blob": "^0.24.1", "@vercel/functions": "^2.0.0", "@vercel/kv": "^3.0.0", "@vercel/otel": "^1.13.0", "@vercel/postgres": "^0.10.0", "ai": "4.3.13", "bcrypt-ts": "^5.0.2", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "codemirror": "^6.0.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "diff-match-patch": "^1.0.5", "dotenv": "^16.4.5", "drizzle-orm": "^0.43.1", "fast-deep-equal": "^3.1.3", "framer-motion": "^11.18.2", "geist": "^1.3.1", "google-auth-library": "^9.15.1", "googleapis": "^149.0.0", "jira.js": "^5.2.0", "jsonwebtoken": "^9.0.2", "langsmith": "^0.3.31", "lru-cache": "^11.1.0", "lucide-react": "^0.509.0", "nanoid": "^5.0.8", "next": "15.3.2", "next-themes": "^0.3.0", "node-domexception": "^2.0.2", "orderedmap": "^2.1.1", "papaparse": "^5.5.2", "postgres": "^3.4.4", "prosemirror-example-setup": "^1.2.3", "prosemirror-inputrules": "^1.4.0", "prosemirror-markdown": "^1.13.1", "prosemirror-model": "^1.23.0", "prosemirror-schema-basic": "^1.2.3", "prosemirror-schema-list": "^1.4.1", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.34.3", "react": "19.1.0", "react-data-grid": "7.0.0-beta.47", "react-dom": "19.1.0", "react-markdown": "^9.0.1", "react-resizable-panels": "^2.1.7", "react-textarea-autosize": "^8.5.9", "react-window": "^1.8.11", "recharts": "^2.15.3", "redis": "^5.0.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "resumable-stream": "^2.0.0", "server-only": "^0.0.1", "sonner": "^1.5.0", "swr": "^2.2.5", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.1", "uuid": "^11.1.0", "zod": "^3.23.8", "zustand": "^5.0.4"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@playwright/test": "^1.50.1", "@tailwindcss/postcss": "^4.1.6", "@tailwindcss/typography": "^0.5.15", "@types/d3-scale": "^4.0.8", "@types/node": "^22.8.6", "@types/papaparse": "^5.3.15", "@types/pdf-parse": "^1.1.4", "@types/react": "19.1.3", "@types/react-dom": "19.1.3", "@types/uuid": "^10.0.0", "drizzle-kit": "^0.30.0", "eslint": "^8.57.1", "eslint-config-next": "15.3.2", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-tailwindcss": "^3.17.5", "postcss": "^8.5.3", "tailwindcss": "^4.1.6", "tsx": "^4.19.1", "tw-animate-css": "^1.2.9", "typescript": "^5.6.3"}, "packageManager": "pnpm@9.12.3", "pnpm": {"overrides": {"@types/react": "19.1.3", "@types/react-dom": "19.1.3"}}, "overrides": {"react-is": "19.1.0"}}