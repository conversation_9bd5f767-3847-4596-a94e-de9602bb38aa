/**
 * Content Script Context Message Handler
 *
 * Handles context extraction requests from background script.
 * Uses Plasmo messaging patterns for clean communication.
 */

import type { PlasmoMessaging } from "@plasmohq/messaging"

import type { PageContext } from "~types"
import { debugExtraction } from "~utils/content-extraction"

interface ContextRequest {
  tabId?: number
  method: string
}

interface ContextResponse {
  success: boolean
  context: any
  error?: string
}

const handler: PlasmoMessaging.MessageHandler<
  ContextRequest,
  ContextResponse
> = async (req, res) => {
  console.log("[debug-context] Context extraction requested")

  const { method } = req.body || {}
  if (!method) {
    res.send({ success: false, context: null, error: "Method is required" })
    return
  }

  try {
    const context: PageContext = {
      url: window.location.href,
      title: document.title,
      pageContent: debugExtraction(method),
      timestamp: Date.now()
    }

    res.send({ success: true, context })
  } catch (error) {
    console.error("[Content] Context extraction failed:", error)
    res.send({
      success: false,
      context: null,
      error: error instanceof Error ? error.message : "Unknown error"
    })
  }
}

export default handler
