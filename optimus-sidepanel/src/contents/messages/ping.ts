/**
 * Content Script Ping Message Handler
 * 
 * Handles ping requests to check if content script is active.
 * Used by TabSelectionDialog to validate available tabs.
 */

import type { PlasmoMessaging } from "@plasmohq/messaging"

interface PingRequest {
  // No specific body needed for ping
}

interface PingResponse {
  pong: boolean
  url: string
  title: string
}

const handler: PlasmoMessaging.MessageHandler<PingRequest, PingResponse> = async (req, res) => {
  console.log("[Content] Ping received")

  res.send({
    pong: true,
    url: window.location.href,
    title: document.title
  })
}

export default handler
