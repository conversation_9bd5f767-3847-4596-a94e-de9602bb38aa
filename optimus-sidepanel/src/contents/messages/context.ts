/**
 * Content Script Context Message Handler
 *
 * Handles context extraction requests from background script.
 * Uses Plasmo messaging patterns for clean communication.
 */

import type { PlasmoMessaging } from "@plasmohq/messaging"

import type { PageContext } from "~types"
import { extractPageContent } from "~utils/content-extraction"

interface ContextRequest {
  tabId?: number
}

interface ContextResponse {
  success: boolean
  context: PageContext | null
  error?: string
}

const handler: PlasmoMessaging.MessageHandler<
  ContextRequest,
  ContextResponse
> = async (req, res) => {
  console.log("[Content] Context extraction requested")

  try {
    // Wait for page to be fully loaded
    await ensurePageReady()

    const context: PageContext = {
      url: window.location.href,
      title: document.title,
      favicon: getFaviconUrl(),
      pageContent: extractPageContent(),
      selectedText: getSelectedText(),
      timestamp: Date.now()
    }

    console.log(
      `[Content] Context extracted: ${context.pageContent?.length || 0} chars`
    )

    res.send({ success: true, context })
  } catch (error) {
    console.error("[Content] Context extraction failed:", error)
    res.send({
      success: false,
      context: null,
      error: error instanceof Error ? error.message : "Unknown error"
    })
  }
}

async function ensurePageReady(): Promise<void> {
  // Wait for DOM to be complete
  if (document.readyState !== "complete") {
    await new Promise((resolve) => {
      window.addEventListener("load", resolve, { once: true })
      setTimeout(resolve, 3000) // Timeout after 3 seconds
    })
  }

  // Additional wait for dynamic content
  await new Promise((resolve) => setTimeout(resolve, 500))
}

/**
 * Get selected text from page
 */
function getSelectedText(): string | null {
  const selection = window.getSelection()
  const text = selection?.toString().trim()
  return text && text.length > 0 ? text : null
}

function getFaviconUrl(): string | null {
  const favicon = document.querySelector('link[rel*="icon"]') as HTMLLinkElement
  if (favicon?.href) {
    return favicon.href
  }

  // Fallback to default favicon
  return `${window.location.origin}/favicon.ico`
}

export default handler
