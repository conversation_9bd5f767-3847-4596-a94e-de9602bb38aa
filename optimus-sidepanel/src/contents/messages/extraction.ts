/**
 * Content Script Extraction Message Handler
 * 
 * Handles debug extraction requests for testing different extraction methods.
 * Uses Plasmo messaging patterns for clean communication.
 */

import type { PlasmoMessaging } from "@plasmohq/messaging"

import { debugExtraction } from "~utils/content-extraction"

interface ExtractionRequest {
  method: "innerText" | "markdown" | "html" | "simple"
}

interface ExtractionResponse {
  success: boolean
  content: string
  method: string
  error?: string
}

const handler: PlasmoMessaging.MessageHandler<ExtractionRequest, ExtractionResponse> = async (req, res) => {
  const { method } = req.body || {}
  
  console.log(`[Content] Debug extraction: ${method}`)

  try {
    await ensurePageReady()

    let content = ""

    switch (method) {
      case "innerText":
        content = document.body?.innerText || ""
        break

      case "markdown":
        content = debugExtraction("body")
        break

      case "html":
        content = document.body?.innerHTML || ""
        break

      case "simple":
        content = document.body?.textContent || ""
        break

      default:
        throw new Error(`Unknown extraction method: ${method}`)
    }

    console.log(`[Content] Debug ${method}: ${content.length} chars`)
    res.send({ success: true, content, method })
  } catch (error) {
    console.error(`[Content] Debug extraction failed:`, error)
    res.send({ 
      success: false, 
      content: "", 
      method,
      error: error instanceof Error ? error.message : "Unknown error"
    })
  }
}

async function ensurePageReady(): Promise<void> {
  // Wait for DOM to be complete
  if (document.readyState !== "complete") {
    await new Promise((resolve) => {
      window.addEventListener("load", resolve, { once: true })
      setTimeout(resolve, 3000) // Timeout after 3 seconds
    })
  }

  // Additional wait for dynamic content
  await new Promise((resolve) => setTimeout(resolve, 500))
}

export default handler
