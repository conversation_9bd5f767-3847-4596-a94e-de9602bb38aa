// /**
//  * Optimus Content Script
//  *
//  * Modernized content script using Plasmo patterns.
//  * Handles page content extraction and context management.
//  */

// import type { PlasmoCSConfig } from "plasmo"

// export const config: PlasmoCSConfig = {
//   matches: ["<all_urls>"],
//   run_at: "document_idle",
//   all_frames: false
// }

// // Prevent double initialization
// if (!window.__optimusContentInit) {
//   window.__optimusContentInit = true
//   initialize()
// }

// /**
//  * Initialize content script
//  */
// function initialize() {
//   console.log(
//     "🚀 Optimus content script initializing on:",
//     window.location.href
//   )

//   // Setup navigation listeners for SPA
//   setupNavigationListeners()

//   console.log("✅ Optimus content script ready")
// }

// /**
//  * Setup navigation listeners for SPA support
//  */
// function setupNavigationListeners() {
//   // Listen for navigation changes in SPAs
//   const originalPushState = history.pushState
//   const originalReplaceState = history.replaceState

//   history.pushState = function (...args) {
//     originalPushState.apply(this, args)
//     handleNavigation()
//   }

//   history.replaceState = function (...args) {
//     originalReplaceState.apply(this, args)
//     handleNavigation()
//   }

//   window.addEventListener("popstate", handleNavigation)
//   window.addEventListener("hashchange", handleNavigation)
// }

// /**
//  * Handle navigation events
//  */
// function handleNavigation() {
//   console.log("[Content] Navigation detected:", window.location.href)
//   // Could notify background of URL change here if needed
// }

// // Global type augmentation
// declare global {
//   interface Window {
//     __optimusContentInit?: boolean
//   }
// }
