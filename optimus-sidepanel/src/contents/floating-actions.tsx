// /**
//  * Floating Actions CSUI Component
//  *
//  * Plasmo Content Script UI for floating action buttons.
//  * Shows when text is selected, provides quick actions.
//  */

// import { sendToBackground } from "@plasmohq/messaging"
// import type { PlasmoCSConfig, PlasmoGetStyle } from "plasmo"
// import { useEffect, useState } from "react"

// export const config: PlasmoCSConfig = {
//   matches: ["<all_urls>"],
//   all_frames: false
// }

// // Inject styles into shadow DOM
// export const getStyle: PlasmoGetStyle = () => {
//   const style = document.createElement("style")
//   style.textContent = `
//     .floating-actions {
//       position: fixed;
//       z-index: 999999;
//       background: rgba(0, 0, 0, 0.9);
//       border-radius: 8px;
//       padding: 8px;
//       display: flex;
//       gap: 4px;
//       backdrop-filter: blur(10px);
//       border: 1px solid rgba(255, 255, 255, 0.1);
//       animation: fadeIn 0.2s ease-out;
//     }

//     .floating-button {
//       background: rgba(255, 255, 255, 0.1);
//       border: none;
//       border-radius: 6px;
//       color: white;
//       padding: 6px 12px;
//       font-size: 12px;
//       cursor: pointer;
//       transition: all 0.2s ease;
//     }

//     .floating-button:hover {
//       background: rgba(255, 255, 255, 0.2);
//       transform: translateY(-1px);
//     }

//     @keyframes fadeIn {
//       from { opacity: 0; transform: translateY(10px); }
//       to { opacity: 1; transform: translateY(0); }
//     }
//   `
//   return style
// }

// const FloatingActions = () => {
//   const [selectedText, setSelectedText] = useState<string>("")
//   const [position, setPosition] = useState<{ x: number; y: number } | null>(null)
//   const [isVisible, setIsVisible] = useState(false)

//   useEffect(() => {
//     let timeoutId: NodeJS.Timeout

//     const handleSelection = () => {
//       // Clear existing timeout
//       if (timeoutId) clearTimeout(timeoutId)

//       // Small delay to ensure selection is stable
//       timeoutId = setTimeout(() => {
//         const selection = window.getSelection()
//         const text = selection?.toString().trim()

//         if (text && text.length >= 3) {
//           const range = selection?.getRangeAt(0)
//           const rect = range?.getBoundingClientRect()

//           if (rect) {
//             setSelectedText(text)
//             setPosition({
//               x: rect.left + rect.width / 2,
//               y: rect.top - 10
//             })
//             setIsVisible(true)
//           }
//         } else {
//           setIsVisible(false)
//           setSelectedText("")
//           setPosition(null)
//         }
//       }, 100)
//     }

//     const handleClickOutside = () => {
//       setIsVisible(false)
//       setSelectedText("")
//       setPosition(null)
//     }

//     // Listen for text selection
//     document.addEventListener("mouseup", handleSelection)
//     document.addEventListener("keyup", handleSelection)
//     document.addEventListener("click", handleClickOutside)

//     return () => {
//       document.removeEventListener("mouseup", handleSelection)
//       document.removeEventListener("keyup", handleSelection)
//       document.removeEventListener("click", handleClickOutside)
//       if (timeoutId) clearTimeout(timeoutId)
//     }
//   }, [])

//   const handleAction = async (action: string) => {
//     console.log(`[FloatingActions] ${action} clicked with text:`, selectedText.substring(0, 50))

//     try {
//       switch (action) {
//         case "chat":
//           // Open sidepanel and send selected text
//           await sendToBackground({
//             name: "sidepanel",
//             body: { action: "open" }
//           })
//           // TODO: Send selected text to chat
//           break

//         case "explain":
//           // Quick explain action
//           await sendToBackground({
//             name: "sidepanel",
//             body: { action: "open" }
//           })
//           // TODO: Send explain request
//           break

//         case "copy":
//           // Copy to clipboard
//           await navigator.clipboard.writeText(selectedText)
//           break
//       }
//     } catch (error) {
//       console.error(`[FloatingActions] ${action} failed:`, error)
//     }

//     // Hide after action
//     setIsVisible(false)
//   }

//   if (!isVisible || !position || !selectedText) {
//     return null
//   }

//   return (
//     <div
//       className="floating-actions"
//       style={{
//         left: `${position.x}px`,
//         top: `${position.y}px`,
//         transform: "translate(-50%, -100%)"
//       }}>
//       <button
//         className="floating-button"
//         onClick={() => handleAction("chat")}
//         title="Chat about this">
//         💬
//       </button>
//       <button
//         className="floating-button"
//         onClick={() => handleAction("explain")}
//         title="Explain this">
//         🤔
//       </button>
//       <button
//         className="floating-button"
//         onClick={() => handleAction("copy")}
//         title="Copy text">
//         📋
//       </button>
//     </div>
//   )
// }

// export default FloatingActions
