@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap");

@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

/* Ensure no white flash on load */
html,
body {
  background-color: #000 !important;
}

/* Plasmo specific styles */
#plasmo-shadow-container {
  all: initial;
  box-sizing: border-box;
}

/* Root variables for the new design system */
:root {
  --grain-opacity: 0.15;
  --blur-strength: 16px;
  --glass-bg: rgba(0, 0, 0, 0.3);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-highlight: rgba(255, 255, 255, 0.05);
  /* ===== App-wide theme variables (synced with app/globals.css) ===== */
  /* Neutral palette */
  --background: 0 0% 100%;
  --foreground: 240 10% 3.9%;
  --card: 0 0% 100%;
  --card-foreground: 240 10% 3.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 240 10% 3.9%;

  /* Brand / primary palette */
  --primary: 240 5.9% 10%;
  --primary-foreground: 0 0% 98%;

  /* Secondary & muted */
  --secondary: 240 4.8% 95.9%;
  --secondary-foreground: 240 5.9% 10%;
  --muted: 240 4.8% 95.9%;
  --muted-foreground: 240 3.8% 46.1%;

  /* Accent / destructive */
  --accent: 240 4.8% 95.9%;
  --accent-foreground: 240 5.9% 10%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;

  /* Border / ring */
  --border: 240 5.9% 90%;
  --input: 240 5.9% 90%;
  --ring: 240 10% 3.9%;
}

/* Dark theme overrides (kept in-file so the side-panel can toggle without importing globals.css) */
.dark {
  --background: 240 10% 3.9%;
  --foreground: 0 0% 98%;

  --card: 240 10% 3.9%;
  --card-foreground: 0 0% 98%;

  --popover: 240 10% 3.9%;
  --popover-foreground: 0 0% 98%;

  --primary: 0 0% 98%;
  --primary-foreground: 240 5.9% 10%;

  --secondary: 240 3.7% 15.9%;
  --secondary-foreground: 0 0% 98%;

  --muted: 240 3.7% 15.9%;
  --muted-foreground: 240 5% 64.9%;

  --accent: 240 3.7% 15.9%;
  --accent-foreground: 0 0% 98%;

  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;

  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --ring: 240 4.9% 83.9%;
}

/* Base styles */
* {
  box-sizing: border-box;
}

body {
  font-family:
    "Inter",
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  overscroll-behavior: contain;
}

/* Grain texture overlay */
.grain-overlay {
  position: relative;
}

.grain-overlay::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
  opacity: var(--grain-opacity);
  background-image: radial-gradient(
      circle at 20% 80%,
      transparent 30%,
      rgba(255, 255, 255, 0.1) 60%
    ),
    radial-gradient(
      circle at 80% 20%,
      transparent 30%,
      rgba(255, 255, 255, 0.05) 60%
    ),
    radial-gradient(
      circle at 40% 40%,
      transparent 20%,
      rgba(255, 255, 255, 0.03) 50%
    ),
    repeating-conic-gradient(
      from 0deg at 50% 50%,
      transparent 0deg,
      rgba(255, 255, 255, 0.01) 1deg,
      transparent 2deg
    );
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

/* Glass morphism effects */
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(var(--blur-strength));
  -webkit-backdrop-filter: blur(var(--blur-strength));
  border: 1px solid var(--glass-border);
}

.glass-subtle {
  background: rgba(128, 128, 128, 0.2);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.glass-strong {
  background: rgba(64, 64, 64, 0.4);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

/* Minimal transitions */
.transition-minimal {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom scrollbar with glass effect */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Message bubbles */
/* .message-user {
  background: linear-gradient(
    135deg,
    hsl(var(--primary) / 0.8),
    hsl(var(--primary) / 0.9)
  );
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid hsl(var(--primary) / 0.2);
  box-shadow: 0 4px 16px hsl(var(--primary) / 0.1);
}

.message-assistant {
  background: hsl(var(--muted) / 0.6);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid hsl(var(--border) / 0.3);
  box-shadow: 0 4px 16px hsl(var(--muted) / 0.1);
} */

/* Input field glass effect */
.input-glass {
  background: rgba(31, 41, 55, 0.4);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(75, 85, 99, 0.3);
  transition: all 0.2s ease;
}

.input-glass:focus {
  background: rgba(31, 41, 55, 0.6);
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Button glass effects */
.btn-glass {
  background: rgba(31, 41, 55, 0.6);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(75, 85, 99, 0.3);
  transition: all 0.2s ease;
}

.btn-glass:hover {
  background: rgba(31, 41, 55, 0.8);
  border-color: rgba(75, 85, 99, 0.5);
  transform: translateY(-1px);
}

.btn-primary {
  background: linear-gradient(
    135deg,
    hsl(var(--primary) / 0.8),
    hsl(var(--primary) / 0.9)
  );
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid hsl(var(--primary) / 0.3);
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: linear-gradient(
    135deg,
    hsl(var(--primary) / 0.9),
    hsl(var(--primary) / 1)
  );
  border-color: hsl(var(--primary) / 0.5);
  transform: translateY(-1px);
}

/* Loading spinner */
.spinner {
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-top: 2px solid rgba(59, 130, 246, 0.8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Fade animations - minimal and smooth */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-fade-in-up {
  animation: fadeInUp 0.3s ease-out;
}

/* Utilities */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Hide scrollbar but keep functionality */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
