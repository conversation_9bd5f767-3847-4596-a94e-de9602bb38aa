/**
 * Optimus Sidepanel - Plasmo Edition
 *
 * Simplified sidepanel using Plasmo patterns.
 * Clean architecture with professional component organization.
 */

import { <PERSON><PERSON>rovider, SignedIn, SignedOut } from "@clerk/chrome-extension"
import { Plus } from "lucide-react"

import { ContextCard, DebugModal } from "~components"
import ErrorBoundary from "~components/ErrorBoundary"
import { InputForm } from "~components/InputForm"
import { LoadingIndicator } from "~components/LoadingIndicator"
import { MessageList } from "~components/MessageList"
import { useChat } from "~hooks/useChat"
import { useContext } from "~hooks/useContext"
import { useConversations } from "~hooks/useConversations"

import "./style.css"

const SidePanel = () => {
  const {
    context,
    currentTabId,
    isLoading: contextLoading,
    refreshContext
  } = useContext()

  const {
    isLoading,
    messages,
    isReplying,
    sendMessage,
    handleNewChat,
    currentConversation
  } = useConversations({
    context,
    currentTabId
  })

  const {
    input,
    setInput,
    streamingMessageId,
    streamingContent,
    messagesEndRef,
    inputRef,
    handleSendMessage
  } = useChat({
    isReplying,
    messages,
    currentConversation,
    context,
    sendMessage
  })

  if (isLoading) {
    return <LoadingIndicator />
  }

  return (
    <div className="flex flex-col h-screen bg-black text-white font-inter relative">
      {/* Context Mode Controls */}
      {/* <ContextModeControls
        contextMode={contextMode}
        onModeChange={handleContextModeChange}
        onShowTabDialog={() => setShowTabDialog(true)}
      /> */}

      <DebugModal isOpen={true} onClose={() => {}} context={context} />

      {/* Action buttons */}
      <div className="absolute top-4 right-4 flex items-center space-x-2 z-50">
        {/* Debug button for testing context */}
        <button
          onClick={refreshContext}
          className="btn-primary text-xs size-9 flex justify-center items-center rounded-full shadow-lg"
          title="Refresh Context">
          🔄
        </button>
        {/* New Chat button */}
        <button
          onClick={handleNewChat}
          title="Start a fresh conversation"
          className="btn-primary text-xs size-9 flex justify-center items-center rounded-full shadow-lg">
          <Plus size={20} />
        </button>
      </div>

      {/* Messages */}
      <div className="flex-1 relative overflow-hidden">
        <ErrorBoundary>
          <MessageList
            messages={messages}
            isReplying={isReplying}
            streamingMessageId={streamingMessageId}
            streamingContent={streamingContent}
          />
        </ErrorBoundary>
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="flex-shrink-0 glass-strong border-t border-white/10 p-2 rounded-2xl m-1.5 space-y-2">
        {context && (
          <div className="flex items-center space-x-2 overflow-x-auto scrollbar-hide">
            <ErrorBoundary>
              {/* Always show page context if available */}
              <ContextCard
                key={`page-${currentTabId}`}
                context={context}
                type="page"
                onRemove={() => {}}
              />
              {/* Show selected text context if available */}
              {context.selectedText && (
                <ContextCard
                  key={`selected-${currentTabId}`}
                  context={context}
                  type="selectedText"
                  onRemove={() => {}}
                />
              )}
              {/* Show clipboard context if available */}
              {context.clipboard && (
                <ContextCard
                  key={`clipboard-${currentTabId}`}
                  context={context}
                  type="clipboard"
                  onRemove={() => {}}
                />
              )}
            </ErrorBoundary>
          </div>
        )}

        {/* Input Form */}
        <ErrorBoundary>
          <InputForm
            input={input}
            isReplying={isReplying}
            onInputChange={setInput}
            onSubmit={handleSendMessage}
            ref={inputRef}
          />
        </ErrorBoundary>
      </div>
    </div>
  )
}

// -------------------------------------------------------------
// Clerk integration wrapper: provides auth context & gating
// -------------------------------------------------------------

const SidePanelRoot = () => {
  const publishableKey = process.env.PLASMO_PUBLIC_CLERK_PUBLISHABLE_KEY as
    | string
    | undefined

  // If the env variable is missing we can't initialise Clerk –
  // show a clear error instead of a blank screen.
  if (!publishableKey) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-950 text-white p-4">
        <div className="text-center space-y-3 max-w-xs">
          <p className="text-lg font-semibold">Missing Clerk key</p>
          <p className="text-sm opacity-80">
            Set <code>PLASMO_PUBLIC_CLERK_PUBLISHABLE_KEY</code> in your
            <code>.env</code> then rebuild the extension.
          </p>
        </div>
      </div>
    )
  }

  const syncHost =
    (process.env.PLASMO_PUBLIC_CLERK_SYNC_HOST as string | undefined) ||
    "http://localhost:3000"

  // Absolute URL to this extension (chrome-extension://<id>/)
  const EXTENSION_URL = chrome.runtime.getURL(".")

  return (
    <ClerkProvider
      publishableKey={publishableKey}
      syncHost={syncHost}
      afterSignOutUrl={`${EXTENSION_URL}/sidepanel.html`}
      signInFallbackRedirectUrl={`${EXTENSION_URL}/sidepanel.html`}
      signUpFallbackRedirectUrl={`${EXTENSION_URL}/sidepanel.html`}>
      <SignedIn>
        <SidePanel />
      </SignedIn>
      <SignedOut>
        <div className="flex flex-col items-center justify-center h-screen bg-black text-white p-4 space-y-4">
          <button
            onClick={() => {
              // Open Optimus sign-in page in a new tab
              chrome.tabs.create({ url: `${syncHost}/sign-in` })
              try {
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore – close() chưa có trong type definitions
                if (chrome.sidePanel?.close) {
                  // @ts-ignore
                  chrome.sidePanel.close().catch(() => {})
                }
              } catch (_) {
                /* noop */
              }
            }}
            className="btn-primary px-6 py-2 rounded-lg shadow-lg text-sm font-medium">
            Login tại trang chủ Optimus
          </button>
          <p className="text-xs text-gray-400 mt-3 text-center max-w-xs">
            Sau khi hoàn tất đăng nhập ở tab mới, hãy đóng rồi mở lại Sidepanel
            để đồng bộ phiên.
          </p>
        </div>
      </SignedOut>
    </ClerkProvider>
  )
}

export default SidePanelRoot
