/**
 * Optimus Background Service Worker
 *
 * Handles extension lifecycle, context caching, and sidepanel management.
 * Uses Plasmo framework for clean, maintainable architecture.
 */

import { Storage } from "@plasmohq/storage"

// Initialize storage
export const storage = new Storage()

// Global constants
export const API_BASE_URL =
  process.env.PLASMO_PUBLIC_API_URL || "http://localhost:3000"

// Context cache (in-memory for performance)
const contextCache = new Map<number, any>()
const CACHE_TTL_MS = 0 // Development: always fresh

/**
 * Initialize background service
 */
async function initialize() {
  setupSidepanelHandlers()
}

/**
 * Handle sidepanel actions
 */
function setupSidepanelHandlers() {
  chrome.action.onClicked.addListener(async (tab) => {
    if (tab.id) {
      await chrome.sidePanel.open({ tabId: tab.id })
    }
  })

  // Handle keyboard shortcut
  chrome.commands.onCommand.addListener(async (command) => {
    if (command === "_execute_action") {
      const [tab] = await chrome.tabs.query({
        active: true,
        currentWindow: true
      })
      if (tab.id) {
        await chrome.sidePanel.open({ tabId: tab.id })
      }
    }
  })
}

/**
 * Context cache utilities
 */
export function getCachedContext(tabId: number) {
  const cached = contextCache.get(tabId)
  if (!cached) return null

  // With TTL=0, always return stale for fresh extraction
  if (CACHE_TTL_MS === 0) return null

  const isExpired = Date.now() - cached.timestamp > CACHE_TTL_MS
  return isExpired ? null : cached.context
}

export function setCachedContext(tabId: number, context: any) {
  contextCache.set(tabId, {
    context,
    timestamp: Date.now()
  })
}

// Initialize when background script loads
initialize().catch(console.error)
