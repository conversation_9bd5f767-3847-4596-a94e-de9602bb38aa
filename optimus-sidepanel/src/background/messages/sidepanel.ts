/**
 * Sidepanel Message Handler
 * 
 * Handles sidepanel open/close/toggle operations.
 * Provides clean API for sidepanel management.
 */

import type { PlasmoMessaging } from "@plasmohq/messaging"

interface SidepanelRequest {
  action: "open" | "close" | "toggle"
  tabId?: number
}

interface SidepanelResponse {
  success: boolean
  action: string
  tabId?: number
}

const handler: PlasmoMessaging.MessageHandler<SidepanelRequest, SidepanelResponse> = async (req, res) => {
  const { action, tabId: requestedTabId } = req.body || {}
  
  // Get current tab if not specified
  const tabId = requestedTabId || req.sender?.tab?.id
  if (!tabId) {
    console.warn("[Sidepanel] No tab ID available")
    res.send({ success: false, action })
    return
  }

  console.log(`[Sidepanel] ${action} requested for tab ${tabId}`)

  try {
    switch (action) {
      case "open":
        await chrome.sidePanel.open({ tabId })
        break
        
      case "close":
        // Chrome doesn't have a direct close API, but we can navigate away
        // For now, just log the action
        console.log("[Sidepanel] Close action logged (no direct API)")
        break
        
      case "toggle":
        // Chrome doesn't expose sidepanel state, so we'll always open
        await chrome.sidePanel.open({ tabId })
        break
        
      default:
        throw new Error(`Unknown action: ${action}`)
    }

    res.send({ success: true, action, tabId })
    
  } catch (error) {
    console.error(`[Sidepanel] Error handling ${action}:`, error)
    res.send({ success: false, action, tabId })
  }
}

export default handler