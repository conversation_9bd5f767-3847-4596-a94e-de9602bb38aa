/**
 * Extraction Message Handler
 *
 * Handles direct content extraction requests for debugging and testing.
 * Provides various extraction methods for different use cases.
 */

import type { PlasmoMessaging } from "@plasmohq/messaging"
import { sendToContentScript } from "@plasmohq/messaging"

interface ExtractionRequest {
  method: "innerText" | "markdown" | "html" | "simple"
  tabId?: number
}

interface ExtractionResponse {
  success: boolean
  content: string
  method: string
  length: number
}

const handler: PlasmoMessaging.MessageHandler<
  ExtractionRequest,
  ExtractionResponse
> = async (req, res) => {
  const { method, tabId: requestedTabId } = req.body || {}

  // Get current tab if not specified
  const tabId = requestedTabId || req.sender?.tab?.id
  if (!tabId) {
    console.warn("[Extraction] No tab ID available")
    res.send({ success: false, content: "", method, length: 0 })
    return
  }

  console.log(`[Extraction] ${method} extraction requested for tab ${tabId}`)

  try {
    // Request extraction from content script
    const response = await sendToContentScript({
      tabId,
      name: "extraction",
      body: {
        method
      }
    })

    if (response?.success) {
      const content = response.content || ""
      console.log(`[Extraction] ${method} successful: ${content.length} chars`)

      res.send({
        success: true,
        content,
        method,
        length: content.length
      })
    } else {
      throw new Error(response?.error || "Extraction failed")
    }
  } catch (error) {
    console.error(`[Extraction] ${method} failed:`, error)
    res.send({
      success: false,
      content: "",
      method,
      length: 0
    })
  }
}

export default handler
