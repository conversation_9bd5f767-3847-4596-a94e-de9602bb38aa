import { <PERSON>U<PERSON>, X } from "lucide-react"
import React, { forwardRef, useImperativeHandle, useRef } from "react"

interface InputFormProps {
  input: string
  isReplying: boolean
  onInputChange: (value: string) => void
  onSubmit: (message: string) => void
}

export const InputForm = forwardRef<HTMLInputElement, InputFormProps>(
  ({ input, isReplying, onInputChange, onSubmit }, ref) => {
    const localRef = useRef<HTMLInputElement>(null)

    useImperativeHandle(ref, () => localRef.current as HTMLInputElement)

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault()
      if (input.trim()) {
        onSubmit(input.trim())
      }
    }

    return (
      <div className="p-2 animate-fade-in">
        <form onSubmit={handleSubmit} className="flex space-x-2">
          <input
            ref={localRef}
            autoFocus
            type="text"
            value={input}
            onChange={(e) => onInputChange(e.target.value)}
            placeholder="Ask me anything about this page..."
            disabled={isReplying}
            className="flex-1 bg-transparent p-0 text-base text-white outline-0! ring-0! border-0! focus:outline-0 disabled:opacity-50 transition-minimal placeholder:text-gray-400"
          />
          <button
            type="submit"
            disabled={!input.trim() || isReplying}
            className="btn-primary p-1 size-8 rounded-full outline-none disabled:opacity-50 disabled:cursor-not-allowed transition-minimal flex items-center justify-center">
            {isReplying ? <X size={20} /> : <ArrowUp size={20} />}
          </button>
        </form>
      </div>
    )
  }
)

InputForm.displayName = "InputForm"
