import ReactMarkdown from "react-markdown"
import rehypeHighlight from "rehype-highlight"
import remarkGfm from "remark-gfm"

import type { ChatMessage } from "../types"
import StreamingText from "./StreamingText"

interface MessageItemProps {
  message: ChatMessage
  index: number
  streamingMessageId: string | null
  streamingContent: string
}

export const MessageItem = ({
  message,
  index,
  streamingMessageId,
  streamingContent
}: MessageItemProps) => {
  // Additional safety check - don't render if message is malformed
  if (!message || typeof message !== "object") {
    console.warn("Invalid message object received:", message)
    return null
  }

  // Ensure content is always a valid string to prevent ReactMarkdown crashes
  let safeContent = ""
  try {
    // Handle different content types safely
    if (message?.content) {
      if (typeof message.content === "string") {
        safeContent = message.content.trim()
      } else if (typeof message.content === "object") {
        // Handle potential object content (shouldn't happen but being defensive)
        safeContent = JSON.stringify(message.content)
      } else {
        safeContent = String(message.content).trim()
      }
    }

    // Additional safety: remove any null bytes or special characters that might break markdown
    safeContent = safeContent
      .replace(/\0/g, "")
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, "")
  } catch (error) {
    console.error("Error processing message content:", error)
    safeContent = "Error displaying message"
  }

  return (
    <div
      className={`flex animate-fade-in ${
        message.role === "user" ? "justify-end" : "justify-start"
      }`}>
      <div
        className={`max-w-[85%] rounded-3xl px-4 py-3 transition-minimal ${
          message.role === "user"
            ? "message-user text-white shadow-lg"
            : "message-assistant text-gray-100 shadow-lg bg-zinc-700/80"
        }`}>
        {message.role === "assistant" ? (
          <div className="prose prose-invert prose-sm max-w-none">
            {streamingMessageId === message.id ? (
              <StreamingText
                text={streamingContent || ""}
                speed={20}
                className="whitespace-pre-wrap text-sm leading-relaxed"
              />
            ) : safeContent ? (
              <div className="prose prose-invert prose-sm max-w-none">
                <ReactMarkdown
                  remarkPlugins={[remarkGfm]}
                  rehypePlugins={[rehypeHighlight]}
                  className="whitespace-pre-wrap text-sm py-1"
                  skipHtml={true}
                  transformImageUri={() => ""}
                  transformLinkUri={(uri) => uri}
                  components={{
                    // Override problematic components to prevent crashes
                    img: () => null,
                    script: () => null,
                    style: () => null,
                    iframe: () => null
                  }}>
                  {safeContent}
                </ReactMarkdown>
              </div>
            ) : (
              <div className="text-gray-400 italic text-sm">No content</div>
            )}
          </div>
        ) : (
          <div className="whitespace-pre-wrap text-sm">{safeContent}</div>
        )}
        {/* TODO: Add timestamp back when needed
        <div
          className={`text-xs mt-2 transition-minimal ${
            message.role === "user" ? "text-blue-100/70" : "text-gray-400"
          }`}>
          {formatTimestamp(message.timestamp)}
        </div> */}
      </div>
    </div>
  )
}
