/* eslint-disable @next/next/no-img-element */
import { Clipboard, MousePointerClick, X } from "lucide-react"

import type { PageContext } from "../types"

interface ContextCardProps {
  context: PageContext | null
  type: "selectedText" | "clipboard" | "page"
  onRemove?: () => void
}

interface ContextConfig {
  title?: string
  colorClass: string
  borderClass: string
  getContent: (context: PageContext) => string | null
  getIcon: (context: PageContext) => string | React.ComponentType<any> | null
}

const contextConfigs: Record<string, ContextConfig> = {
  selectedText: {
    title: "Selecting",
    colorClass: "text-amber-300",
    borderClass: "border-amber-400/20",
    getContent: (context) => context.selectedText,
    getIcon: () => MousePointerClick
  },
  clipboard: {
    title: "Clipboard",
    colorClass: "text-emerald-300",
    borderClass: "border-emerald-400/20",
    getContent: (context) => context.clipboard,
    getIcon: () => Clipboard
  },
  page: {
    colorClass: "text-blue-300",
    borderClass: "border-blue-400/20",
    getContent: (context) => context.title,
    getIcon: (context) => context.favicon
  }
}

export const ContextCard = ({ context, type, onRemove }: ContextCardProps) => {
  if (!context) {
    return null
  }

  const config = contextConfigs[type]
  if (!config) {
    return null
  }

  const content = config.getContent(context)
  const icon = config.getIcon(context)

  if (!content) {
    return null
  }

  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation()
    onRemove?.()
  }

  return (
    <div
      className={`glass-subtle rounded-xl p-1.5 border ${config.borderClass} animate-fade-in min-w-fit overflow-hidden relative group cursor-default`}>
      <button
        onClick={handleRemove}
        className="absolute top-1 right-1 size-5 rounded-full items-center justify-center transition-all duration-200 z-10 opacity-70 hover:opacity-100 hidden group-hover:flex"
        title="Remove context">
        <X className="size-4" />
      </button>

      <div className="flex items-center space-x-2 pr-1">
        {/* Icon */}
        <div className="bg-zinc-900 rounded-lg flex items-center justify-center px-1 size-9">
          {icon &&
            (typeof icon === "string" ? (
              <img
                src={icon}
                alt=""
                className="size-5 rounded flex-shrink-0"
                onError={(e) => {
                  e.currentTarget.style.display = "none"
                }}
              />
            ) : (
              (() => {
                const IconComponent = icon
                return (
                  <IconComponent
                    className={`size-5 flex-shrink-0 ${config.colorClass}`}
                  />
                )
              })()
            ))}
        </div>

        <div className="flex-1 max-w-56">
          {/* Title */}
          <div
            className={`text-sm ${config.colorClass} font-medium opacity-80 line-clamp-1 truncate`}>
            {type === "page" ? content : config.title}
          </div>

          {/* Content */}
          <div className="text-sm text-gray-100 font-medium leading-relaxed line-clamp-1 truncate max-w-full opacity-80">
            {type === "page" ? new URL(context.url).hostname : content}
          </div>
        </div>
      </div>
    </div>
  )
}
