import type { ContextMode } from "../types"

interface ContextModeControlsProps {
  contextMode: ContextMode
  onModeChange: (mode: ContextMode) => void
  onShowTabDialog: () => void
}

export const ContextModeControls = ({
  contextMode,
  onModeChange,
  onShowTabDialog
}: ContextModeControlsProps) => {
  return (
    <div className="animate-fade-in flex items-center justify-center absolute top-4 z-50 left-1/2 -translate-x-1/2 w-full">
      <div className="flex w-fit rounded-full bg-zinc-700 overflow-hidden border border-white/10 text-sm font-medium p-0.5">
        <button
          onClick={() => onModeChange("this-tab")}
          className={`px-4 py-1.5 transition-minimal rounded-full ${
            contextMode === "this-tab"
              ? "bg-zinc-100/80 text-zinc-900 shadow-lg backdrop-blur-sm"
              : "text-gray-300 hover:bg-white/5"
          }`}>
          This Tab
        </button>
        <button
          onClick={() => onModeChange("all-tabs")}
          className={`px-4 py-1.5 transition-minimal rounded-full ${
            contextMode === "all-tabs"
              ? "bg-zinc-100/80 text-zinc-900 shadow-lg backdrop-blur-sm"
              : "text-gray-300 hover:bg-white/5"
          }`}>
          All Tabs
        </button>
        {/* <button
          onClick={onShowTabDialog}
          className={`px-4 py-1.5 transition-minimal rounded-full ${
            contextMode === "multi-tabs"
              ? "bg-zinc-100/80 text-zinc-900 shadow-lg backdrop-blur-sm"
              : "text-gray-300 hover:bg-white/5"
          }`}
          title="Select specific tabs">
          <Menu />
        </button> */}
      </div>
    </div>
  )
}
