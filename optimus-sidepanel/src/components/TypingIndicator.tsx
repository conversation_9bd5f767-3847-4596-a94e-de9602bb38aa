import { Spark<PERSON> } from "lucide-react"

interface TypingIndicatorProps {
  isVisible: boolean
}

export const TypingIndicator = ({ isVisible }: TypingIndicatorProps) => {
  if (!isVisible) return null

  return (
    <div className="flex justify-start animate-fade-in gap-2 py-2 px-2">
      <div className="flex items-center">
        <Sparkle
          className="size-4 opacity-50"
          style={{
            animation: "sparkleGlow 3.5s cubic-bezier(0.4, 0, 0.6, 1) infinite"
          }}
        />
        <style>{`
          @keyframes sparkleGlow {
            0%,
            100% {
              opacity: 0.2;
              transform: scale(0.9) rotate(0deg);
              filter: brightness(0.8);
            }
            15% {
              opacity: 0.4;
              transform: scale(1.05) rotate(45deg);
              filter: brightness(1.1);
            }
            30% {
              opacity: 0.8;
              transform: scale(1.15) rotate(90deg);
              filter: brightness(1.3);
            }
            50% {
              opacity: 1;
              transform: scale(1.25) rotate(180deg);
              filter: brightness(1.5)
                drop-shadow(0 0 8px rgba(255, 255, 255, 0.3));
            }
            70% {
              opacity: 0.8;
              transform: scale(1.15) rotate(270deg);
              filter: brightness(1.3);
            }
            85% {
              opacity: 0.4;
              transform: scale(1.05) rotate(315deg);
              filter: brightness(1.1);
            }
          }
        `}</style>
      </div>
      <span className="text-sm text-gray-400 opacity-70">
        AI is thinking...
      </span>
    </div>
  )
}
