/* eslint-disable @next/next/no-img-element */
import { useEffect, useState } from "react"

import { sendToContentScript } from "@plasmohq/messaging"

interface TabInfo {
  id: number
  title: string
  url: string
  favicon?: string
  selected: boolean
}

interface TabSelectionDialogProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: (selectedTabs: number[]) => void
}

export const TabSelectionDialog = ({
  isOpen,
  onClose,
  onConfirm
}: TabSelectionDialogProps) => {
  const [tabs, setTabs] = useState<TabInfo[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (isOpen) {
      // Get all tabs when dialog opens, but only from current window/profile
      chrome.tabs.query({ currentWindow: false }, async (allTabs) => {
        const validTabs = []

        for (const tab of allTabs) {
          // Skip invalid tabs
          if (
            !tab.id ||
            !tab.url ||
            tab.url.startsWith("chrome://") ||
            tab.url.startsWith("chrome-extension://")
          ) {
            continue
          }

          try {
            // Try to ping the tab to see if our content script is active
            const response = await sendToContentScript({
              tabId: tab.id,
              name: "ping",
              body: {}
            })
            if (response?.pong) {
              // Tab has our extension active
              validTabs.push({
                id: tab.id,
                title: tab.title || "Untitled",
                url: tab.url,
                favicon: tab.favIconUrl,
                selected: false
              })
            }
          } catch (error) {
            // Tab doesn't have our content script or is not accessible
            // Skip this tab silently
            continue
          }
        }

        setTabs(validTabs)
        setLoading(false)
      })
    }
  }, [isOpen])

  const toggleTab = (tabId: number) => {
    setTabs((prev) =>
      prev.map((tab) =>
        tab.id === tabId ? { ...tab, selected: !tab.selected } : tab
      )
    )
  }

  const selectAll = () => {
    setTabs((prev) => prev.map((tab) => ({ ...tab, selected: true })))
  }

  const selectNone = () => {
    setTabs((prev) => prev.map((tab) => ({ ...tab, selected: false })))
  }

  const handleConfirm = () => {
    const selectedIds = tabs.filter((tab) => tab.selected).map((tab) => tab.id)
    onConfirm(selectedIds)
    onClose()
  }

  if (!isOpen) return null

  return (
    <div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 animate-fade-in"
      onClick={onClose}>
      <div
        className="glass-strong rounded-2xl shadow-2xl max-w-md w-full mx-4 max-h-96 border border-white/20 animate-fade-in-up"
        onClick={(e) => e.stopPropagation()}>
        {/* Header */}
        <div className="p-6 border-b border-white/10">
          <h3 className="text-xl font-semibold text-gray-100 mb-1">
            Select Tabs for Context
          </h3>
          <p className="text-sm text-gray-400">
            Choose which tabs to include in conversation context
          </p>
        </div>

        {/* Controls */}
        <div className="p-4 border-b border-white/10">
          <div className="flex space-x-2">
            <button
              onClick={selectAll}
              className="btn-primary px-3 py-1.5 text-xs rounded-lg">
              Select All
            </button>
            <button
              onClick={selectNone}
              className="btn-glass px-3 py-1.5 text-xs rounded-lg text-gray-300">
              Select None
            </button>
          </div>
        </div>

        {/* Tab List */}
        <div className="max-h-64 overflow-y-auto scrollbar-hide">
          {loading ? (
            <div className="p-6 text-center">
              <div className="w-6 h-6 spinner mx-auto mb-2" />
              <p className="text-sm text-gray-400">Loading tabs...</p>
            </div>
          ) : tabs.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              No valid tabs found
            </div>
          ) : (
            <div className="divide-y divide-white/5">
              {tabs.map((tab) => (
                <div
                  key={tab.id}
                  onClick={() => toggleTab(tab.id)}
                  className={`p-4 cursor-pointer transition-minimal hover:bg-white/5 ${
                    tab.selected
                      ? "bg-blue-500/20 border-r-2 border-blue-400"
                      : ""
                  }`}>
                  <div className="flex items-center space-x-3">
                    {/* Checkbox */}
                    <div
                      className={`w-4 h-4 rounded border-2 flex items-center justify-center transition-minimal ${
                        tab.selected
                          ? "bg-blue-500 border-blue-500"
                          : "border-gray-500"
                      }`}>
                      {tab.selected && (
                        <svg
                          className="w-3 h-3 text-white"
                          fill="currentColor"
                          viewBox="0 0 20 20">
                          <path
                            fillRule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      )}
                    </div>

                    {/* Favicon */}
                    {tab.favicon && (
                      <img
                        src={tab.favicon}
                        alt=""
                        className="w-4 h-4 rounded flex-shrink-0"
                        onError={(e) => {
                          e.currentTarget.style.display = "none"
                        }}
                      />
                    )}

                    {/* Tab Info */}
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium text-gray-200 truncate">
                        {tab.title}
                      </div>
                      <div className="text-xs text-gray-400 truncate">
                        {new URL(tab.url).hostname}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 p-4 border-t border-white/10">
          <button
            onClick={onClose}
            className="btn-glass px-4 py-2 text-sm rounded-lg text-gray-300">
            Cancel
          </button>
          <button
            onClick={handleConfirm}
            disabled={tabs.filter((tab) => tab.selected).length === 0}
            className="btn-primary px-4 py-2 text-sm rounded-lg disabled:opacity-50">
            Confirm ({tabs.filter((tab) => tab.selected).length})
          </button>
        </div>
      </div>
    </div>
  )
}
