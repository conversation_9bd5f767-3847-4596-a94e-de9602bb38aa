import { useEffect, useRef } from "react"

import type { ChatMessage } from "../types"
import { MessageItem } from "./MessageItem"
import { TypingIndicator } from "./TypingIndicator"

interface MessageListProps {
  messages: ChatMessage[]
  isReplying: boolean
  streamingMessageId: string | null
  streamingContent: string
}

export const MessageList = ({
  messages,
  isReplying,
  streamingMessageId,
  streamingContent
}: MessageListProps) => {
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [messages, streamingContent])

  return (
    <div className="h-full overflow-y-auto scrollbar-hide overscroll-contain bg-gray-950">
      <div className="p-4 space-y-4 min-h-full flex-1 flex flex-col pt-20">
        {messages.length === 0 ? (
          <div className="flex items-center justify-center flex-1">
            {/* <EmptyState /> */}
          </div>
        ) : (
          <>
            {messages.map((message, index) => (
              <MessageItem
                key={message.id}
                message={message}
                index={index}
                streamingMessageId={streamingMessageId}
                streamingContent={streamingContent}
              />
            ))}
            <TypingIndicator isVisible={isReplying} />
          </>
        )}
        <div ref={messagesEndRef} />
      </div>
    </div>
  )
}
