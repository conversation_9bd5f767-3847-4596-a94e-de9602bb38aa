/**
 * useConversations Hook - Simplified for MVP
 * 
 * Basic conversation management using Plasmo storage.
 * Matches the current sidepanel UI expectations.
 */

import { useCallback, useEffect, useState } from "react"

import { sendChatMessage, handleStreamingResponse } from "~api"
import { useConversations as useStoredConversations, useCurrentConversation } from "~hooks/useStorage"
import type { ChatMessage, Conversation, PageContext } from "~types"

// Generate UUID using crypto.randomUUID()
function generateUUID(): string {
  return crypto.randomUUID()
}

interface UseConversationsProps {
  context: PageContext | null | undefined
  currentTabId: number | null
}

export const useConversations = ({
  context,
  currentTabId
}: UseConversationsProps) => {
  const [conversations, setConversations] = useStoredConversations()
  const [currentConversationId, setCurrentConversationId] = useCurrentConversation()
  const [isLoading] = useState(false)
  const [isReplying, setIsReplying] = useState(false)

  // Get current conversation
  const currentConversation = conversations.find(conv => conv.id === currentConversationId) || null
  const messages = currentConversation?.messages || []

  // Create or get conversation for current tab
  const createOrGetConversation = useCallback(() => {
    if (!currentTabId || !context) return null

    // Try to find existing conversation for this tab
    let conversation = conversations.find(conv => conv.tabId === currentTabId)

    if (!conversation) {
      // Create new conversation
      conversation = {
        id: generateUUID(),
        tabId: currentTabId,
        title: context.title || "New Chat",
        url: context.url,
        favicon: context.favicon,
        messages: [],
        domain: new URL(context.url).hostname,
        lastActive: Date.now(),
        createdAt: Date.now()
      }

      setConversations(prev => [...prev, conversation])
    }

    // Set as current conversation
    setCurrentConversationId(conversation.id)
    return conversation
  }, [currentTabId, context, conversations, setConversations, setCurrentConversationId])

  // Initialize conversation when tab/context changes
  useEffect(() => {
    if (currentTabId && context) {
      createOrGetConversation()
    }
  }, [currentTabId, context, createOrGetConversation])

  const sendMessage = useCallback(
    async (messageText: string) => {
      const trimmed = messageText.trim()
      if (!trimmed || !currentTabId || !context) {
        console.warn("[useConversations] Cannot send message without text, tab, or context")
        return
      }

      const conversation = createOrGetConversation()
      if (!conversation) return

      // Create user message
      const userMessage: ChatMessage = {
        id: generateUUID(),
        role: "user",
        content: trimmed,
        timestamp: Date.now()
      }

      // Add user message to conversation
      const updatedConversation = {
        ...conversation,
        messages: [...conversation.messages, userMessage],
        lastActive: Date.now()
      }

      setConversations(prev => 
        prev.map(conv => conv.id === conversation.id ? updatedConversation : conv)
      )

      setIsReplying(true)

      try {
        // Send to API
        const response = await sendChatMessage({
          message: trimmed,
          chatId: conversation.id,
          context,
          conversationHistory: conversation.messages.map(msg => ({
            role: msg.role,
            content: msg.content
          }))
        })

        // Handle streaming response
        const content = await handleStreamingResponse(response)
        
        if (content) {
          // Create AI message
          const aiMessage: ChatMessage = {
            id: generateUUID(),
            role: "assistant",
            content: content,
            timestamp: Date.now()
          }

          // Add AI message to conversation
          setConversations(prev => 
            prev.map(conv => {
              if (conv.id === conversation.id) {
                return {
                  ...conv,
                  messages: [...updatedConversation.messages, aiMessage],
                  lastActive: Date.now()
                }
              }
              return conv
            })
          )
        } else {
          throw new Error("No response content received")
        }

      } catch (error) {
        console.error("[useConversations] Send message failed:", error)
        
        // Add error message
        const errorMessage: ChatMessage = {
          id: generateUUID(),
          role: "assistant",
          content: `**Error**: ${error instanceof Error ? error.message : "Failed to get response"}`,
          timestamp: Date.now()
        }

        setConversations(prev => 
          prev.map(conv => {
            if (conv.id === conversation.id) {
              return {
                ...conv,
                messages: [...updatedConversation.messages, errorMessage],
                lastActive: Date.now()
              }
            }
            return conv
          })
        )
      } finally {
        setIsReplying(false)
      }
    },
    [currentTabId, context, createOrGetConversation, setConversations]
  )

  const handleNewChat = useCallback(() => {
    if (!currentTabId || !context) {
      console.warn("[handleNewChat] No active tab or context")
      return
    }

    // Create new conversation
    const newConversation: Conversation = {
      id: generateUUID(),
      tabId: currentTabId,
      title: context.title || "New Chat",
      url: context.url,
      favicon: context.favicon,
      messages: [],
      domain: new URL(context.url).hostname,
      lastActive: Date.now(),
      createdAt: Date.now()
    }

    setConversations(prev => [...prev, newConversation])
    setCurrentConversationId(newConversation.id)
  }, [currentTabId, context, setConversations, setCurrentConversationId])

  return {
    isLoading,
    isReplying,
    messages,
    currentConversation,
    sendMessage,
    handleNewChat
  }
}