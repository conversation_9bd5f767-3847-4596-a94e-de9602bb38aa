/**
 * useContext Hook - Plasmo Edition
 *
 * Clean hook for managing page context using Plasmo messaging.
 * Replaces complex message handling with simple sendToBackground calls.
 */

import { useEffect, useState } from "react"

import { sendToBackground } from "@plasmohq/messaging"

import { MESSAGE_TYPES } from "~constants"
import type { PageContext } from "~types"

interface ContextHookResult {
  context: PageContext | null
  currentTabId: number | null
  isLoading: boolean
  refreshContext: () => Promise<void>
}

export const useContext = (): ContextHookResult => {
  const [context, setContext] = useState<PageContext | null>(null)
  const [currentTabId, setCurrentTabId] = useState<number | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  /**
   * Fetch context from background
   */
  const fetchContext = async (forceRefresh = false) => {
    try {
      setIsLoading(true)

      // Get current tab
      const [tab] = await chrome.tabs.query({
        active: true,
        currentWindow: true
      })
      const tabId = tab?.id

      if (!tabId) {
        console.warn("[useContext] No active tab found")
        setContext(null)
        setCurrentTabId(null)
        return
      }

      // Request context using Plasmo messaging
      // const response = await sendToBackground({
      //   name: "context",
      //   body: {
      //     tabId,
      //     forceRefresh
      //   }
      // })

      // if (response?.context) {
      //   setContext(response.context)
      //   setCurrentTabId(tabId)
      //   console.log(
      //     `[useContext] Context loaded: ${response.cached ? "cached" : "fresh"}`
      //   )
      // } else {
      //   console.warn("[useContext] No context received")
      //   setContext(null)
      //   setCurrentTabId(tabId)
      // }
    } catch (error) {
      console.error("[useContext] Failed to fetch context:", error)
      setContext(null)
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * Refresh context (force fresh extraction)
   */
  const refreshContext = async () => {
    console.log("[useContext] Refreshing context...")
    await fetchContext(true)
  }

  // Initial load
  useEffect(() => {
    fetchContext()
  }, [])

  // Listen for tab changes
  useEffect(() => {
    const handleTabChange = () => {
      console.log("[useContext] Tab change detected, refreshing context")
      fetchContext()
    }

    chrome.tabs.onActivated.addListener(handleTabChange)
    chrome.tabs.onUpdated.addListener((tabId, changeInfo) => {
      if (changeInfo.status === "complete") {
        handleTabChange()
      }
    })

    return () => {
      chrome.tabs.onActivated.removeListener(handleTabChange)
      chrome.tabs.onUpdated.removeListener(handleTabChange)
    }
  }, [])

  return {
    context,
    currentTabId,
    isLoading,
    refreshContext
  }
}
