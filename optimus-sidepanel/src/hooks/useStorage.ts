/**
 * useStorage Hook - Plasmo Edition
 * 
 * Clean wrapper around @plasmohq/storage for consistent usage.
 * Provides typed storage hooks for common extension data.
 */

import { useStorage as usePlasmoStorage } from "@plasmohq/storage/hook"

import type { ChatMessage, Conversation, ContextMode, PageContext } from "~types"

/**
 * Hook for managing conversations
 */
export function useConversations() {
  return usePlasmoStorage<Conversation[]>("conversations", [])
}

/**
 * Hook for managing current conversation ID
 */
export function useCurrentConversation() {
  return usePlasmoStorage<string | null>("currentConversationId", null)
}

/**
 * Hook for managing context mode
 */
export function useContextMode() {
  return usePlasmoStorage<ContextMode>("contextMode", "this-tab")
}

/**
 * Hook for managing screen time data
 */
export function useScreenTime() {
  return usePlasmoStorage<Record<string, number>>("screenTime", {})
}

/**
 * Hook for managing extension settings
 */
export function useSettings() {
  return usePlasmoStorage<{
    apiUrl?: string
    theme?: "dark" | "light"
    debugMode?: boolean
  }>("settings", {
    apiUrl: process.env.PLASMO_PUBLIC_API_URL || "http://localhost:3000",
    theme: "dark",
    debugMode: false
  })
}

/**
 * Hook for caching page contexts (temporary storage)
 */
export function useCachedContexts() {
  return usePlasmoStorage<Record<number, PageContext>>("cachedContexts", {})
}

/**
 * Generic storage hook with better typing
 */
export function useTypedStorage<T>(key: string, defaultValue: T) {
  return usePlasmoStorage<T>(key, defaultValue)
}