import { useEffect, useRef, useState } from "react"

import { MESSAGE_TYPES } from "~constants"

interface UseChatProps {
  isReplying: boolean
  messages: any[]
  currentConversation: any
  context: any
  sendMessage: (message: string) => Promise<void>
}

/**
 * Custom hook for managing chat UI logic
 * Handles input management, auto-scrolling, focus management, and message posting
 */
export function useChat({
  isReplying,
  messages,
  currentConversation,
  context,
  sendMessage
}: UseChatProps) {
  const [input, setInput] = useState("")
  const [streamingMessageId, setStreamingMessageId] = useState<string | null>(
    null
  )
  const [streamingContent, setStreamingContent] = useState("")
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Listen for command to post a message (from floating buttons)
  useEffect(() => {
    const handleMessage = (message: any) => {
      if (message.type === MESSAGE_TYPES.POST_MESSAGE_TO_CHAT) {
        if (message.payload?.message) {
          sendMessage(message.payload.message)
        }
      }
    }

    chrome.runtime.onMessage.addListener(handleMessage)
    return () => chrome.runtime.onMessage.removeListener(handleMessage)
  }, [sendMessage])

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [messages, currentConversation, context])

  // Smart input focus management
  useEffect(() => {
    const focusInput = () => inputRef.current?.focus()

    // Focus on panel open, conversation change, or after reply
    if (!isReplying) {
      requestAnimationFrame(focusInput)
    }

    return () => {
      // Maintain focus when component updates
      if (document.activeElement !== inputRef.current) {
        focusInput()
      }
    }
  }, [isReplying, currentConversation, context])

  const handleSendMessage = async (messageText: string) => {
    await sendMessage(messageText)
    setInput("")
    // Auto-focus after sending
    setTimeout(() => inputRef.current?.focus(), 0)
  }

  const refreshContext = () => {
    console.log("[useChat] Refresh context button clicked")

    // With TTL=0, cache is always stale, so just request fresh context
    // chrome.runtime
    //   .sendMessage({ type: MESSAGE_TYPES.REQUEST_CONTEXT })
    //   .then((response) => {
    //     console.log("[useChat] Fresh context response:", response)
    //   })
    //   .catch((error) => {
    //     console.error("[useChat] Fresh context error:", error)
    //   })
  }

  return {
    input,
    setInput,
    streamingMessageId,
    streamingContent,
    messagesEndRef,
    inputRef,
    handleSendMessage,
    refreshContext
  }
}
