// Legacy MESSAGE_TYPES - kept for backward compatibility during transition
// TODO: Remove these once all legacy message handling is fully migrated
export const MESSAGE_TYPES = {
  // Still used in some places during migration
  CTX_UPDATE: "CTX_UPDATE", // Sending updated context to sidepanel
  CTX_UPDATE_REQUEST: "CTX_UPDATE_REQUEST",
  REQUEST_CONTEXT: "REQUEST_CONTEXT", // Request context from background
  POST_MESSAGE_TO_CHAT: "POST_MESSAGE_TO_CHAT", // Post message to chat
  STM_SAVED: "STM_SAVED", // Short-term memory save confirmation
  OPTIMUS_AUTH_UPDATE: "OPTIMUS_AUTH_UPDATE"
} as const

// The user-facing prompt that shows up in the sidepanel chat
export const AI_PROMPTS = {
  EXPLAIN: (selection: string) =>
    `Gi<PERSON>i thích giúp tôi đoạn này thật ngắn gọn và dễ hiểu: "${selection}"`
} as const

// The different types of AI actions that can be triggered from the floating buttons
export const AI_ACTION_TYPES = {
  EXPLAIN: "explain", // The code uses 'explain'
  ADD_STM: "add_stm"
} as const
