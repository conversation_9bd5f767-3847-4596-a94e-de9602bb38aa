// Use a global cache to preserve fetched CSV data across content script reloads
const csvCache: Map<string, Promise<string | undefined>> = (window as any)
  .__optimusCsvCache ?? new Map()
;(window as any).__optimusCsvCache = csvCache

/**
 * Get the current active sheet ID (gid) from the URL hash, query params,
 * or the active tab element in the DOM.
 */
function getActiveGid(): string {
  const hashGid = location.hash.match(/gid=(\d+)/)?.[1]
  if (hashGid) return hashGid

  const searchGid = new URLSearchParams(location.search).get("gid")
  if (searchGid) return searchGid

  const domGid = document
    .querySelector('[aria-selected="true"][data-sheet-id]')
    ?.getAttribute("data-sheet-id")

  return domGid ?? "0"
}

/**
 * If on a Google Sheets page, fetches the content of the active sheet as a CSV string.
 * Returns undefined if not on a Sheets page or if the fetch fails.
 * Caches results to avoid re-fetching on SPA navigations.
 */
export async function fetchSheetCsv(): Promise<string | undefined> {
  if (!location.hostname.includes("docs.google.com")) return undefined

  const match = location.pathname.match(/\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/)
  if (!match) return undefined

  const sheetId = match[1]
  const gid = getActiveGid()
  const cacheKey = `${sheetId}:${gid}`

  if (csvCache.has(cacheKey)) {
    return csvCache.get(cacheKey)!
  }

  const promise = (async () => {
    try {
      const url = `https://docs.google.com/spreadsheets/d/${sheetId}/gviz/tq?tqx=out:csv&gid=${gid}`
      const resp = await fetch(url, { credentials: "include" })
      if (!resp.ok) return undefined

      const text = await resp.text()
      // Limit size to 100k chars to avoid oversized payloads
      const MAX_SIZE = 100_000
      return text.length > MAX_SIZE ? text.slice(0, MAX_SIZE) : text
    } catch (e) {
      console.warn("fetchSheetCsv error:", e)
      return undefined
    }
  })()

  csvCache.set(cacheKey, promise)
  return promise
}
