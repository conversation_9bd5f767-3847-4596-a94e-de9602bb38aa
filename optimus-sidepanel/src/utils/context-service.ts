/**
 * Smart Context Manager
 * 
 * Manages context caching, retrieval, and smart suggestions.
 * Modernized to use Plasmo patterns and storage.
 */

import { Storage } from "@plasmohq/storage"

import type { SiteContext, SiteContextReference } from "~types"

const storage = new Storage()

export class SmartContextManager {
  private static instance: SmartContextManager
  private contextCache = new Map<string, SiteContext>()

  static getInstance(): SmartContextManager {
    if (!SmartContextManager.instance) {
      SmartContextManager.instance = new SmartContextManager()
    }
    return SmartContextManager.instance
  }

  /**
   * Get available context references for mention suggestions
   */
  async getAvailableContexts(): Promise<SiteContextReference[]> {
    try {
      const stored = await storage.get("cachedContexts") || {}
      const contexts: SiteContextReference[] = []

      for (const [id, context] of Object.entries(stored)) {
        if (context && typeof context === 'object' && 'url' in context) {
          const ctx = context as SiteContext
          contexts.push({
            id,
            url: ctx.url,
            title: ctx.title,
            domain: ctx.domain,
            favicon: ctx.favicon,
            lastAccessed: ctx.lastAccessed || ctx.timestamp,
            isAvailable: true
          })
        }
      }

      // Sort by last accessed (most recent first)
      return contexts.sort((a, b) => b.lastAccessed - a.lastAccessed)
    } catch (error) {
      console.error("[SmartContextManager] Error getting available contexts:", error)
      return []
    }
  }

  /**
   * Search contexts by query
   */
  async searchContexts(query: string): Promise<SiteContextReference[]> {
    const available = await this.getAvailableContexts()
    
    if (!query.trim()) {
      return available.slice(0, 10) // Return recent contexts
    }

    const lowerQuery = query.toLowerCase()
    
    return available.filter(context => 
      context.title.toLowerCase().includes(lowerQuery) ||
      context.domain.toLowerCase().includes(lowerQuery) ||
      context.url.toLowerCase().includes(lowerQuery)
    ).slice(0, 10)
  }

  /**
   * Get full context by ID
   */
  async getContextById(id: string): Promise<SiteContext | null> {
    try {
      const stored = await storage.get("cachedContexts") || {}
      return stored[id] || null
    } catch (error) {
      console.error("[SmartContextManager] Error getting context by ID:", error)
      return null
    }
  }

  /**
   * Cache a context
   */
  async cacheContext(context: SiteContext): Promise<void> {
    try {
      const stored = await storage.get("cachedContexts") || {}
      stored[context.id] = context
      await storage.set("cachedContexts", stored)
      
      // Also update in-memory cache
      this.contextCache.set(context.id, context)
    } catch (error) {
      console.error("[SmartContextManager] Error caching context:", error)
    }
  }

  /**
   * Clear old contexts (keep last 50)
   */
  async cleanupOldContexts(): Promise<void> {
    try {
      const available = await this.getAvailableContexts()
      
      if (available.length <= 50) {
        return // No cleanup needed
      }

      // Keep only the 50 most recent
      const toKeep = available.slice(0, 50)
      const stored = await storage.get("cachedContexts") || {}
      const cleaned: Record<string, SiteContext> = {}

      for (const ref of toKeep) {
        if (stored[ref.id]) {
          cleaned[ref.id] = stored[ref.id]
        }
      }

      await storage.set("cachedContexts", cleaned)
      console.log(`[SmartContextManager] Cleaned up ${available.length - 50} old contexts`)
    } catch (error) {
      console.error("[SmartContextManager] Error cleaning up contexts:", error)
    }
  }
}
