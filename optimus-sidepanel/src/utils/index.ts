/**
 * Utilities - Plasmo Edition
 * 
 * Clean utilities for the Plasmo-based extension.
 * Only essential functions that aren't handled by Plasmo framework.
 */

// =====================================================
// Tab Utilities
// =====================================================

export interface TabInfo {
  id: number
  title: string
  url: string
  favicon?: string
  isActive: boolean
}

/**
 * Check if a URL is injectable (content scripts can run)
 */
export function isInjectableUrl(url: string | undefined): boolean {
  if (!url) return false

  const nonInjectablePatterns = [
    /^chrome:\/\//,
    /^chrome-extension:\/\//,
    /^edge:\/\//,
    /^about:/,
    /^data:/,
    /^blob:/,
    /^file:\/\/.*\.pdf$/i,
    /^https:\/\/chrome\.google\.com\/webstore/,
    /^https:\/\/chromewebstore\.google\.com/
  ]

  return !nonInjectablePatterns.some((pattern) => pattern.test(url))
}

/**
 * Get the current active tab
 */
export async function getCurrentTab(): Promise<chrome.tabs.Tab | null> {
  const tabs = await chrome.tabs.query({ active: true, currentWindow: true })
  return tabs[0] || null
}

// =====================================================
// Content Utilities
// =====================================================

/**
 * Get selected text from page
 */
export function getSelectedText(): string {
  const selection = window.getSelection()
  return selection?.toString().trim() || ""
}

/**
 * Check if selection is valid (has enough text)
 */
export function hasValidSelection(minLength = 3): boolean {
  const selectedText = getSelectedText()
  return selectedText.length >= minLength
}

/**
 * Check if element is editable
 */
export function isEditableElement(element: HTMLElement): boolean {
  const tagName = element?.tagName?.toLowerCase()
  return (
    element?.isContentEditable || 
    tagName === "input" || 
    tagName === "textarea"
  )
}

/**
 * Check if current page is Google Sheets
 */
export function isGoogleSheetsPage(): boolean {
  return (
    location.hostname.includes("docs.google.com") &&
    location.pathname.includes("/spreadsheets/")
  )
}

// =====================================================
// General Utilities
// =====================================================

/**
 * Debounce function
 */
export function debounce<T extends (...args: any[]) => void>(
  fn: T,
  delay = 800
): T & { cancel: () => void } {
  let timeoutId: number

  const debouncedFn = ((...args: Parameters<T>) => {
    clearTimeout(timeoutId)
    timeoutId = window.setTimeout(() => fn(...args), delay)
  }) as T & { cancel: () => void }

  debouncedFn.cancel = () => clearTimeout(timeoutId)
  return debouncedFn
}