/**
 * Unified API Client with automatic authentication
 * All server requests automatically include auth headers
 */

import { createClerkClient } from "@clerk/chrome-extension/background"

const API_BASE_URL =
  process.env.PLASMO_PUBLIC_API_URL || "http://localhost:3000"

const publishableKey = process.env.PLASMO_PUBLIC_CLERK_PUBLISHABLE_KEY

if (!publishableKey) {
  throw new Error(
    "Please add the PLASMO_PUBLIC_CLERK_PUBLISHABLE_KEY to the .env.development file"
  )
}

/**
 * Get fresh authentication token using Clerk client
 */
async function getAuthToken(): Promise<string | null> {
  try {
    const clerk = await createClerkClient({
      publishableKey
    })

    // If there is no valid session, return null
    if (!clerk.session) {
      return null
    }

    // Return the user's fresh session token
    return await clerk.session.getToken()
  } catch (error) {
    console.error("Error getting auth token:", error)
    return null
  }
}

/**
 * Authenticated fetch wrapper - automatically adds auth headers to all requests
 */
async function fetcher(
  url: string,
  options: RequestInit = {}
): Promise<Response> {
  // Get fresh token
  const token = await getAuthToken()

  // Prepare headers
  const headers: HeadersInit = {
    "Content-Type": "application/json",
    ...options.headers
  }

  // Add auth header if token exists
  if (token) {
    headers["Authorization"] = `Bearer ${token}`
  }

  // Make the request with auth headers
  const response = await fetch(url, {
    ...options,
    headers,
    credentials: "include"
  })

  // Handle common auth errors
  if (!response.ok) {
    if (response.status === 401) {
      throw new Error("Authentication required. Please sign in to Optimus.")
    }
    throw new Error(`HTTP ${response.status}: ${response.statusText}`)
  }

  return response
}

/**
 * Send a chat message to the API
 */
export async function sendChatMessage(data: {
  message: string
  chatId: string
  context?: any
  conversationHistory?: Array<{ role: string; content: string }>
}): Promise<Response> {
  return await fetcher(`${API_BASE_URL}/api/sidepanel/chat`, {
    method: "POST",
    body: JSON.stringify(data)
  })
}

/**
 * Store to short-term memory
 */
export async function storeToMemory(data: {
  text: string
  metadata?: any
}): Promise<Response> {
  return await fetcher(`${API_BASE_URL}/api/stm`, {
    method: "POST",
    body: JSON.stringify(data)
  })
}

/**
 * Handle streaming response from the API
 */
export async function handleStreamingResponse(
  response: Response
): Promise<string> {
  const reader = response.body?.getReader()
  const decoder = new TextDecoder()
  let fullResponse = ""

  if (!reader) {
    throw new Error("No response body reader available")
  }

  try {
    while (true) {
      const { done, value } = await reader.read()
      if (done) break

      const chunk = decoder.decode(value)
      const lines = chunk.split("\n")

      for (const line of lines) {
        if (line.startsWith("0:")) {
          const content = line.substring(2).trim()
          if (content && content !== '""') {
            try {
              const parsed = JSON.parse(content)
              if (typeof parsed === "string") {
                fullResponse += parsed
              }
            } catch {
              const text = content.replace(/^"|"$/g, "")
              fullResponse += text
            }
          }
        }
      }
    }
  } finally {
    reader.releaseLock()
  }

  return fullResponse
}
