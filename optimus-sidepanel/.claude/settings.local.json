{"permissions": {"allow": ["Bash(pnpm add:*)", "Bash(rm:*)", "WebFetch(domain:clerk.com)", "Bash(pnpm lint:*)", "<PERSON><PERSON>(npx prettier:*)", "WebFetch(domain:docs.plasmo.com)", "mcp__serena__list_dir", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(pnpm build:*)", "mcp__serena__get_current_config", "mcp__serena__initial_instructions", "mcp__serena__check_onboarding_performed", "mcp__serena__onboarding", "mcp__serena__*", "mcp__serena__write_memory"], "deny": []}}