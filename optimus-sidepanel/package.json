{"name": "optimus-sidepanel", "displayName": "Optimus sidepanel", "version": "0.0.1", "description": "Side Panel for BraveBits Optimus", "author": "<PERSON>", "scripts": {"dev": "plasmo dev", "build": "plasmo build", "package": "plasmo package"}, "dependencies": {"@ai-sdk/openai": "^0.0.66", "@clerk/chrome-extension": "^2.5.1", "@plasmohq/messaging": "^0.7.2", "@plasmohq/storage": "^1.15.0", "@types/turndown": "^5.0.5", "ai": "^3.4.33", "framer-motion": "^11.11.17", "lucide-react": "^0.525.0", "plasmo": "0.90.5", "react": "18.2.0", "react-dom": "18.2.0", "react-markdown": "^8.0.7", "react-router-dom": "^7.6.3", "rehype-highlight": "^6.0.0", "remark-gfm": "3.0.1", "turndown": "^7.2.0", "zustand": "^5.0.6"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "4.1.1", "@types/chrome": "0.0.258", "@types/node": "20.11.5", "@types/react": "18.2.48", "@types/react-dom": "18.2.18", "@types/react-router-dom": "^5.3.3", "autoprefixer": "^10.4.21", "postcss": "8.4.33", "prettier": "3.2.4", "tailwindcss": "3.4.17", "typescript": "5.3.3"}, "manifest": {"key": "$CRX_PUBLIC_KEY", "permissions": ["activeTab", "storage", "unlimitedStorage", "favicon", "offscreen", "search", "scripting", "sidePanel", "clipboardRead", "cookies"], "host_permissions": ["http://localhost/*", "https://*/*", "$CLERK_FRONTEND_API/*", "$PLASMO_PUBLIC_CLERK_SYNC_HOST/*"], "side_panel": {"default_path": "sidepanel.html"}, "content_security_policy": {"extension_pages": "script-src 'self' 'wasm-unsafe-eval'; object-src 'self'; connect-src 'self' http://localhost:* ws://localhost:* https://* data:;"}, "commands": {"_execute_action": {"suggested_key": {"default": "Ctrl+E", "mac": "Command+E"}, "description": "Toggle Optimus sidepanel"}}}}