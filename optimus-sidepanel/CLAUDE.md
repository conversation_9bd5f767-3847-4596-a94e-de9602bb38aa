# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with the Optimus Chrome Extension.

## Development Commands

### Core Development

- `pnpm dev` - Start development server with hot reload
- `pnpm build` - Build extension for production
- `pnpm package` - Package extension for distribution

### Installation & Testing

1. Build: `pnpm build`
2. Load in Chrome: Navigate to `chrome://extensions/`, enable "Developer mode", click "Load unpacked", select `build/chrome-mv3-prod` folder
3. Development: Use `build/chrome-mv3-dev` folder when running `pnpm dev`

## Architecture Overview

### Technology Stack

- **Framework**: Plasmo (Chrome Extension framework)
- **UI**: React 18 + TypeScript + Tailwind CSS
- **State Management**: Zustand for UI state + Chrome Storage API for persistence
- **AI Integration**: Consolidated API client connecting to parent Optimus API
- **Authentication**: Clerk Chrome Extension integration with cookie-based auth

### Project Structure (Simplified)

```
src/
├── api.ts                 # All API calls consolidated here
├── background.ts          # Background script + context caching
├── content.tsx            # Content script (simplified)
├── sidepanel.tsx          # Main sidepanel UI
├── message-handlers.ts    # Central message routing
├── types.ts               # TypeScript type definitions
├── constants.ts           # All constants
├── components/            # All UI components
│   ├── floating-buttons/  # Floating UI elements
│   ├── sidepanel-button/  # Sidepanel toggle button
│   └── [other components]
├── handlers/              # Message handlers (modular)
│   ├── context-handler.ts
│   ├── floating-action-handler.ts
│   ├── message-handler.ts
│   └── tab-listener.ts
├── hooks/                 # Custom React hooks
│   ├── useChat.ts         # Chat UI logic
│   ├── useConversations.ts
│   └── usePageContext.ts
├── lib/                   # Complex integrations only
│   ├── google-sheets.ts   # Google Sheets CSV extraction
│   ├── html-to-markdown.ts # HTML to Markdown conversion
│   └── storage.ts         # Chrome storage adapter
├── store/                 # State management
│   └── conversation-store.ts # Zustand store
├── utils/                 # All utilities
│   ├── index.ts           # General utilities
│   ├── context-service.ts # Content script context utilities
│   ├── dom-injector.tsx   # React component injection
│   ├── event-handlers.ts  # Content script event handling
│   └── screen-time.ts     # Screen time tracking
└── tabs/                  # Tab pages (routes)
```

### Key Features

#### Chrome Extension Architecture

- **Sidepanel**: Main AI chat interface accessible via browser sidepanel
- **Content Script**: Extracts page content, manages floating buttons for text selection
- **Background Script**: Handles context caching and message routing
- **Message Router**: Central `message-handlers.ts` dispatches actions to dedicated handlers

#### AI Integration

- **Simplified API Client** (`api.ts`):
  - `sendChatMessage()` - Main chat endpoint
  - `storeToMemory()` - Store to short-term memory
  - `handleStreamingResponse()` - Process streaming AI responses
- **Authentication**: Uses Clerk authentication with cookie-based session management
- **Context-aware AI**: Supports page content, selected text
- **Removed Complexity**: No more sync service or complex caching layers

#### Data Management

- **Zustand Store**: UI state and current conversation management
- **Chrome Storage**: Settings, screen time data, and offline persistence
- **No Sync Service**: Removed complex sync queue and retry logic (to be implemented later)
- **Simple Architecture**: Direct API calls when online, local storage when offline

### Recent Refactoring Changes

#### 1. **Storage Simplification**
- Removed `sync-service.ts` (401 lines) - Complex sync queue was over-engineered for MVP
- Kept Zustand for UI state + Chrome storage for persistence
- Will implement simple server sync later when needed

#### 2. **API Consolidation**
- Created single `api.ts` file for all API calls
- Removed API utilities scattered across multiple files
- Simple, direct fetch calls with basic error handling

#### 3. **File Organization**
- Moved widgets → components folder
- Consolidated utilities into single `utils/index.ts`
- Moved background-only functions from context-service to background.ts
- Deleted duplicate lib folder and ui-helpers.ts

#### 4. **Component Logic Extraction**
- Created `useChat` hook to handle chat UI logic
- Extracted input management, scrolling, and focus handling
- Reduced sidepanel.tsx complexity significantly

### Development Patterns

#### Message Passing (Simplified)

Essential message types only:
- `PAGE_CONTEXT` - Content → Background
- `POST_MESSAGE_TO_CHAT` - Background → Sidepanel
- `REQUEST_CONTEXT` - Sidepanel → Background
- `CTX_UPDATE` - Background → Sidepanel
- `OPEN_SIDEPANEL`, `TOGGLE_SIDEPANEL` - Navigation
- Removed unused/duplicate message types

#### Storage Architecture (Simplified)

- **Zustand**: Real-time UI state and current conversation
- **Chrome Storage**: Persistent data (settings, screen time)
- **No Sync Queue**: Direct API calls when needed
- **Offline Support**: Works offline with local storage

#### Context Management

- **Smart Caching**: 30-minute TTL in background script
- **Direct Flow**: Content → Background (cache) → Sidepanel
- **No Over-optimization**: Simple, efficient context passing

### API Integration

#### Authentication Flow

1. User signs in via Clerk in parent Optimus application
2. Extension receives auth cookies via `syncHost` configuration
3. API requests include cookies for authentication
4. Server validates using existing `getUserId()` function

#### Error Handling (Simplified)

- **Network Failures**: Simple error messages
- **Auth Errors**: Clear "Please sign in" messages
- **No Complex Retry**: Removed exponential backoff and queue
- **UI Errors**: ErrorBoundary prevents crashes

### Development Guidelines

1. **Keep It Simple**: MVP focus - no premature optimization
2. **Direct API Calls**: Use `api.ts` for all server communication
3. **State Management**: Zustand for UI, Chrome storage for persistence
4. **Error Handling**: Simple try-catch with user-friendly messages
5. **Testing**: Load unpacked extension from `build/chrome-mv3-dev`

### Troubleshooting

- **Authentication**: Ensure user is signed in to parent Optimus application
- **API Errors**: Check CORS headers and authentication
- **Storage Issues**: Clear extension storage if needed
- **Build Errors**: Run `pnpm build` to check for issues

### Future Improvements (Post-MVP)

1. **Sync Service**: Implement simple server sync when stable
2. **Multi-tab Context**: Add support for all-tabs mode
3. **Performance**: Add caching and optimization as needed
4. **Error Recovery**: Add retry logic if users report issues