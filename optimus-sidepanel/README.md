# Optimus Chrome Extension - AI-Powered Sidepanel

A powerful Chrome extension that provides an AI assistant directly in your browser's sidepanel, with intelligent page context understanding and productivity features.

## ✨ Features

### 🤖 **AI Assistant with Context**

- **Smart Context Understanding**: Automatically captures and converts page content to markdown
- **Page-Aware Responses**: AI understands current page content, title, URL, and metadata
- **Selected Text Integration**: Floating buttons appear when you select text for instant explanations
- **Markdown Rendering**: AI responses are beautifully rendered with syntax highlighting

### 💬 **Conversation Management**

- **Per-Tab Conversations**: Each browser tab maintains its own conversation history
- **Merge Mode**: Option to merge all conversations into one continuous chat
- **Persistent Storage**: Conversations saved locally and restored when revisiting pages
- **Smart Tab Switching**: Automatically switches to relevant conversation when changing tabs

### 📊 **Screen Time Analytics**

- **Detailed Tracking**: Monitor time spent on each website with precision
- **Focus vs Blur Time**: Track active engagement vs background time
- **Tab Switch Analytics**: Monitor how often you switch between tabs
- **Visual Reports**: See daily/weekly usage patterns and trends
- **Per-Domain Insights**: Understand your browsing habits by website

### 🎯 **Floating Actions**

- **Text Selection Detection**: Automatically shows action buttons when selecting text (3+ characters)
- **Quick Explain**: Get instant explanations of selected content
- **Knowledge Base**: Save important information for later reference
- **Smart Positioning**: Buttons intelligently position above selected text

### 💾 **Data Persistence**

- **IndexedDB Storage**: Robust local storage for conversations and analytics
- **Cross-Session Continuity**: Resume conversations exactly where you left off
- **Data Privacy**: All data stored locally in your browser
- **Auto-Cleanup**: Automatic cleanup of old data (30+ days)

## 🚀 Installation

### Prerequisites

- Chrome browser
- Node.js 18+ and pnpm
- Optimus API backend running (default: http://localhost:3000)

### Build from Source

1. **Clone and setup:**

   ```bash
   git clone <repository>
   cd optimus-sidepanel
   pnpm install
   ```

2. **Environment setup:**

   ```bash
   # Copy environment template
   cp .env.example .env

   # Configure API URL (default: http://localhost:3000)
   echo "PLASMO_PUBLIC_API_URL=http://localhost:3000" > .env
   ```

3. **Build extension:**

   ```bash
   pnpm build
   ```

4. **Install in Chrome:**
   - Open `chrome://extensions/`
   - Enable "Developer mode"
   - Click "Load unpacked"
   - Select the `build/chrome-mv3-prod` folder

### Development Mode

```bash
# Start development server with hot reload
pnpm dev

# Open chrome://extensions/ and load unpacked from build/chrome-mv3-dev
```

## 🎮 Usage

### Basic Chat

1. **Open Sidepanel**: Click the extension icon in Chrome toolbar
2. **Start Chatting**: Type your question in the input field
3. **Context Awareness**: AI automatically understands the current page content

### Floating Actions

1. **Select Text**: Highlight any text on a webpage (minimum 3 characters)
2. **Choose Action**:
   - **🧠 Explain**: Get quick explanations
   - **➕ Add to KB**: Save to knowledge base
3. **Instant Results**: Response appears directly in sidepanel

### Conversation Management

1. **Per-Tab Mode** (default): Each tab has its own conversation
2. **Merge Mode**: Click the merge button to combine all conversations
3. **Tab Switching**: Conversations automatically switch when you change tabs
4. **History**: All conversations persist between browser sessions

### Screen Time Analytics

1. **View Analytics**: Click the screen time button in sidepanel header
2. **See Insights**: View daily usage, focus time, and session counts
3. **Monitor Habits**: Track patterns to improve productivity

## 🏗️ Architecture

### Components

- **`sidepanel.tsx`**: Main UI with React + Tailwind CSS
- **`content.tsx`**: Page content extraction and floating buttons
- **`background.ts`**: Message routing, screen time tracking, and data persistence
- **`database.ts`**: IndexedDB wrapper for local storage
- **`types.ts`**: TypeScript interfaces and type definitions

### Data Flow

1. **Content Script** → Extracts page content and detects text selection
2. **Background Script** → Processes messages, tracks screen time, manages database
3. **Sidepanel** → Renders UI, handles user interactions, displays AI responses
4. **API Backend** → Processes AI requests with full page context

### Storage Structure

- **Conversations**: Per-tab chat history with metadata
- **Screen Time**: Detailed analytics per website/session
- **Settings**: User preferences and configuration

## 🔧 Configuration

### API Integration

The extension connects to your Optimus API backend. Ensure it's running and accessible:

```typescript
// Default configuration
const API_BASE_URL = process.env.PLASMO_PUBLIC_API_URL || "http://localhost:3000"

// API expects this format:
{
  message: string,
  context: {
    url: string,
    title: string,
    pageContent: string, // Auto-converted markdown
    selectedText?: string,
    meta: Record<string, string>
  },
  conversationHistory: ChatMessage[]
}
```

### Chrome Permissions

The extension requires these permissions (automatically configured):

- `sidePanel`: For the sidepanel interface
- `activeTab`: To access current tab information
- `storage`: For local data persistence
- `tabs`: For tab switching and screen time tracking
- `<all_urls>`: To work on all websites

## 🎨 Customization

### Styling

Built with Tailwind CSS. Key theme elements:

- **Dark Theme**: Primary UI uses gray/black color scheme
- **Accent Colors**: Blue for user messages, amber for selections
- **Syntax Highlighting**: Code blocks with GitHub Dark theme
- **Custom Scrollbars**: Styled for consistency

### Conversation Modes

```typescript
interface ConversationMode {
  type: "per-tab" | "merged"
  mergedSince?: number // When merge mode was enabled
}
```

### Screen Time Configuration

```typescript
interface AppSettings {
  conversationMode: ConversationMode
  screenTimeEnabled: boolean
  autoSaveConversations: boolean
}
```

## 🐛 Troubleshooting

### Common Issues

**AI not responding:**

- Check API backend is running on correct port
- Verify `.env` file has correct `PLASMO_PUBLIC_API_URL`
- Check browser console for error messages

**Floating buttons not appearing:**

- Ensure text selection is at least 3 characters
- Check console for content script errors
- Try refreshing the page

**Conversations not persisting:**

- Check browser storage permissions
- Clear extension data and reload if corrupted
- Verify IndexedDB is not disabled in browser

**Screen time not tracking:**

- Ensure extension has tab permissions
- Check background script is running
- Try reloading the extension

### Debug Mode

Open browser DevTools (F12) to see detailed logs:

- Content script logs: Page interaction and text selection
- Background script logs: Message routing and database operations
- Sidepanel logs: UI interactions and API calls

## 🔄 Updates

### Version History

- **v1.0**: Initial release with basic AI chat
- **v2.0**: Added floating buttons and context awareness
- **v3.0**: Markdown rendering and improved UI
- **v4.0**: Per-tab conversations and screen time tracking
- **v4.1**: Enhanced database storage and analytics

### Automatic Updates

The extension will automatically update when you rebuild and reload in development mode.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

[Your License Here]

---

**Built with**: Plasmo Framework, React, TypeScript, Tailwind CSS, IndexedDB
**AI Backend**: Optimus API with context-aware responses
