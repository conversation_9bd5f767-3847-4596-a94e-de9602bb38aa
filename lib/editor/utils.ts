export const extractNormalText = (text: string) => {
  const plain = text.replace(/\n+/g, " ").trim();
  if (plain.startsWith("[") && plain.endsWith("]")) {
    try {
      const arr = JSON.parse(text);
      if (Array.isArray(arr)) {
        const parts = arr.flatMap((o: any) => {
          const val = Object.values(o)[0];
          return typeof val === "string" ? [val.replace(/\n+/g, " ").trim()] : [];
        });
        const combined = parts.join(" ");
        return combined.length > 150 ? `${combined.substring(0, 150)}...` : combined;
      }
    } catch {
      // invalid JSON, fallback to plain
    }
  }
  return plain.length > 150 ? `${plain.substring(0, 150)}...` : plain;
};
