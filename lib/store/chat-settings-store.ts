import { type Mode, MODES, type Workspace, WORKSPACES, WORKSPACE_IDS } from "@/lib/constants";
import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

interface ChatSettingsState {
  forceDeepResearch: boolean;
  toggleForceDeepResearch: () => void;

  mode: Mode;
  setMode: (mode: Mode) => void;

  workspace: Workspace;
  setWorkspace: (workspace: Workspace) => void;
  selectedOptions: Record<string, string | undefined>;
  setSelectedOption: (key: string, value: string | undefined) => void;
}

export const useChatSettingsStore = create<ChatSettingsState>()(
  persist(
    (set) => ({
      forceDeepResearch: false, // Initial state
      toggleForceDeepResearch: () => set((state) => ({ forceDeepResearch: !state.forceDeepResearch })),

      workspace: WORKSPACES[WORKSPACE_IDS.DEFAULT],
      setWorkspace: (workspace: Workspace) => set((state) => ({ ...state, workspace })),

      mode: MODES.DEFAULT,
      setMode: (mode: Mode) => set((state) => ({ ...state, mode })),

      selectedOptions: {},
      setSelectedOption: (key, value) =>
        set((state) => ({
          selectedOptions: { ...state.selectedOptions, [key]: value },
        })),
    }),
    {
      name: "chat-settings",
      storage: createJSONStorage(() => localStorage),
    }
  )
);
