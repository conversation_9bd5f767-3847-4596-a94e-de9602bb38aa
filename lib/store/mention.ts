import type { JiraTask } from "@/components/jira-tasks-dialog";
import type { ExtendedAttachment } from "@/components/multimodal/types";
import { create } from "zustand";

export type MentionItem = {
  id: string;
  title: string;
  description?: string;
  type: "ticket" | "file";
  url?: string;
  details?: JiraTask | ExtendedAttachment;
};

interface MentionState {
  mentionedItems: MentionItem[];
  addItem: (item: MentionItem) => void;
  removeItem: (itemId: string) => void;
  clearAll: () => void;
}

export const useMentionStore = create<MentionState>((set) => ({
  mentionedItems: [],
  addItem: (item) =>
    set((state) => {
      // Check if ticket already exists
      if (state.mentionedItems.some((t) => t.id === item.id)) {
        return state; // Return unchanged state if ticket already exists
      }
      return {
        mentionedItems: [...state.mentionedItems, item],
      };
    }),

  removeItem: (itemId) =>
    set((state) => ({
      mentionedItems: state.mentionedItems.filter((item) => item.id !== itemId),
    })),

  clearAll: () => set({ mentionedItems: [] }),
}));
