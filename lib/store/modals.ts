import { create } from "zustand";

type ModalsState = {
  historyOpen: boolean;
  fileManagerOpen: boolean;
  jiraTasksOpen: boolean;
  toolsOpen: boolean;
  mentionSelectorOpen: boolean;
  setHistoryOpen: (open: boolean) => void;
  setFileManagerOpen: (open: boolean) => void;
  setJiraTasksOpen: (open: boolean) => void;
  setToolsOpen: (open: boolean) => void;
  setMentionSelectorOpen: (open: boolean) => void;
  toggleHistory: () => void;
  toggleFileManager: () => void;
  toggleJiraTasks: () => void;
  toggleTools: () => void;
  toggleMentionSelector: () => void;
};

export const useModalsStore = create<ModalsState>((set) => ({
  historyOpen: false,
  fileManagerOpen: false,
  jiraTasksOpen: false,
  toolsOpen: false,
  mentionSelectorOpen: false,
  setHistoryOpen: (open) => set({ historyOpen: open }),
  setFileManagerOpen: (open) => set({ fileManagerOpen: open }),
  setJiraTasksOpen: (open) => set({ jiraTasksOpen: open }),
  setToolsOpen: (open) => set({ toolsOpen: open }),
  setMentionSelectorOpen: (open) => set({ mentionSelectorOpen: open }),
  toggleHistory: () => set((state) => ({ historyOpen: !state.historyOpen })),
  toggleFileManager: () => set((state) => ({ fileManagerOpen: !state.fileManagerOpen })),
  toggleJiraTasks: () => set((state) => ({ jiraTasksOpen: !state.jiraTasksOpen })),
  toggleTools: () => set((state) => ({ toolsOpen: !state.toolsOpen })),
  toggleMentionSelector: () => set((state) => ({ mentionSelectorOpen: !state.mentionSelectorOpen })),
}));
