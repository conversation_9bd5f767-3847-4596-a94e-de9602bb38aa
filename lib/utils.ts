import type { AIDocument } from "@/lib/db/schema";
import type { CoreAssistantMessage, CoreToolMessage, UIMessage } from "ai";
import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export interface ApplicationError extends Error {
  info?: any;
  status?: number;
}

/**
 * Enhanced fetcher function for data fetching with better error handling
 * @param url The URL to fetch data from
 * @param options Optional fetch options
 * @returns Parsed JSON response
 */
export const fetcher = async (url: string, options?: RequestInit) => {
  try {
    console.log("[Fetcher] Attempting to fetch URL:", url);
    const res = await fetch(url, options);

    if (!res.ok) {
      const error = new Error(`API error: ${res.status} ${res.statusText}`) as ApplicationError;

      try {
        // Try to parse error information from the response
        error.info = await res.json();
      } catch {
        error.info = { message: "Failed to parse error response" };
      }

      error.status = res.status;
      console.error(`Failed to fetch from ${url}:`, error);
      throw error;
    }

    return await res.json();
  } catch (error) {
    if (!(error instanceof Error)) {
      throw new Error(`Unknown error when fetching ${url}`);
    }

    // If it's already an ApplicationError, just rethrow it
    if ("status" in error) {
      throw error;
    }

    // Otherwise, wrap it in our error format
    const appError = error as ApplicationError;
    appError.info = { message: error.message };
    console.error(`Network or parsing error at ${url}:`, error);
    throw appError;
  }
};

/**
 * POST data to an API endpoint
 * @param url The URL to post to
 * @param data The data to send
 * @returns Parsed JSON response
 */
export const postFetcher = async (url: string, data: any) => {
  return fetcher(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });
};

export function getLocalStorage(key: string) {
  if (typeof window !== "undefined") {
    return JSON.parse(localStorage.getItem(key) || "[]");
  }
  return [];
}

export function generateUUID(): string {
  if (typeof crypto !== "undefined" && "randomUUID" in crypto) {
    return crypto.randomUUID();
  }
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

type ResponseMessageWithoutId = CoreToolMessage | CoreAssistantMessage;
type ResponseMessage = ResponseMessageWithoutId & { id: string };

export function getMostRecentUserMessage(messages: Array<UIMessage>) {
  const userMessages = messages.filter((message) => message.role === "user");
  return userMessages.at(-1);
}

export function getDocumentTimestampByIndex(documents: Array<AIDocument>, index: number) {
  if (!documents) return new Date();
  if (index > documents.length) return new Date();

  return documents[index].createdAt;
}

export function getTrailingMessageId({ messages }: { messages: Array<ResponseMessage> }): string | null {
  const trailingMessage = messages.at(-1);

  if (!trailingMessage) return null;

  return trailingMessage.id;
}

export function sanitizeText(text: string) {
  if (!text || typeof text !== "string") return "";
  // Sanitize text for XSS protection, replace unusual content, etc.
  // This is a very basic implementation
  return text;
}

/**
 * Parse a JSON cookie value and extract a specific key
 * @param cookies - The raw cookie string from request headers
 * @param targetKey - The key to extract from the parsed JSON cookie
 * @returns The value of the target key if found, empty string otherwise
 */
export function parseJsonCookie(cookies: string | null, targetKey: string): string {
  if (!cookies) return "";

  try {
    // Split cookies and find the JSON cookie
    const cookieOptions = cookies
      .split(";")
      .map((c) => c.trim())
      .find((c) => {
        try {
          // Try to parse each cookie value as JSON to find our options object
          const decoded = decodeURIComponent(c);
          const parsed = JSON.parse(decoded);
          // Check if this cookie contains our target key
          return parsed && typeof parsed === "object" && targetKey in parsed;
        } catch {
          return false;
        }
      });

    if (cookieOptions) {
      const options = JSON.parse(decodeURIComponent(cookieOptions));
      return options[targetKey] || "";
    }
  } catch (error) {
    console.error("Error parsing JSON cookie:", error);
  }

  return "";
}

export function formatSize(size: number): string {
  const units = ["B", "KB", "MB", "GB"];
  let formattedSize = size;
  let unitIndex = 0;

  while (formattedSize >= 1024 && unitIndex < units.length - 1) {
    formattedSize /= 1024;
    unitIndex++;
  }

  return `${formattedSize.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`;
}

export function formatDate(date: string) {
  const now = new Date();
  const dateToFormat = new Date(date);
  const secondsAgo = Math.floor((now.getTime() - dateToFormat.getTime()) / 1000);
  const minutesAgo = Math.floor(secondsAgo / 60);
  const hoursAgo = Math.floor(minutesAgo / 60);
  const daysAgo = Math.floor(hoursAgo / 24);

  if (daysAgo > 0) {
    return `${daysAgo}d ago`;
  } else if (hoursAgo > 0) {
    return `${hoursAgo}h ago`;
  } else if (minutesAgo > 0) {
    return `${minutesAgo}m ago`;
  } else {
    return "Just now";
  }
}

// Function to sanitize S3 object keys
export function sanitizeS3Key(key: string): string {
  // Normalize Unicode characters to their base form (e.g., á -> a)
  let sanitizedKey = key.normalize("NFD").replace(/[\u0300-\u036f]/g, "");
  // Replace spaces with underscores
  sanitizedKey = sanitizedKey.replace(/\s+/g, "_");
  // Remove any characters that are not A-Z, a-z, 0-9, !, -, _, ., *, ', (, )
  // Slashes are allowed if they are part of a path structure, but for a flat filename, better to remove or replace.
  // For simplicity here, we'll remove most other special characters.
  sanitizedKey = sanitizedKey.replace(/[^A-Za-z0-9!\-_.*'()]/g, "");
  // Optionally, handle multiple consecutive underscores or dots if needed
  sanitizedKey = sanitizedKey.replace(/_{2,}/g, "_").replace(/\.{2,}/g, ".");
  // Ensure key is not empty after sanitization
  if (!sanitizedKey) {
    return `sanitized_file_${Date.now()}`;
  }
  // Ensure key does not start or end with a dot or underscore, which can be problematic for some systems
  sanitizedKey = sanitizedKey.replace(/^[._]+|[._]+$/g, "");
  if (!sanitizedKey) {
    // Check again after trimming dots/underscores
    return `fallback_file_${Date.now()}`;
  }
  return sanitizedKey;
}
