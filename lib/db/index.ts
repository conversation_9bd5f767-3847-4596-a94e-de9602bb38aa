import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";

const client = postgres(process.env.POSTGRES_URL!, {
  ssl: false, // Completely disable SSL for local connection
  connect_timeout: 30, // Reduced from 60
  max_lifetime: 60 * 10, // Increased from 5min to 10min
  idle_timeout: 20, // Reduced from 30
  max: 15, // Increased from 10 to 15
  prepare: false, // Disable prepared statements for better compatibility
  connection: {
    application_name: "optimus",
  },
  onnotice: (notice) => {
    if (process.env.NODE_ENV === "development") {
      console.log("Database notice:", notice);
    }
  },
  debug: false, // Disable debug in development for performance
});

export const db = drizzle(client);
