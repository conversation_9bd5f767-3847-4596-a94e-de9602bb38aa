import { config } from "dotenv";
import { drizzle } from "drizzle-orm/postgres-js";
import { migrate } from "drizzle-orm/postgres-js/migrator";
import postgres from "postgres";

config({
  path: ".env.local",
});

const setupExtensions = async (connection: postgres.Sql) => {
  console.log("🔧 Setting up required extensions...");

  try {
    // Create pgvector extension for vector embeddings
    await connection`CREATE EXTENSION IF NOT EXISTS vector`;
    console.log("✅ pgvector extension ready");

    // Create uuid-ossp extension for UUID generation functions
    await connection`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`;
    console.log("✅ uuid-ossp extension ready");
  } catch (error) {
    console.warn("⚠️  Extension setup warning:", error);
    console.log("📝 Note: Some extensions might already exist or require superuser privileges");
  }
};

const runMigrate = async () => {
  if (!process.env.POSTGRES_URL) {
    throw new Error("POSTGRES_URL is not defined");
  }

  const connection = postgres(process.env.POSTGRES_URL, {
    max: 1,
    ssl: false, // Completely disable SSL for local connection
    connect_timeout: 60,
    idle_timeout: 30,
    connection: {
      application_name: "optimus-migrate",
    },
  });

  try {
    // Setup required extensions first
    await setupExtensions(connection);

    const db = drizzle(connection);

    console.log("⏳ Running migrations...");

    const start = Date.now();
    await migrate(db, { migrationsFolder: "./lib/db/migrations" });
    const end = Date.now();

    console.log("✅ Migrations completed in", end - start, "ms");
    console.log("🎉 Database is ready!");
  } finally {
    await connection.end();
  }

  process.exit(0);
};

runMigrate().catch((err) => {
  console.error("❌ Migration failed");
  console.error(err);
  process.exit(1);
});
