import type { ArtifactKind } from "@/components/artifact";
import type { VisibilityType } from "@/components/visibility-selector";
import { and, asc, count, desc, eq, gt, gte, inArray, lt, or, sql, type SQL } from "drizzle-orm";
import { db } from "./index";
import {
  ai_document,
  type Chat,
  chat,
  document_metadata,
  type Memory,
  memory,
  mcp_config,
  type McpConfig,
  type Message,
  message,
  stream,
  type Suggestion,
  suggestion,
  user,
  type User,
  vote,
} from "./schema";

export async function getUser(clerkId: string, email: string): Promise<Array<User>> {
  try {
    return await db
      .select()
      .from(user)
      .where(or(eq(user.clerkId, clerkId), eq(user.email, email)));
  } catch (error) {
    console.error("Failed to get user from database");
    throw error;
  }
}

export async function saveChat({ id, userId, title, visibility }: { id: string; userId: string; title: string; visibility: VisibilityType }) {
  try {
    return await db.insert(chat).values({
      id,
      createdAt: new Date(),
      userId,
      title,
      visibility,
    });
  } catch (error) {
    console.error("Failed to save chat in database");
    throw error;
  }
}

export async function deleteChatById({ id }: { id: string }) {
  try {
    await db.delete(vote).where(eq(vote.chatId, id));
    await db.delete(message).where(eq(message.chatId, id));
    await db.delete(stream).where(eq(stream.chatId, id));

    const [chatsDeleted] = await db.delete(chat).where(eq(chat.id, id)).returning();
    return chatsDeleted;
  } catch (error) {
    console.error("Failed to delete chat by id from database");
    throw error;
  }
}

export async function getChatsByUserId({
  id,
  limit,
  startingAfter,
  endingBefore,
}: {
  id: string;
  limit: number;
  startingAfter: string | null;
  endingBefore: string | null;
}) {
  try {
    const extendedLimit = limit + 1;

    const query = (whereCondition?: SQL<any>) =>
      db
        .select()
        .from(chat)
        .where(whereCondition ? and(whereCondition, eq(chat.userId, id)) : eq(chat.userId, id))
        .orderBy(desc(chat.createdAt))
        .limit(extendedLimit);

    let filteredChats: Array<Chat> = [];

    if (startingAfter) {
      const [selectedChat] = await db.select().from(chat).where(eq(chat.id, startingAfter)).limit(1);

      if (!selectedChat) {
        throw new Error(`Chat with id ${startingAfter} not found`);
      }

      filteredChats = await query(gt(chat.createdAt, selectedChat.createdAt));
    } else if (endingBefore) {
      const [selectedChat] = await db.select().from(chat).where(eq(chat.id, endingBefore)).limit(1);

      if (!selectedChat) {
        throw new Error(`Chat with id ${endingBefore} not found`);
      }

      filteredChats = await query(lt(chat.createdAt, selectedChat.createdAt));
    } else {
      filteredChats = await query();
    }

    const hasMore = filteredChats.length > limit;

    return {
      chats: hasMore ? filteredChats.slice(0, limit) : filteredChats,
      hasMore,
    };
  } catch (error) {
    console.error(`Failed to get chats for user ${id} from database:`, error);
    throw error;
  }
}

export async function getChatById({ id }: { id: string }) {
  try {
    const [selectedChat] = await db.select().from(chat).where(eq(chat.id, id));
    return selectedChat;
  } catch (error) {
    // Check for connection timeout errors explicitly
    if (error instanceof Error) {
      if (error.message.includes("CONNECT_TIMEOUT") || (error as any).code === "CONNECT_TIMEOUT") {
        console.error(`Database connection timeout when getting chat id ${id}. Check database load and network connectivity.`, error);
        throw new Error(`Database connection timeout. Please try again later. Error: ${error.message}`);
      }
    }
    console.error(`Failed to get chat by id from database: ${id}`, error);
    throw error;
  }
}

export async function saveMessages({ messages }: { messages: Array<Message> }) {
  try {
    return await db.insert(message).values(messages);
  } catch (error) {
    console.error("Failed to save messages in database", error);
    throw error;
  }
}

export async function getMessagesByChatId({ id }: { id: string }) {
  try {
    return await db.select().from(message).where(eq(message.chatId, id)).orderBy(asc(message.createdAt));
  } catch (error) {
    console.error("Failed to get messages by chat id from database", error);
    throw error;
  }
}

export async function voteMessage({ chatId, messageId, type }: { chatId: string; messageId: string; type: "up" | "down" }) {
  try {
    const [existingVote] = await db.select().from(vote).where(eq(vote.messageId, messageId));

    if (existingVote) {
      return await db
        .update(vote)
        .set({ isUpvoted: type === "up" })
        .where(and(eq(vote.messageId, messageId), eq(vote.chatId, chatId)));
    }
    return await db.insert(vote).values({
      chatId,
      messageId,
      isUpvoted: type === "up",
    });
  } catch (error) {
    console.error("Failed to upvote message in database", error);
    throw error;
  }
}

export async function getVotesByChatId({ id }: { id: string }) {
  try {
    return await db.select().from(vote).where(eq(vote.chatId, id));
  } catch (error) {
    console.error("Failed to get votes by chat id from database", error);
    throw error;
  }
}

export async function saveDocument({
  id,
  title,
  kind,
  content,
  userId,
}: {
  id: string;
  title: string;
  kind: ArtifactKind;
  content: string;
  userId: string;
}) {
  try {
    return await db
      .insert(ai_document)
      .values({
        id,
        title,
        kind,
        content,
        userId,
        createdAt: new Date(),
      })
      .returning();
  } catch (error) {
    console.error("Failed to save document in database");
    throw error;
  }
}

export async function getDocumentsById({ id }: { id: string }) {
  try {
    const documents = await db.select().from(ai_document).where(eq(ai_document.id, id)).orderBy(asc(ai_document.createdAt));

    return documents;
  } catch (error) {
    console.error("Failed to get document by id from database");
    throw error;
  }
}

export async function getDocumentById({ id }: { id: string }) {
  try {
    const [selectedDocument] = await db.select().from(ai_document).where(eq(ai_document.id, id)).orderBy(desc(ai_document.createdAt));

    return selectedDocument;
  } catch (error) {
    console.error("Failed to get document by id from database");
    throw error;
  }
}

export async function deleteDocumentsByIdAfterTimestamp({ id, timestamp }: { id: string; timestamp: Date }) {
  try {
    await db.delete(suggestion).where(and(eq(suggestion.documentId, id), gt(suggestion.documentCreatedAt, timestamp)));

    return await db
      .delete(ai_document)
      .where(and(eq(ai_document.id, id), gt(ai_document.createdAt, timestamp)))
      .returning();
  } catch (error) {
    console.error("Failed to delete documents by id after timestamp from database");
    throw error;
  }
}

export async function saveSuggestions({ suggestions }: { suggestions: Array<Suggestion> }) {
  try {
    return await db.insert(suggestion).values(suggestions);
  } catch (error) {
    console.error("Failed to save suggestions in database");
    throw error;
  }
}

export async function getSuggestionsByDocumentId({ documentId }: { documentId: string }) {
  try {
    return await db
      .select()
      .from(suggestion)
      .where(and(eq(suggestion.documentId, documentId)));
  } catch (error) {
    console.error("Failed to get suggestions by document version from database");
    throw error;
  }
}

export async function getMessageById({ id }: { id: string }) {
  try {
    return await db.select().from(message).where(eq(message.id, id));
  } catch (error) {
    console.error("Failed to get message by id from database");
    throw error;
  }
}

export async function deleteMessagesByChatIdAfterTimestamp({ chatId, timestamp }: { chatId: string; timestamp: Date }) {
  try {
    const messagesToDelete = await db
      .select({ id: message.id })
      .from(message)
      .where(and(eq(message.chatId, chatId), gte(message.createdAt, timestamp)));

    const messageIds = messagesToDelete.map((message) => message.id);

    if (messageIds.length > 0) {
      await db.delete(vote).where(and(eq(vote.chatId, chatId), inArray(vote.messageId, messageIds)));

      return await db.delete(message).where(and(eq(message.chatId, chatId), inArray(message.id, messageIds)));
    }
  } catch (error) {
    console.error("Failed to delete messages by id after timestamp from database");
    throw error;
  }
}

export async function updateChatVisibilityById({ chatId, visibility }: { chatId: string; visibility: "private" | "public" }) {
  try {
    return await db.update(chat).set({ visibility }).where(eq(chat.id, chatId));
  } catch (error) {
    console.error("Failed to update chat visibility in database");
    throw error;
  }
}

export async function getMessageCountByUserId({ id, differenceInHours }: { id: string; differenceInHours: number }) {
  try {
    const twentyFourHoursAgo = new Date(Date.now() - differenceInHours * 60 * 60 * 1000);

    const [stats] = await db
      .select({ count: count(message.id) })
      .from(message)
      .innerJoin(chat, eq(message.chatId, chat.id))
      .where(and(eq(chat.userId, id), gte(message.createdAt, twentyFourHoursAgo), eq(message.role, "user")))
      .execute();

    return stats?.count ?? 0;
  } catch (error) {
    console.error("Failed to get message count by user id for the last 24 hours from database");
    throw error;
  }
}

export async function createStreamId({ streamId, chatId }: { streamId: string; chatId: string }) {
  try {
    await db.insert(stream).values({ id: streamId, chatId, createdAt: new Date() });
  } catch (error) {
    console.error("Failed to create stream id in database");
    throw error;
  }
}

export async function getStreamIdsByChatId({ chatId }: { chatId: string }) {
  try {
    const streamIds = await db.select({ id: stream.id }).from(stream).where(eq(stream.chatId, chatId)).orderBy(desc(stream.createdAt)).execute();

    return streamIds.map(({ id }) => id);
  } catch (error) {
    console.error("Failed to get stream ids by chat id from database");
    throw error;
  }
}

export async function createUser({ clerkId, email, name, departments }: { clerkId: string; email: string; name: string; departments: string[] }) {
  try {
    // Check if user with this email or clerkId already exists
    const existingUser = await db
      .select()
      .from(user)
      .where(or(eq(user.email, email), eq(user.clerkId, clerkId)))
      .limit(1);

    // Only insert if no user exists
    if (existingUser.length === 0) {
      const [newUser] = await db
        .insert(user)
        .values({
          clerkId,
          email,
          name,
          departments,
          createdAt: new Date(),
          updatedAt: new Date(),
        })
        .returning();

      return [newUser] as User[];
    }

    return existingUser as User[];
  } catch (error) {
    console.error("Failed to create user in database:", error);
    throw error;
  }
}

export async function createMemory({ userId, content }: { userId: string; content: string }) {
  try {
    const [newMemory] = await db
      .insert(memory)
      .values({
        content,
        userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();
    return newMemory as Memory;
  } catch (error) {
    console.error("Failed to create memory in database:", error);
    throw error;
  }
}

export async function getMemoriesByUserId({ userId, limit }: { userId: string; limit: number }) {
  try {
    return await db.select().from(memory).where(eq(memory.userId, userId)).orderBy(desc(memory.createdAt)).limit(limit);
  } catch (error) {
    console.error("Failed to get memories by user id from database", error);
    throw error;
  }
}

export async function getCompanyFiles({ email }: { email: string }) {
  try {
    // Extract domain from email
    const domain = email.split("@")[1];

    const files = await db
      .select({
        id: document_metadata.id,
        name: document_metadata.name,
        fileType: document_metadata.fileType,
        createdAt: document_metadata.createdAt,
        updatedAt: document_metadata.updatedAt,
        permissions: document_metadata.permissions,
        url: document_metadata.url,
        department: document_metadata.department,
        owner: document_metadata.owner,
        path: document_metadata.path,
      })
      .from(document_metadata)
      .where(or(sql`${domain} = ANY(${document_metadata.permissions})`, sql`${email} = ANY(${document_metadata.permissions})`))
      .execute();

    return files;
  } catch (error) {
    console.error("Failed to get company files:", error);
    console.error("Error details:", (error as Error).message);
    return [];
  }
}

export async function getPersonalFiles({ userId }: { userId: string }) {
  try {
    const files = await db
      .select({
        id: ai_document.id,
        title: ai_document.title,
        kind: ai_document.kind,
        createdAt: ai_document.createdAt,
        metadata: ai_document.metadata,
      })
      .from(ai_document)
      .where(eq(ai_document.userId, userId));

    return files;
  } catch (error) {
    console.error("Failed to get personal files:", error);
    throw error;
  }
}

export async function updateMemory({
  memoryId,
  userId,
  content,
}: {
  memoryId: string;
  userId: string;
  content: string;
}): Promise<Memory | undefined> {
  try {
    const [updatedMemory] = await db
      .update(memory)
      .set({ content, updatedAt: new Date() })
      .where(and(eq(memory.id, memoryId), eq(memory.userId, userId)))
      .returning();
    return updatedMemory;
  } catch (error) {
    console.error(`Failed to update memory ${memoryId} for user ${userId} in database`, error);
    throw error;
  }
}

export async function deleteMemory({ memoryId, userId }: { memoryId: string; userId: string }): Promise<Memory | undefined> {
  try {
    const [deletedMemory] = await db
      .delete(memory)
      .where(and(eq(memory.id, memoryId), eq(memory.userId, userId)))
      .returning();
    return deletedMemory;
  } catch (error) {
    console.error(`Failed to delete memory ${memoryId} for user ${userId} from database`, error);
    throw error;
  }
}

export async function createMcpConfig({ userId, name, config, credential }: { userId: string; name: string; config: any; credential?: string }) {
  try {
    // Don't deactivate other configs - allow multiple active MCPs
    const [newCfg] = await db
      .insert(mcp_config)
      .values({
        userId,
        name,
        config,
        credential,
        active: true, // New configs are active by default
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();
    return newCfg as McpConfig;
  } catch (error) {
    console.error("Failed to create mcp config in database:", error);
    throw error;
  }
}

export async function getMcpConfigsByUserId({ userId }: { userId: string }) {
  try {
    return await db.select().from(mcp_config).where(eq(mcp_config.userId, userId));
  } catch (error) {
    console.error("Failed to get mcp configs from database:", error);
    throw error;
  }
}

// Updated to toggle individual MCP status without affecting others
export async function toggleMcpConfig({ userId, id }: { userId: string; id: string }) {
  try {
    // Get current status
    const [currentConfig] = await db
      .select()
      .from(mcp_config)
      .where(and(eq(mcp_config.userId, userId), eq(mcp_config.id, id)))
      .limit(1);

    if (!currentConfig) {
      throw new Error("MCP config not found");
    }

    // Toggle the active status
    const [updated] = await db
      .update(mcp_config)
      .set({ active: !currentConfig.active, updatedAt: new Date() })
      .where(and(eq(mcp_config.userId, userId), eq(mcp_config.id, id)))
      .returning();

    return updated as McpConfig;
  } catch (error) {
    console.error("Failed to toggle mcp config:", error);
    throw error;
  }
}

// Keep the old function for backward compatibility but update to not deactivate others
export async function setActiveMcpConfig({ userId, id }: { userId: string; id: string }) {
  try {
    // Just activate the specified config without deactivating others
    const [updated] = await db
      .update(mcp_config)
      .set({ active: true, updatedAt: new Date() })
      .where(and(eq(mcp_config.userId, userId), eq(mcp_config.id, id)))
      .returning();
    return updated as McpConfig;
  } catch (error) {
    console.error("Failed to set active mcp config:", error);
    throw error;
  }
}

export async function deleteMcpConfig({ userId, id }: { userId: string; id: string }) {
  try {
    const [deleted] = await db
      .delete(mcp_config)
      .where(and(eq(mcp_config.id, id), eq(mcp_config.userId, userId)))
      .returning();
    return deleted as McpConfig;
  } catch (error) {
    console.error("Failed to delete mcp config from database:", error);
    throw error;
  }
}

// Get the first active config (for backward compatibility)
export async function getActiveMcpConfig({ userId }: { userId: string }) {
  try {
    const [cfg] = await db
      .select()
      .from(mcp_config)
      .where(and(eq(mcp_config.userId, userId), eq(mcp_config.active, true)))
      .limit(1);
    return cfg as McpConfig | undefined;
  } catch (error) {
    console.error("Failed to get active mcp config:", error);
    throw error;
  }
}

// Get all active MCP configs for a user
export async function getActiveMcpConfigs({ userId }: { userId: string }) {
  try {
    const configs = await db
      .select()
      .from(mcp_config)
      .where(and(eq(mcp_config.userId, userId), eq(mcp_config.active, true)));
    return configs as McpConfig[];
  } catch (error) {
    console.error("Failed to get active mcp configs:", error);
    throw error;
  }
}

export async function updateChatTitleById({ chatId, title }: { chatId: string; title: string }) {
  try {
    return await db.update(chat).set({ title }).where(eq(chat.id, chatId));
  } catch (error) {
    console.error("Failed to update chat title in database");
    throw error;
  }
}
