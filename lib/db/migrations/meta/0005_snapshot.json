{"id": "cc6753f5-354d-40ab-86e2-ec30d76e572f", "prevId": "04fa4cb8-5b66-4732-8123-c41a52bc86d0", "version": "7", "dialect": "postgresql", "tables": {"public.ai_document": {"name": "ai_document", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false}, "text": {"name": "text", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'text'"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"ai_document_id_created_at_pk": {"name": "ai_document_id_created_at_pk", "columns": ["id", "created_at"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chat": {"name": "chat", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "visibility": {"name": "visibility", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'private'"}}, "indexes": {}, "foreignKeys": {"chat_user_id_user_id_fk": {"name": "chat_user_id_user_id_fk", "tableFrom": "chat", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.department_leaders": {"name": "department_leaders", "schema": "", "columns": {"department": {"name": "department", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"department_leaders_department_user_id_pk": {"name": "department_leaders_department_user_id_pk", "columns": ["department", "user_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.document_metadata": {"name": "document_metadata", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "file_title": {"name": "file_title", "type": "text", "primaryKey": false, "notNull": false}, "file_type": {"name": "file_type", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "owner": {"name": "owner", "type": "text", "primaryKey": false, "notNull": false}, "path": {"name": "path", "type": "text", "primaryKey": false, "notNull": false}, "permissions": {"name": "permissions", "type": "text[]", "primaryKey": false, "notNull": false}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": false}, "department": {"name": "department", "type": "text", "primaryKey": false, "notNull": false}, "schema": {"name": "schema", "type": "text", "primaryKey": false, "notNull": false}, "data_source": {"name": "data_source", "type": "text", "primaryKey": false, "notNull": false}, "segments": {"name": "segments", "type": "text[]", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": false}, "size": {"name": "size", "type": "bigint", "primaryKey": false, "notNull": false}, "content_hash": {"name": "content_hash", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.document_rows": {"name": "document_rows", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "dataset_id": {"name": "dataset_id", "type": "text", "primaryKey": false, "notNull": false}, "row_data": {"name": "row_data", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"document_rows_dataset_id_document_metadata_id_fk": {"name": "document_rows_dataset_id_document_metadata_id_fk", "tableFrom": "document_rows", "tableTo": "document_metadata", "columnsFrom": ["dataset_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.documents": {"name": "documents", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "embedding": {"name": "embedding", "type": "vector(768)", "primaryKey": false, "notNull": false}, "file_id": {"name": "file_id", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.mcp_config": {"name": "mcp_config", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "config": {"name": "config", "type": "jsonb", "primaryKey": false, "notNull": true}, "credential": {"name": "credential", "type": "text", "primaryKey": false, "notNull": false}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"mcp_config_user_id_user_id_fk": {"name": "mcp_config_user_id_user_id_fk", "tableFrom": "mcp_config", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.memory": {"name": "memory", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.message": {"name": "message", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "chat_id": {"name": "chat_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "parts": {"name": "parts", "type": "json", "primaryKey": false, "notNull": true}, "attachments": {"name": "attachments", "type": "json", "primaryKey": false, "notNull": true}, "sources": {"name": "sources", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"message_chat_id_chat_id_fk": {"name": "message_chat_id_chat_id_fk", "tableFrom": "message", "tableTo": "chat", "columnsFrom": ["chat_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.n8n_chat_histories": {"name": "n8n_chat_histories", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "session_id": {"name": "session_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "jsonb", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.stream": {"name": "stream", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "chat_id": {"name": "chat_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"stream_chat_id_chat_id_fk": {"name": "stream_chat_id_chat_id_fk", "tableFrom": "stream", "tableTo": "chat", "columnsFrom": ["chat_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"stream_id_pk": {"name": "stream_id_pk", "columns": ["id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.suggestion": {"name": "suggestion", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "document_id": {"name": "document_id", "type": "uuid", "primaryKey": false, "notNull": true}, "document_created_at": {"name": "document_created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "original_text": {"name": "original_text", "type": "text", "primaryKey": false, "notNull": true}, "suggested_text": {"name": "suggested_text", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_resolved": {"name": "is_resolved", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"suggestion_document_id_document_created_at_ai_document_id_created_at_fk": {"name": "suggestion_document_id_document_created_at_ai_document_id_created_at_fk", "tableFrom": "suggestion", "tableTo": "ai_document", "columnsFrom": ["document_id", "document_created_at"], "columnsTo": ["id", "created_at"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"suggestion_id_pk": {"name": "suggestion_id_pk", "columns": ["id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "clerkId": {"name": "clerkId", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "departments": {"name": "departments", "type": "text[]", "primaryKey": false, "notNull": true, "default": "'{\"\"}'"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_clerkId_unique": {"name": "user_clerkId_unique", "nullsNotDistinct": false, "columns": ["clerkId"]}, "user_email_unique": {"name": "user_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vote": {"name": "vote", "schema": "", "columns": {"chat_id": {"name": "chat_id", "type": "uuid", "primaryKey": false, "notNull": true}, "message_id": {"name": "message_id", "type": "uuid", "primaryKey": false, "notNull": true}, "is_upvoted": {"name": "is_upvoted", "type": "boolean", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"vote_chat_id_chat_id_fk": {"name": "vote_chat_id_chat_id_fk", "tableFrom": "vote", "tableTo": "chat", "columnsFrom": ["chat_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "vote_message_id_message_id_fk": {"name": "vote_message_id_message_id_fk", "tableFrom": "vote", "tableTo": "message", "columnsFrom": ["message_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"vote_chat_id_message_id_pk": {"name": "vote_chat_id_message_id_pk", "columns": ["chat_id", "message_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}