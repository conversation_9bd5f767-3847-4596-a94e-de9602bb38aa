import { relations } from "drizzle-orm/relations";
import { documentMetadata, documentRows, user, chat, stream, aiDocument, suggestion, message, vote } from "./schema";

export const documentRowsRelations = relations(documentRows, ({ one }) => ({
  documentMetadatum_datasetId: one(documentMetadata, {
    fields: [documentRows.datasetId],
    references: [documentMetadata.id],
    relationName: "documentRows_datasetId_documentMetadata_id",
  }),
}));

export const documentMetadataRelations = relations(documentMetadata, ({ many }) => ({
  documentRows_datasetId: many(documentRows, {
    relationName: "documentRows_datasetId_documentMetadata_id",
  }),
}));

export const chatRelations = relations(chat, ({ one, many }) => ({
  user: one(user, {
    fields: [chat.userId],
    references: [user.id],
  }),
  streams: many(stream),
  messages: many(message),
  votes: many(vote),
}));

export const userRelations = relations(user, ({ many }) => ({
  chats: many(chat),
}));

export const streamRelations = relations(stream, ({ one }) => ({
  chat: one(chat, {
    fields: [stream.chatId],
    references: [chat.id],
  }),
}));

export const suggestionRelations = relations(suggestion, ({ one }) => ({
  aiDocument: one(aiDocument, {
    fields: [suggestion.documentId],
    references: [aiDocument.id],
  }),
}));

export const aiDocumentRelations = relations(aiDocument, ({ many }) => ({
  suggestions: many(suggestion),
}));

export const messageRelations = relations(message, ({ one, many }) => ({
  chat: one(chat, {
    fields: [message.chatId],
    references: [chat.id],
  }),
  votes: many(vote),
}));

export const voteRelations = relations(vote, ({ one }) => ({
  chat: one(chat, {
    fields: [vote.chatId],
    references: [chat.id],
  }),
  message: one(message, {
    fields: [vote.messageId],
    references: [message.id],
  }),
}));
