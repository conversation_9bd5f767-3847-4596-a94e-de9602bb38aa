CREATE TABLE "n8n_chat_histories" (
	"id" serial PRIMARY KEY NOT NULL,
	"session_id" varchar(255) NOT NULL,
	"message" jsonb NOT NULL
);
--> statement-breakpoint
ALTER TABLE "mcp_config" ALTER COLUMN "created_at" SET DEFAULT now();--> statement-breakpoint
ALTER TABLE "mcp_config" ALTER COLUMN "updated_at" SET DEFAULT now();--> statement-breakpoint
ALTER TABLE "document_metadata" ADD COLUMN "file_title" text;--> statement-breakpoint
ALTER TABLE "document_metadata" ADD COLUMN "data_source" text;--> statement-breakpoint
ALTER TABLE "document_metadata" ADD COLUMN "segments" text[];--> statement-breakpoint
ALTER TABLE "document_metadata" ADD COLUMN "category" text;