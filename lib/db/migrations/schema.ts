import {
  bigint,
  bigserial,
  boolean,
  foreignKey,
  json,
  jsonb,
  pgTable,
  primaryKey,
  serial,
  text,
  timestamp,
  unique,
  uuid,
  varchar,
  vector,
} from "drizzle-orm/pg-core";

export const documentRows = pgTable(
  "document_rows",
  {
    id: serial().primaryKey().notNull(),
    datasetId: text("dataset_id"),
    rowData: jsonb("row_data"),
  },
  (table) => [
    foreignKey({
      columns: [table.datasetId],
      foreignColumns: [documentMetadata.id],
      name: "document_rows_dataset_id_fkey",
    }),
    foreignKey({
      columns: [table.datasetId],
      foreignColumns: [documentMetadata.id],
      name: "document_rows_dataset_id_document_metadata_id_fk",
    }),
  ]
);

export const documents = pgTable("documents", {
  id: bigserial({ mode: "bigint" }).primaryKey().notNull(),
  content: text(),
  metadata: jsonb(),
  embedding: vector({ dimensions: 768 }),
  fileId: text("file_id"),
});

export const memory = pgTable("memory", {
  id: uuid().defaultRandom().primaryKey().notNull(),
  content: text(),
  createdAt: timestamp("created_at", { mode: "string" }).notNull(),
  updatedAt: timestamp("updated_at", { mode: "string" }).notNull(),
  userId: uuid("user_id").notNull(),
});

export const user = pgTable(
  "user",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    clerkId: varchar({ length: 64 }).notNull(),
    email: varchar({ length: 64 }).notNull(),
    createdAt: timestamp("created_at", { mode: "string" }).defaultNow().notNull(),
    updatedAt: timestamp("updated_at", { mode: "string" }).defaultNow().notNull(),
    name: text(),
    departments: text().array().default([""]).notNull(),
  },
  (table) => [unique("user_clerkId_unique").on(table.clerkId), unique("user_email_unique").on(table.email)]
);

export const chat = pgTable(
  "chat",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    createdAt: timestamp("created_at", { mode: "string" }).notNull(),
    title: text().notNull(),
    userId: uuid("user_id").notNull(),
    visibility: varchar().default("private").notNull(),
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [user.id],
      name: "chat_user_id_user_id_fk",
    }),
  ]
);

export const stream = pgTable(
  "stream",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    chatId: uuid("chat_id").notNull(),
    createdAt: timestamp("created_at", { mode: "string" }).notNull(),
  },
  (table) => [
    foreignKey({
      columns: [table.chatId],
      foreignColumns: [chat.id],
      name: "stream_chat_id_chat_id_fk",
    }),
  ]
);

export const suggestion = pgTable(
  "suggestion",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    documentId: uuid("document_id").notNull(),
    documentCreatedAt: timestamp("document_created_at", { mode: "string" }).notNull(),
    originalText: text("original_text").notNull(),
    suggestedText: text("suggested_text").notNull(),
    description: text(),
    isResolved: boolean("is_resolved").default(false).notNull(),
    userId: uuid("user_id").notNull(),
    createdAt: timestamp("created_at", { mode: "string" }).notNull(),
  },
  (table) => [
    foreignKey({
      columns: [table.documentId, table.documentCreatedAt],
      foreignColumns: [aiDocument.id, aiDocument.createdAt],
      name: "suggestion_document_id_document_created_at_ai_document_id_creat",
    }),
  ]
);

export const message = pgTable(
  "message",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    chatId: uuid("chat_id").notNull(),
    role: varchar().notNull(),
    parts: json().notNull(),
    attachments: json().notNull(),
    createdAt: timestamp("created_at", { mode: "string" }).notNull(),
    sources: json(),
  },
  (table) => [
    foreignKey({
      columns: [table.chatId],
      foreignColumns: [chat.id],
      name: "message_chat_id_chat_id_fk",
    }),
  ]
);

export const documentMetadata = pgTable("document_metadata", {
  id: text().primaryKey().notNull(),
  name: text(),
  fileType: text("file_type"),
  createdAt: timestamp("created_at", { mode: "string" }),
  updatedAt: timestamp("updated_at", { mode: "string" }),
  owner: text(),
  path: text(),
  permissions: text().array(),
  url: text(),
  department: text(),
  schema: text(),
  dataSource: text("data_source"),
  segments: text().array(),
  category: text(),
  // You can use { mode: "bigint" } if numbers are exceeding js number limitations
  size: bigint({ mode: "number" }),
  contentHash: text("content_hash"),
});

export const n8NChatHistories = pgTable("n8n_chat_histories", {
  id: serial().primaryKey().notNull(),
  sessionId: varchar("session_id", { length: 255 }).notNull(),
  message: jsonb().notNull(),
});

export const departmentLeaders = pgTable(
  "department_leaders",
  {
    department: text().notNull(),
    userId: uuid("user_id").notNull(),
  },
  (table) => [primaryKey({ columns: [table.department, table.userId], name: "department_leaders_department_user_id_pk" })]
);

export const vote = pgTable(
  "vote",
  {
    chatId: uuid("chat_id").notNull(),
    messageId: uuid("message_id").notNull(),
    isUpvoted: boolean("is_upvoted").notNull(),
  },
  (table) => [
    foreignKey({
      columns: [table.chatId],
      foreignColumns: [chat.id],
      name: "vote_chat_id_chat_id_fk",
    }),
    foreignKey({
      columns: [table.messageId],
      foreignColumns: [message.id],
      name: "vote_message_id_message_id_fk",
    }),
    primaryKey({ columns: [table.chatId, table.messageId], name: "vote_chat_id_message_id_pk" }),
  ]
);

export const aiDocument = pgTable(
  "ai_document",
  {
    id: uuid().defaultRandom().notNull(),
    createdAt: timestamp("created_at", { mode: "string" }).notNull(),
    title: text().notNull(),
    content: text(),
    text: varchar().default("text").notNull(),
    userId: text("user_id").notNull(),
    metadata: jsonb().default({}).notNull(),
  },
  (table) => [primaryKey({ columns: [table.id, table.createdAt], name: "ai_document_id_created_at_pk" })]
);
