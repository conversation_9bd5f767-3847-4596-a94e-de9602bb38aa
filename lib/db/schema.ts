import type { InferSelectModel } from "drizzle-orm";
import {
  bigint,
  bigserial,
  boolean,
  foreignKey,
  json,
  jsonb,
  pgTable,
  primaryKey,
  serial,
  text,
  timestamp,
  uuid,
  varchar,
  vector,
} from "drizzle-orm/pg-core";

export const user = pgTable("user", {
  id: uuid("id").primaryKey().notNull().defaultRandom(),
  clerkId: varchar("clerkId", { length: 64 }).notNull().unique(),
  email: varchar("email", { length: 64 }).notNull().unique(),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
  name: text("name"),
  departments: text("departments").array().default([""]).notNull(),
});
export type User = InferSelectModel<typeof user>;

export const chat = pgTable("chat", {
  id: uuid("id").primaryKey().notNull().defaultRandom(),
  createdAt: timestamp("created_at").notNull(),
  title: text("title").notNull(),
  userId: uuid("user_id")
    .notNull()
    .references(() => user.id),
  visibility: varchar("visibility").notNull().default("private"),
});
export type Chat = InferSelectModel<typeof chat>;

export const message = pgTable("message", {
  id: uuid("id").primaryKey().notNull().defaultRandom(),
  chatId: uuid("chat_id")
    .notNull()
    .references(() => chat.id),
  role: varchar("role").notNull(),
  parts: json("parts").notNull(),
  attachments: json("attachments").notNull(),
  sources: json("sources"),
  createdAt: timestamp("created_at").notNull(),
});
export type Message = InferSelectModel<typeof message>;

export const vote = pgTable(
  "vote",
  {
    chatId: uuid("chat_id")
      .notNull()
      .references(() => chat.id),
    messageId: uuid("message_id")
      .notNull()
      .references(() => message.id),
    isUpvoted: boolean("is_upvoted").notNull(),
  },
  (table) => {
    return {
      pk: primaryKey({ columns: [table.chatId, table.messageId] }),
    };
  }
);
export type Vote = InferSelectModel<typeof vote>;

export const ai_document = pgTable(
  "ai_document",
  {
    id: uuid("id").notNull().defaultRandom(),
    createdAt: timestamp("created_at").notNull(),
    title: text("title").notNull(),
    content: text("content"),
    kind: varchar("text", { enum: ["text", "code", "image", "sheet"] })
      .notNull()
      .default("text"),
    userId: text("user_id").notNull(),
    metadata: jsonb("metadata").notNull().default("{}"),
  },
  (table) => {
    return {
      pk: primaryKey({ columns: [table.id, table.createdAt] }),
    };
  }
);

export type AIDocument = InferSelectModel<typeof ai_document>;

export const suggestion = pgTable(
  "suggestion",
  {
    id: uuid("id").notNull().defaultRandom(),
    documentId: uuid("document_id").notNull(),
    documentCreatedAt: timestamp("document_created_at").notNull(),
    originalText: text("original_text").notNull(),
    suggestedText: text("suggested_text").notNull(),
    description: text("description"),
    isResolved: boolean("is_resolved").notNull().default(false),
    userId: uuid("user_id").notNull(),
    createdAt: timestamp("created_at").notNull(),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.id] }),
    documentRef: foreignKey({
      columns: [table.documentId, table.documentCreatedAt],
      foreignColumns: [ai_document.id, ai_document.createdAt],
    }),
  })
);
export type Suggestion = InferSelectModel<typeof suggestion>;

export const stream = pgTable(
  "stream",
  {
    id: uuid("id").notNull().defaultRandom(),
    chatId: uuid("chat_id").notNull(),
    createdAt: timestamp("created_at").notNull(),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.id] }),
    chatRef: foreignKey({
      columns: [table.chatId],
      foreignColumns: [chat.id],
    }),
  })
);
export type Stream = InferSelectModel<typeof stream>;

// Department leaders join table
export const department_leaders = pgTable(
  "department_leaders",
  {
    department: text("department").notNull(),
    user_id: uuid("user_id").notNull(),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.department, table.user_id] }),
  })
);

export const documents = pgTable("documents", {
  id: bigserial("id", { mode: "bigint" }).primaryKey().notNull(),
  content: text("content"),
  metadata: jsonb("metadata"),
  embedding: vector("embedding", { dimensions: 768 }),
  file_id: text("file_id"),
});
export type Document = InferSelectModel<typeof documents>;

export const document_metadata = pgTable("document_metadata", {
  id: text("id").primaryKey().notNull(),
  name: text("name"),
  fileTitle: text("file_title"),
  fileType: text("file_type"),
  createdAt: timestamp("created_at"),
  updatedAt: timestamp("updated_at"),
  owner: text("owner"),
  path: text("path"),
  permissions: text("permissions").array(),
  url: text("url"),
  department: text("department"),
  schema: text("schema"),
  dataSource: text("data_source"),
  segments: text("segments").array(),
  category: text("category"),
  size: bigint("size", { mode: "number" }),
  contentHash: text("content_hash"),
});
export type DocumentMetadata = InferSelectModel<typeof document_metadata>;

export const document_rows = pgTable("document_rows", {
  id: serial("id").primaryKey().notNull(),
  dataset_id: text("dataset_id").references(() => document_metadata.id),
  row_data: jsonb("row_data"),
});
export type DocumentRow = InferSelectModel<typeof document_rows>;

export const memory = pgTable("memory", {
  id: uuid("id").primaryKey().notNull().defaultRandom(),
  content: text("content"),
  createdAt: timestamp("created_at").notNull(),
  updatedAt: timestamp("updated_at").notNull(),
  userId: uuid("user_id").notNull(),
});
export type Memory = InferSelectModel<typeof memory>;

export const mcp_config = pgTable("mcp_config", {
  id: uuid("id").primaryKey().notNull().defaultRandom(),
  userId: uuid("user_id")
    .notNull()
    .references(() => user.id),
  name: text("name").notNull(),
  config: jsonb("config").notNull(),
  credential: text("credential"),
  active: boolean("active").notNull().default(false),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});
export type McpConfig = InferSelectModel<typeof mcp_config>;

export const n8NChatHistories = pgTable("n8n_chat_histories", {
  id: serial().primaryKey().notNull(),
  sessionId: varchar("session_id", { length: 255 }).notNull(),
  message: jsonb().notNull(),
});
export type N8NChatHistory = InferSelectModel<typeof n8NChatHistories>;
