import { createUser, getUser } from "@/lib/db/queries";
import { currentUser } from "@clerk/nextjs/server";
import { createClient } from "redis";
import { LRUCache } from "lru-cache";

// Types
interface UserData {
  userId: string;
  email: string;
  name: string;
}

// Cache configuration
const CONFIG = {
  CACHE_TTL_SECONDS: 5 * 60, // 5 minutes for Redis
  CACHE_VERSION: "v1", // For cache invalidation if needed
  CACHE_PREFIX: "optimus:user", // Unique prefix to avoid collisions
} as const;

// Redis client setup (lazy initialization)
let redisClient: ReturnType<typeof createClient> | null = null;
let redisConnectionPromise: Promise<any> | null = null;

function getRedisClient() {
  if (!process.env.REDIS_URL) return null;
  if (!redisClient) {
    const client = createClient({
      url: process.env.REDIS_URL,
      socket: {
        reconnectStrategy: (retries) => Math.min(retries * 50, 1000),
      },
    });

    client.on("error", (err) => console.warn("⚠️ Redis client error:", err));
    client.on("connect", () => console.log("✅ Redis connected"));

    redisClient = client;
    redisConnectionPromise = client.connect().catch((err) => {
      console.warn("⚠️ Redis connection failed, using memory fallback:", err);
      redisClient = null;
      redisConnectionPromise = null;
    });
  }
  return redisClient;
}

// Fallback in-memory cache using LRU cache
const memoryCache = new LRUCache<string, UserData>({
  max: 1000,
  ttl: CONFIG.CACHE_TTL_SECONDS * 1000, // Convert to milliseconds
});

// Helper function to create unique cache keys
function createCacheKey(clerkId: string): string {
  return `${CONFIG.CACHE_PREFIX}:${CONFIG.CACHE_VERSION}:${clerkId}`;
}

// Core function to fetch user data from database
async function fetchUserFromDatabase(clerkId: string): Promise<UserData | null> {
  const user = await currentUser();
  if (!user || user.id !== clerkId) {
    return null;
  }

  let userDataArray = await getUser(clerkId, "");

  if (!userDataArray || userDataArray.length === 0) {
    userDataArray = await createUser({
      clerkId: clerkId,
      email: user.emailAddresses[0]?.emailAddress || "",
      name: [user.firstName, user.lastName].filter(Boolean).join(" ") || "Unnamed User",
      departments: [],
    });
  }

  if (!userDataArray || userDataArray.length === 0 || !userDataArray[0]?.id) {
    console.error(`❌ Failed to get or create user for clerkId ${clerkId}`);
    return null;
  }

  const dbUser = userDataArray[0];

  return {
    userId: dbUser.id,
    email: dbUser.email,
    name: dbUser.name || "",
  };
}

/**
 * Get full user data with caching
 * Returns userId, email, and name
 */
export async function getUserData(): Promise<UserData | null> {
  const user = await currentUser();
  if (!user) {
    return null;
  }

  const { id: clerkId } = user;
  const cacheKey = createCacheKey(clerkId);

  // 1. Try Redis first
  const redis = getRedisClient();
  if (redis) {
    if (redisConnectionPromise) await redisConnectionPromise;

    if (redis.isReady) {
      try {
        const cacheData = await redis.get(cacheKey);
        if (cacheData) {
          return JSON.parse(String(cacheData));
        }
      } catch (error) {
        console.warn("Redis get error:", error);
      }
    }
  }

  // 2. Try Memory Cache
  const memoryData = memoryCache.get(clerkId);
  if (memoryData) {
    return memoryData;
  }

  // 3. Fetch from Database
  const dbUserData = await fetchUserFromDatabase(clerkId);
  if (!dbUserData) {
    return null;
  }

  // 4. Populate both caches
  memoryCache.set(clerkId, dbUserData);
  if (redis?.isReady) {
    redis.setEx(cacheKey, CONFIG.CACHE_TTL_SECONDS, JSON.stringify(dbUserData)).catch((err) => {
      console.warn("Redis setEx error:", err);
    });
  }

  return dbUserData;
}

/**
 * Get only the userId with caching
 * This is the most commonly used function
 */
export async function getUserId(): Promise<string | null> {
  const userData = await getUserData();
  return userData?.userId ?? null;
}

/**
 * Invalidate cache for a specific user
 */
// export async function invalidateUserCache(clerkId: string): Promise<void> {
//   const cacheKey = createCacheKey(clerkId);

//   // Clear from memory cache
//   memoryCache.delete(clerkId);

//   // Clear from Redis
//   const redis = getRedisClient();
//   if (redis?.isReady) {
//     try {
//       await redis.del(cacheKey);
//     } catch (error) {
//       console.warn("Redis invalidate error:", error);
//     }
//   }
// }

/**
 * Clear all user caches (admin function)
 */
// export async function invalidateAllUserCache(): Promise<void> {
//   // Clear memory cache
//   memoryCache.clear();

//   // Clear Redis cache
//   const redis = getRedisClient();
//   if (redis?.isReady) {
//     try {
//       const pattern = `${CONFIG.CACHE_PREFIX}:${CONFIG.CACHE_VERSION}:*`;
//       const keys = await redis.keys(pattern);
//       if (keys.length > 0) {
//         await redis.del(keys);
//       }
//     } catch (error) {
//       console.warn("Redis bulk invalidate error:", error);
//     }
//   }
// }
