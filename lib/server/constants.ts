// Server-only constants and utilities (includes MCP dependencies)
import { TOOL_IDS, WORKSPACE_TOOL_CONFIGS, MODES, type ToolId, type Mode } from "@/lib/constants";
import { MCP_IDS, type McpId } from "@/lib/ai/mcp/config";

// Server-only tool configuration type (extends client-safe ToolConfig)
export interface ServerToolConfig {
  builtInTools: ToolId[];
  mcpTools: McpId[];
}

// Mode-specific tool configurations (server-only due to MCP dependencies)
export const MODE_TOOL_CONFIGS: Record<Mode, ServerToolConfig> = {
  [MODES.DEFAULT]: {
    builtInTools: [],
    mcpTools: [],
  },
  [MODES.PO]: {
    builtInTools: [],
    mcpTools: [MCP_IDS.PO],
  },
};

// Server-only utility functions for dynamic tool resolution
export const getToolsForWorkspaceAndMode = (workspaceId: string, mode: Mode): { builtInTools: ToolId[]; mcpTools: McpId[] } => {
  const workspaceConfig = WORKSPACE_TOOL_CONFIGS[workspaceId] || { builtInTools: [], mcpTools: [] };
  const modeConfig = MODE_TOOL_CONFIGS[mode] || { builtInTools: [], mcpTools: [] };

  // Combine and deduplicate tools
  const combinedBuiltInTools = Array.from(new Set([...workspaceConfig.builtInTools, ...modeConfig.builtInTools]));
  const combinedMcpTools = Array.from(new Set([...(workspaceConfig.mcpTools as McpId[]), ...(modeConfig.mcpTools as McpId[])]));

  return {
    builtInTools: combinedBuiltInTools,
    mcpTools: combinedMcpTools,
  };
};

export const getAllAvailableToolsServer = (): {
  builtInTools: ToolId[];
  mcpTools: McpId[];
} => {
  return {
    builtInTools: Object.values(TOOL_IDS),
    mcpTools: Object.values(MCP_IDS),
  };
};

export const JIRA_USER_INFO = [
  {
    projectId: "21962",
    project: "PF: Shopify",
    member: "Nguyễn Văn Hiệp",
    email: "<EMAIL>",
    jiraId: "557058:707fc141-7a71-4687-9520-061c9639259c",
  },
  { projectId: "21962", project: "PF: Shopify", member: "Lâm Minh Thành", email: "<EMAIL>", jiraId: "5ed84b59be03220ab3277894" },
  {
    projectId: "21962",
    project: "PF: Shopify",
    member: "Nguyễn Huy Hoàng",
    email: "<EMAIL>",
    jiraId: "712020:db147b5c-291c-4294-bb65-a2bc6f11ff46",
  },
  { projectId: "21962", project: "PF: Shopify", member: "Nguyễn Quang Huy 2K2", email: "<EMAIL>", jiraId: "62fef325142d0c981fce90b0" },
  {
    projectId: "21962",
    project: "PF: Shopify",
    member: "Nguyễn Quốc Vượng",
    email: "<EMAIL>",
    jiraId: "712020:2c000d52-5cf1-40f4-8831-bc6ffa45c84e",
  },
  { projectId: "21962", project: "PF: Shopify", member: "Nguyễn Quang Huy 98", email: "<EMAIL>", jiraId: "631a98a9ea661fd37d50f6cf" },
  {
    projectId: "21962",
    project: "PF: Shopify",
    member: "Nguyễn Đức Giang",
    email: "<EMAIL>",
    jiraId: "557058:e17dc022-7916-4b63-997b-fdf266bd811b",
  },
  {
    projectId: "21962",
    project: "PF: Shopify",
    member: "Chu Hữu Thành",
    email: "<EMAIL>",
    jiraId: "557058:5e131c6d-d609-4679-8957-67d6eb24a1d1",
  },
  { projectId: "21962", project: "PF: Shopify", member: "Phạm Văn Hiếu", email: "<EMAIL>", jiraId: "629eaccfd442e6006840c3a3" },
  { projectId: "21962", project: "PF: Shopify", member: "Đỗ Đức Hồng Phúc", email: "<EMAIL>", jiraId: "6281fe65f0302e0068bc2486" },
  {
    projectId: "21962",
    project: "PF: Shopify",
    member: "Nguyễn Thanh Thảo",
    email: "<EMAIL>",
    jiraId: "712020:76c49b91-2e7c-4057-9685-a738512cda62",
  },
  {
    projectId: "21962",
    project: "PF: Shopify",
    member: "Nguyễn Hữu Hiền",
    email: "<EMAIL>",
    jiraId: "557058:61f3da08-66fb-4e56-a1d6-f98498183f32",
  },
  { projectId: "21962", project: "PF: Shopify", member: "Nguyễn Ngọc Vũ", email: "<EMAIL>", jiraId: "5f16b4c207efc400284edcee" },
  {
    projectId: "21962",
    project: "PF: Shopify",
    member: "Vũ Văn Bộ",
    email: "<EMAIL>",
    jiraId: "712020:4c38b935-0254-40b1-a257-993fdf489fad",
  },
  { projectId: "21962", project: "PF: Shopify", member: "Vũ Hải Đăng", email: "<EMAIL>", jiraId: "63296bd188ed2ebef978b296" },
  { projectId: "21962", project: "PF: Shopify", member: "Vũ Tuấn Phong", email: "<EMAIL>", jiraId: "61719e54e79ff6006f4bbfc0" },
  {
    projectId: "22026",
    project: "EM: TailorKit",
    member: "Nguyễn Mạnh Cường",
    email: "<EMAIL>",
    jiraId: "557058:dcc52242-b086-41ef-8869-9902f64ce3d1",
  },
  { projectId: "22026", project: "EM: TailorKit", member: "Nguyễn Thị Khánh", email: "<EMAIL>", jiraId: "62b41f1870d15a142e3a24d6" },
  { projectId: "22026", project: "EM: TailorKit", member: "Phan Công Long", email: "<EMAIL>", jiraId: "62f9d0bbbb0704a2a8083fd9" },
  {
    projectId: "22026",
    project: "EM: TailorKit",
    member: "Nguyễn Minh Dung",
    email: "<EMAIL>",
    jiraId: "712020:2e17b57f-61b2-4bec-ae84-dc8dc44929d0",
  },
  {
    projectId: "22026",
    project: "EM: TailorKit",
    member: "Đinh Thị Hồng Hạnh",
    email: "<EMAIL>",
    jiraId: "712020:64a5a166-935c-4903-8ed3-ac5b0af5a0df",
  },
  {
    projectId: "22029",
    project: "SHT: Normcore",
    member: "Nguyễn Thị Hồng",
    email: "<EMAIL>",
    jiraId: "712020:d21008d4-c289-4eaf-8faa-0677ba043984",
  },
  {
    projectId: "22029",
    project: "SHT: Normcore",
    member: "Nguyễn Tiến Đạt",
    email: "<EMAIL>",
    jiraId: "557058:0ace5fd9-b121-4e9b-8fc7-7fb3295bcb6e",
  },
  { projectId: "22029", project: "SHT: Normcore", member: "Lê Tiến Thành", email: "<EMAIL>", jiraId: "629ec97d1be00a0068acc0a2" },
  {
    projectId: "22029",
    project: "SHT: Normcore",
    member: "Nguyễn Mạnh Đô",
    email: "<EMAIL>",
    jiraId: "557058:1ec80598-3014-403a-abf7-3e339670bfd1",
  },
  {
    projectId: "22029",
    project: "SHT: Normcore",
    member: "Lê Ngọc Kiên",
    email: "<EMAIL>",
    jiraId: "712020:d53b19e2-3874-4441-9e5c-6dc09aae7a05",
  },
  {
    projectId: "22029",
    project: "SHT: Normcore",
    member: "Tống Duy Khải",
    email: "<EMAIL>",
    jiraId: "712020:3aead4bc-ab23-44e1-8d3b-ff40ce44f5b5",
  },
  {
    projectId: "22029",
    project: "SHT: Normcore",
    member: "Lê Thị Trà Vân",
    email: "<EMAIL>",
    jiraId: "712020:dc458e1b-84e1-4993-bc43-c4a167484403",
  },
  {
    projectId: "21999",
    project: "SHT: Blum",
    member: "Nguyễn Thị Hồng",
    email: "<EMAIL>",
    jiraId: "712020:d21008d4-c289-4eaf-8faa-0677ba043984",
  },
  {
    projectId: "21999",
    project: "SHT: Blum",
    member: "Nguyễn Tiến Đạt",
    email: "<EMAIL>",
    jiraId: "557058:0ace5fd9-b121-4e9b-8fc7-7fb3295bcb6e",
  },
  { projectId: "21999", project: "SHT: Blum", member: "Lê Tiến Thành", email: "<EMAIL>", jiraId: "629ec97d1be00a0068acc0a2" },
  {
    projectId: "21999",
    project: "SHT: Blum",
    member: "Nguyễn Mạnh Đô",
    email: "<EMAIL>",
    jiraId: "557058:1ec80598-3014-403a-abf7-3e339670bfd1",
  },
  {
    projectId: "21999",
    project: "SHT: Blum",
    member: "Lê Ngọc Kiên",
    email: "<EMAIL>",
    jiraId: "712020:d53b19e2-3874-4441-9e5c-6dc09aae7a05",
  },
  {
    projectId: "21999",
    project: "SHT: Blum",
    member: "Tống Duy Khải",
    email: "<EMAIL>",
    jiraId: "712020:3aead4bc-ab23-44e1-8d3b-ff40ce44f5b5",
  },
  {
    projectId: "21999",
    project: "SHT: Blum",
    member: "Lê Thị Trà Vân",
    email: "<EMAIL>",
    jiraId: "712020:dc458e1b-84e1-4993-bc43-c4a167484403",
  },
  {
    projectId: "22012",
    project: "SHT: Electro",
    member: "Nguyễn Thị Hồng",
    email: "<EMAIL>",
    jiraId: "712020:d21008d4-c289-4eaf-8faa-0677ba043984",
  },
  {
    projectId: "22012",
    project: "SHT: Electro",
    member: "Nguyễn Tiến Đạt",
    email: "<EMAIL>",
    jiraId: "557058:0ace5fd9-b121-4e9b-8fc7-7fb3295bcb6e",
  },
  { projectId: "22012", project: "SHT: Electro", member: "Lê Tiến Thành", email: "<EMAIL>", jiraId: "629ec97d1be00a0068acc0a2" },
  {
    projectId: "22012",
    project: "SHT: Electro",
    member: "Nguyễn Mạnh Đô",
    email: "<EMAIL>",
    jiraId: "557058:1ec80598-3014-403a-abf7-3e339670bfd1",
  },
  {
    projectId: "22012",
    project: "SHT: Electro",
    member: "Lê Ngọc Kiên",
    email: "<EMAIL>",
    jiraId: "712020:d53b19e2-3874-4441-9e5c-6dc09aae7a05",
  },
  {
    projectId: "22012",
    project: "SHT: Electro",
    member: "Tống Duy Khải",
    email: "<EMAIL>",
    jiraId: "712020:3aead4bc-ab23-44e1-8d3b-ff40ce44f5b5",
  },
  {
    projectId: "22012",
    project: "SHT: Electro",
    member: "Lê Thị Trà Vân",
    email: "<EMAIL>",
    jiraId: "712020:dc458e1b-84e1-4993-bc43-c4a167484403",
  },
  {
    projectId: "22034",
    project: "BB: Workflow Efficiency",
    member: "Vũ Bảo Trung",
    email: "<EMAIL>",
    jiraId: "712020:1f29ee6c-daed-40e3-b87a-7518fa530e8d",
  },
  {
    projectId: "22034",
    project: "BB: Workflow Efficiency",
    member: "Nguyễn Trần Quốc Anh",
    email: "<EMAIL>",
    jiraId: "712020:97f21a0c-6da3-4ef2-b245-c27970f81991",
  },
  {
    projectId: "22034",
    project: "BB: Workflow Efficiency",
    member: "Nguyễn Duy Hoài Lâm",
    email: "<EMAIL>",
    jiraId: "712020:99b3600b-d3de-41b6-a869-e87ddd8b8507",
  },
];

// Re-export MCP types for server use
export { MCP_IDS, type McpId };
