// ==================== CACHE SYSTEM ====================

// Generic cache entry type
export interface CacheEntry<T> {
  timestamp: number;
  data: T;
}

// Default cache expiration time (1 minute)
export const DEFAULT_CACHE_EXPIRATION_MS = 60 * 1000;

/**
 * A generic caching class with expiration support
 */
export class CacheManager<T> {
  private cache = new Map<string, CacheEntry<T>>();
  private expirationMs: number;

  constructor(expirationMs = DEFAULT_CACHE_EXPIRATION_MS) {
    this.expirationMs = expirationMs;
  }

  /**
   * Get a value from the cache
   * @param key The cache key
   * @returns The cached value or undefined if not found or expired
   */
  get(key: string): T | undefined {
    const entry = this.cache.get(key);
    const now = Date.now();

    if (entry && now - entry.timestamp < this.expirationMs) {
      return entry.data;
    }

    // Automatically remove expired entries
    if (entry) {
      this.cache.delete(key);
    }

    return undefined;
  }

  /**
   * Store a value in the cache
   * @param key The cache key
   * @param data The data to cache
   */
  set(key: string, data: T): void {
    this.cache.set(key, {
      timestamp: Date.now(),
      data,
    });
  }

  /**
   * Clear specific entries from the cache
   * @param keyPrefix An optional prefix to clear only matching keys
   */
  clear(keyPrefix?: string): void {
    if (keyPrefix) {
      // Clear keys with matching prefix
      const keysToDelete = Array.from(this.cache.keys()).filter((key) => key.startsWith(keyPrefix));
      keysToDelete.forEach((key) => this.cache.delete(key));
    } else {
      // Clear entire cache
      this.cache.clear();
    }
  }
}

// Document cache instance with document file structure
export interface DocumentCacheData {
  files: any[];
  count: number;
  source: string;
}

// Create a singleton cache instance for documents
export const documentCache = new CacheManager<DocumentCacheData>();

/**
 * Helper to create a document cache key
 * @param userId User ID
 * @param source Source of documents (personal or company)
 * @returns Cache key string
 */
export function createDocumentCacheKey(userId: string, source: "personal" | "company"): string {
  return `${userId}:${source}`;
}

/**
 * Clear the document cache for specific entries or entirely
 * @param userId Optional user ID to clear only that user's cache
 * @param source Optional source to clear only that specific source
 */
export function clearDocumentCache(userId?: string, source?: "personal" | "company"): void {
  if (userId && source) {
    // Clear specific cache entry
    documentCache.clear(`${userId}:${source}`);
  } else if (userId) {
    // Clear all entries for a user
    documentCache.clear(`${userId}:`);
  } else {
    // Clear entire cache
    documentCache.clear();
  }
}

/**
 * Helper to apply search filtering to document files
 * @param files Array of document files
 * @param search Search string to filter by
 * @returns Filtered array of files
 */
export function applyDocumentSearchFilter(files: any[], search: string): any[] {
  if (!search) return files;

  return files.filter((file) => {
    const searchTerm = search.toLowerCase();
    const fileName = (file.name || file.title || "").toLowerCase();

    // Use fuzzy search algorithm
    let i = 0; // Pointer for searchTerm
    let j = 0; // Pointer for fileName
    let searchMatch = false; // Assume no match until found

    while (i < searchTerm.length && j < fileName.length) {
      if (searchTerm[i] === fileName[j]) {
        i++;
      }
      j++;
    }

    if (i === searchTerm.length) {
      searchMatch = true;
    }

    return searchMatch;
  });
}
