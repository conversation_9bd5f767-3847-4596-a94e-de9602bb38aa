import Logo from "@/public/images/logo.webp";
import type { StaticImageData } from "next/image";

export const isProductionEnvironment = process.env.NODE_ENV === "production";
export const isDevelopmentEnvironment = process.env.NODE_ENV === "development";
export const isTestEnvironment = Boolean(process.env.PLAYWRIGHT_TEST_BASE_URL || process.env.TEST_RUN_ID);

export const guestRegex = /^guest-\d+$/;

// TOOL_IDS and ToolId type definition moved from client-metadata.ts
export const TOOL_IDS = {
  // Core document tools
  KNOWLEDGE_BASE: "knowledgeBase",
  LIST_DOCUMENTS: "listDocuments",
  CREATE_DOCUMENT: "createDocument",
  UPDATE_DOCUMENT: "updateDocument",
  REQUEST_SUGGESTIONS: "requestSuggestions",

  // Integration tools
  N8N_WORKFLOW: "n8nWorkflow",
  JIRA: "jira",
  GOOGLE_SHEETS: "googleSheets",

  // Utility tools
  GET_WEATHER: "getWeather",
  MEMORY: "memory",

  // PO Assistant specialized tools
  // CREATE_BACKLOG: "createBacklog",
  // JIRA_STORIES: "jiraStories",
} as const;

export type ToolId = (typeof TOOL_IDS)[keyof typeof TOOL_IDS];

// Built-in tools that have special UI handling (used in message.tsx)
export const BUILT_IN_TOOLS = [
  TOOL_IDS.GET_WEATHER,
  TOOL_IDS.KNOWLEDGE_BASE,
  TOOL_IDS.N8N_WORKFLOW,
  TOOL_IDS.LIST_DOCUMENTS,
  TOOL_IDS.CREATE_DOCUMENT,
  TOOL_IDS.UPDATE_DOCUMENT,
  TOOL_IDS.REQUEST_SUGGESTIONS,
  TOOL_IDS.JIRA,
] as const;

// Tools that show skeleton loading state
export const SKELETON_LOADING_TOOLS = [TOOL_IDS.GET_WEATHER, TOOL_IDS.KNOWLEDGE_BASE, TOOL_IDS.N8N_WORKFLOW, TOOL_IDS.LIST_DOCUMENTS] as const;

// Tool configuration types (client-safe, without MCP dependencies)
export interface ToolConfig {
  builtInTools: ToolId[];
  mcpTools: string[]; // Use string instead of McpId to avoid MCP imports
}

export type Workspace = {
  id: string;
  label: string;
  icon: React.ElementType | StaticImageData;
  color: string;
};

export const WORKSPACE_IDS = {
  DEFAULT: "default_workspace",
} as const;

export type WorkspaceId = (typeof WORKSPACE_IDS)[keyof typeof WORKSPACE_IDS];

export const WORKSPACES: Record<WorkspaceId, Workspace> = {
  [WORKSPACE_IDS.DEFAULT]: {
    id: WORKSPACE_IDS.DEFAULT,
    label: "BraveBits",
    icon: Logo,
    color: "zinc",
  },
  // N8N_ASSISTANT: {
  //   id: "n8n-assistant",
  //   label: "n8n Assistant",
  //   icon: Workflow,
  //   color: "orange",
  // },
  // PAGEFLY: {
  //   id: "pagefly",
  //   label: "Pagefly",
  //   icon: ArrowUpSquare,
  //   color: "purple",
  // },
  // ECOMATE: {
  //   id: "ecomate",
  //   label: "EcoMate",
  //   icon: PenSquare,
  //   color: "green",
  // },
  // SHT: {
  //   id: "sht",
  //   label: "SHT",
  //   icon: ShoppingBag,
  //   color: "blue",
  // },
};

export const MODES = {
  DEFAULT: "default_mode",
  PO: "po_mode",
};
export type Mode = (typeof MODES)[keyof typeof MODES];

// Workspace-specific tool configurations
export const WORKSPACE_TOOL_CONFIGS: Record<string, ToolConfig> = {
  [WORKSPACE_IDS.DEFAULT]: {
    builtInTools: [TOOL_IDS.KNOWLEDGE_BASE, TOOL_IDS.MEMORY, TOOL_IDS.LIST_DOCUMENTS, TOOL_IDS.JIRA, TOOL_IDS.GOOGLE_SHEETS],
    mcpTools: [],
  },
  // [WORKSPACES.N8N_ASSISTANT.id]: {
  //   builtInTools: [TOOL_IDS.N8N_WORKFLOW, TOOL_IDS.MEMORY, TOOL_IDS.LIST_DOCUMENTS],
  //   mcpTools: [MCP_IDS.WORKFLOW],
  // },
  // [WORKSPACES.PAGEFLY.id]: {
  //   builtInTools: [TOOL_IDS.KNOWLEDGE_BASE, TOOL_IDS.MEMORY, TOOL_IDS.LIST_DOCUMENTS, TOOL_IDS.JIRA],
  //   mcpTools: [],
  // },
  // [WORKSPACES.ECOMATE.id]: {
  //   builtInTools: [TOOL_IDS.KNOWLEDGE_BASE, TOOL_IDS.MEMORY, TOOL_IDS.LIST_DOCUMENTS, TOOL_IDS.JIRA],
  //   mcpTools: [],
  // },
};

// Mode-specific tool configurations (moved to constants-server.ts for server-only use)
// Client components should not directly access MCP configurations

// Utility functions for dynamic tool resolution (moved to constants-server.ts for server-only use)
// Client components should use basic tool configurations directly from WORKSPACE_TOOL_CONFIGS

export const getAllAvailableToolIds = (): ToolId[] => {
  return Object.values(TOOL_IDS);
};
