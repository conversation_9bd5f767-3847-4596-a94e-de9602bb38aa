import { embedding<PERSON>odel } from "@/lib/ai/models";
import { embed } from "ai";
import {
  qdrantClient,
  QDRANT_CONFIG,
  type QdrantDocumentPayload,
  ensureCollectionExists,
} from "./qdrant-client";

// Cache for collection existence to avoid repeated checks
let collectionExistsCache: boolean | null = null;

// Simple LRU cache for embeddings (in-memory)
const embeddingCache = new Map<string, number[]>();
const MAX_CACHE_SIZE = 100; // Limit memory usage

// Simple cache for search results (short TTL to ensure freshness)
const searchResultCache = new Map<
  string,
  { results: QdrantSearchResult[]; timestamp: number }
>();
const SEARCH_CACHE_TTL = 5 * 60 * 1000; // 5 minutes cache
const MAX_SEARCH_CACHE_SIZE = 50;

export const generateEmbedding = async (value: string): Promise<number[]> => {
  // Check cache first
  const cacheKey = value.trim();
  if (embeddingCache.has(cacheKey)) {
    console.log("🎯 Embedding cache HIT:", cacheKey.substring(0, 50));
    return embeddingCache.get(cacheKey)!;
  }

  console.log("generateEmbedding (Qdrant)", value);
  const input = value.replaceAll("\\n", " ");
  const { embedding } = await embed({
    model: embeddingModel,
    value: input,
  });

  // Cache the result with LRU eviction
  if (embeddingCache.size >= MAX_CACHE_SIZE) {
    // Remove oldest entry (first key)
    const firstKey = embeddingCache.keys().next().value;
    if (firstKey) {
      embeddingCache.delete(firstKey);
    }
  }
  embeddingCache.set(cacheKey, embedding);
  console.log(`💾 Embedding cached (${embeddingCache.size}/${MAX_CACHE_SIZE})`);

  return embedding;
};

export interface QdrantSearchResult {
  documents: {
    id: number;
    content: string | null;
    metadata: Record<string, any> | null;
    file_id: string | null;
  };
  similarity: number;
}

export const findRelevantContent = async (
  userQuery: string
): Promise<QdrantSearchResult[]> => {
  try {
    // Check search cache first
    const queryCacheKey = userQuery.trim().toLowerCase();
    const cachedSearch = searchResultCache.get(queryCacheKey);
    if (
      cachedSearch &&
      Date.now() - cachedSearch.timestamp < SEARCH_CACHE_TTL
    ) {
      console.log("🎯 Search cache HIT:", queryCacheKey.substring(0, 50));
      return cachedSearch.results;
    }

    // Cached collection existence check - only check once per server restart
    if (collectionExistsCache === null) {
      console.log("🔄 First-time collection check...");
      await ensureCollectionExists();
      collectionExistsCache = true;
      console.log("✅ Collection existence cached");
    }

    // Generate embedding for the user query
    const embeddingStart = performance.now();
    const userQueryEmbedded = await generateEmbedding(userQuery);
    console.log(
      `🧠 Embedding: ${(performance.now() - embeddingStart).toFixed(2)}ms`
    );

    // Search for similar vectors in Qdrant - REDUCED LIMIT for performance
    const searchStart = performance.now();
    const searchResult = await qdrantClient.search(
      QDRANT_CONFIG.collectionName,
      {
        vector: userQueryEmbedded,
        limit: 100,
        score_threshold: 0.5, // 🎯 REDUCED from 0.7 to avoid 0 results (causing frontend errors)
        with_payload: true,
      }
    );
    console.log(`🔍 Search: ${(performance.now() - searchStart).toFixed(2)}ms`);

    // Transform results to match the expected format
    const results: QdrantSearchResult[] = searchResult.map((result) => {
      const payload = result.payload as unknown as QdrantDocumentPayload;
      return {
        documents: {
          id: payload.id,
          content: payload.content,
          metadata: payload.metadata,
          file_id: payload.file_id || null,
        },
        similarity: result.score || 0,
      };
    });

    // Cache the search results with TTL and LRU eviction
    if (searchResultCache.size >= MAX_SEARCH_CACHE_SIZE) {
      // Remove oldest entry (first key)
      const firstKey = searchResultCache.keys().next().value;
      if (firstKey) {
        searchResultCache.delete(firstKey);
      }
    }
    searchResultCache.set(queryCacheKey, {
      results,
      timestamp: Date.now(),
    });
    console.log(`💾 Results cached`);

    console.log("Qdrant search results count:", results.length);
    return results;
  } catch (error) {
    console.error("Error searching in Qdrant:", error);
    // Return empty results on error to maintain API compatibility
    return [];
  }
};

// Function to add a document to Qdrant
export const addDocumentToQdrant = async (
  id: number,
  content: string,
  metadata: Record<string, any>,
  file_id?: string
): Promise<void> => {
  try {
    // Use cached collection check
    if (collectionExistsCache === null) {
      await ensureCollectionExists();
      collectionExistsCache = true;
    }

    // Generate embedding for the content
    const embedding = await generateEmbedding(content);

    // Prepare payload
    const payload: QdrantDocumentPayload = {
      id,
      content,
      metadata,
      file_id,
    };

    // Insert into Qdrant - REMOVE wait: true for async performance
    await qdrantClient.upsert(QDRANT_CONFIG.collectionName, {
      wait: false, // 🚀 ASYNC upsert for better performance
      points: [
        {
          id: id.toString(), // Qdrant uses string IDs
          vector: embedding,
          payload,
        },
      ],
    });

    console.log(`Document ${id} added to Qdrant successfully`);
  } catch (error) {
    console.error(`Error adding document ${id} to Qdrant:`, error);
    throw error;
  }
};

// Function to remove a document from Qdrant
export const removeDocumentFromQdrant = async (id: number): Promise<void> => {
  try {
    await qdrantClient.delete(QDRANT_CONFIG.collectionName, {
      wait: false, // 🚀 ASYNC delete for better performance
      points: [id.toString()],
    });

    console.log(`Document ${id} removed from Qdrant successfully`);
  } catch (error) {
    console.error(`Error removing document ${id} from Qdrant:`, error);
    throw error;
  }
};

// Function to update a document in Qdrant
export const updateDocumentInQdrant = async (
  id: number,
  content: string,
  metadata: Record<string, any>,
  file_id?: string
): Promise<void> => {
  try {
    // Generate new embedding for updated content
    const embedding = await generateEmbedding(content);

    // Prepare updated payload
    const payload: QdrantDocumentPayload = {
      id,
      content,
      metadata,
      file_id,
    };

    // Update in Qdrant (upsert will replace existing) - ASYNC
    await qdrantClient.upsert(QDRANT_CONFIG.collectionName, {
      wait: false, // 🚀 ASYNC upsert for better performance
      points: [
        {
          id: id.toString(),
          vector: embedding,
          payload,
        },
      ],
    });

    console.log(`Document ${id} updated in Qdrant successfully`);
  } catch (error) {
    console.error(`Error updating document ${id} in Qdrant:`, error);
    throw error;
  }
};
