import { db } from "@/lib/db";
import { documents } from "@/lib/db/schema";
import { addDocumentToQdrant, updateDocumentInQdrant, removeDocumentFromQdrant } from "./embedding-qdrant";
import { generateEmbedding } from "./embedding";
import { eq } from "drizzle-orm";

// Feature flag to determine which vector store to use
const USE_QDRANT = true; // process.env.USE_QDRANT === "true";

export interface DocumentIngestionData {
  id?: number; // Optional for new documents, required for updates
  content: string;
  metadata: Record<string, any>;
  file_id?: string;
}

/**
 * Add a new document to the vector store (PostgreSQL or Qdrant)
 */
export async function ingestDocument(data: DocumentIngestionData): Promise<number> {
  const { content, metadata, file_id } = data;

  if (!content || content.trim().length === 0) {
    throw new Error("Document content cannot be empty");
  }

  try {
    if (USE_QDRANT) {
      // For Qdrant, we need to generate an ID first
      const result = await db
        .insert(documents)
        .values({
          content,
          metadata,
          file_id,
          embedding: null, // We'll handle embedding in Qdrant
        })
        .returning({ id: documents.id });

      const documentId = Number(result[0].id);

      // Add to Qdrant with embedding
      await addDocumentToQdrant(documentId, content, metadata, file_id);

      console.log(`Document ${documentId} ingested to Qdrant successfully`);
      return documentId;
    } else {
      // Legacy PostgreSQL with pgvector
      const embedding = await generateEmbedding(content);

      const result = await db
        .insert(documents)
        .values({
          content,
          metadata,
          file_id,
          embedding,
        })
        .returning({ id: documents.id });

      const documentId = Number(result[0].id);
      console.log(`Document ${documentId} ingested to PostgreSQL successfully`);
      return documentId;
    }
  } catch (error) {
    console.error("Error ingesting document:", error);
    throw error;
  }
}

/**
 * Update an existing document in the vector store
 */
export async function updateDocument(data: DocumentIngestionData): Promise<void> {
  const { id, content, metadata, file_id } = data;

  if (!id) {
    throw new Error("Document ID is required for updates");
  }

  if (!content || content.trim().length === 0) {
    throw new Error("Document content cannot be empty");
  }

  try {
    if (USE_QDRANT) {
      // Update in PostgreSQL (without embedding)
      await db
        .update(documents)
        .set({
          content,
          metadata,
          file_id,
        })
        .where(eq(documents.id, BigInt(id)));

      // Update in Qdrant with new embedding
      await updateDocumentInQdrant(id, content, metadata, file_id);

      console.log(`Document ${id} updated in Qdrant successfully`);
    } else {
      // Legacy PostgreSQL with pgvector
      const embedding = await generateEmbedding(content);

      await db
        .update(documents)
        .set({
          content,
          metadata,
          file_id,
          embedding,
        })
        .where(eq(documents.id, BigInt(id)));

      console.log(`Document ${id} updated in PostgreSQL successfully`);
    }
  } catch (error) {
    console.error(`Error updating document ${id}:`, error);
    throw error;
  }
}

/**
 * Remove a document from the vector store
 */
export async function removeDocument(id: number): Promise<void> {
  try {
    if (USE_QDRANT) {
      // Remove from Qdrant first
      await removeDocumentFromQdrant(id);

      // Then remove from PostgreSQL
      await db.delete(documents).where(eq(documents.id, BigInt(id)));

      console.log(`Document ${id} removed from Qdrant and PostgreSQL successfully`);
    } else {
      // Legacy PostgreSQL only
      await db.delete(documents).where(eq(documents.id, BigInt(id)));

      console.log(`Document ${id} removed from PostgreSQL successfully`);
    }
  } catch (error) {
    console.error(`Error removing document ${id}:`, error);
    throw error;
  }
}

/**
 * Batch ingest multiple documents
 */
export async function batchIngestDocuments(documents: DocumentIngestionData[]): Promise<number[]> {
  const results: number[] = [];

  for (const doc of documents) {
    try {
      const id = await ingestDocument(doc);
      results.push(id);
    } catch (error) {
      console.error("Error in batch ingestion:", error);
      // Continue with other documents even if one fails
    }
  }

  return results;
}

/**
 * Extract text content from different file types
 * This is a basic implementation - you might want to use specialized libraries
 * for different file types (pdf-parse, mammoth for docx, etc.)
 */
export function extractTextContent(file: File, content: string | ArrayBuffer): string {
  const fileType = file.type.toLowerCase();

  if (fileType.includes("text/") || fileType.includes("application/json")) {
    return typeof content === "string" ? content : new TextDecoder().decode(content);
  }

  if (fileType.includes("application/pdf")) {
    // TODO: Implement PDF text extraction using pdf-parse or similar
    console.warn("PDF text extraction not implemented yet");
    return `[PDF File: ${file.name}]`;
  }

  if (fileType.includes("application/vnd.openxmlformats-officedocument.wordprocessingml.document")) {
    // TODO: Implement DOCX text extraction using mammoth or similar
    console.warn("DOCX text extraction not implemented yet");
    return `[DOCX File: ${file.name}]`;
  }

  // For other file types, return a placeholder
  return `[File: ${file.name} (${file.type})]`;
}

/**
 * Process uploaded file for document ingestion
 */
export async function processFileForIngestion(
  file: File,
  fileContent: string | ArrayBuffer,
  additionalMetadata: Record<string, any> = {}
): Promise<number> {
  try {
    // Extract text content from file
    const textContent = extractTextContent(file, fileContent);

    // Prepare metadata
    const metadata = {
      fileName: file.name,
      fileType: file.type,
      fileSize: file.size,
      uploadedAt: new Date().toISOString(),
      ...additionalMetadata,
    };

    // Ingest the document
    const documentId = await ingestDocument({
      content: textContent,
      metadata,
      file_id: additionalMetadata.file_id,
    });

    console.log(`File ${file.name} processed and ingested as document ${documentId}`);
    return documentId;
  } catch (error) {
    console.error(`Error processing file ${file.name} for ingestion:`, error);
    throw error;
  }
}
