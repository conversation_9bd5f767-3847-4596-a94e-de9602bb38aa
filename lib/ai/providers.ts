import { createGoogleGenerativeAI } from "@ai-sdk/google";
import type { ImageModel } from "ai";
import { isTestEnvironment } from "../constants";
import { artifactModel, chatModel, reasoningModel, titleModel } from "./models.test";
import { perplexity } from "@ai-sdk/perplexity";

const google = createGoogleGenerativeAI({
  apiKey: process.env.GOOGLE_GENERATIVE_AI_API_KEY,
  baseURL: "https://gateway.helicone.ai/v1beta",
  headers: {
    "Helicone-Auth": `Bearer ${process.env.HELICONE_API_KEY}`,
    "Helicone-Target-URL": "https://generativelanguage.googleapis.com",
  },
});

export const myProvider = isTestEnvironment
  ? {
      languageModel: (id: string) => {
        switch (id) {
          case "chat-model":
            return chatModel;
          case "chat-model-reasoning":
            return reasoningModel;
          case "title-model":
            return titleModel;
          case "artifact-model":
            return artifactModel;
          default:
            throw new Error(`Unknown model: ${id}`);
        }
      },
      imageModel: (_id: string): ImageModel => {
        throw new Error(`Image model not available in test environment: ${_id}`);
      },
    }
  : {
      languageModel: (id: string) => {
        switch (id) {
          case "deep-research":
            return perplexity("sonar-pro");
          case "chat-model":
            return google("gemini-2.0-flash", {
              // cachedContent: "always",
              // useSearchGrounding: true,
            });
          case "chat-model-reasoning":
            return google("gemini-2.0-flash-thinking-exp-01-21");
          case "title-model":
            return google("gemini-2.0-flash", {
              // cachedContent: "always",
              // useSearchGrounding: true,
            });
          case "artifact-model":
            return google("gemini-2.0-flash", {
              // cachedContent: "always",
              // useSearchGrounding: true,
            });
          default:
            throw new Error(`Unknown model: ${id}`);
        }
      },
      imageModel: (id: string): ImageModel => {
        try {
          // Gemini 2.0 Flash supports image generation
          const modelAny = google(id) as any;
          modelAny.maxImagesPerCall = 1;
          return modelAny as ImageModel;
        } catch (error) {
          console.error("Error initializing image model:", error);
          throw new Error(`Image model not available: ${id}`);
        }
      },
    };
