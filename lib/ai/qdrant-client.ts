import { QdrantClient } from "@qdrant/qdrant-js";

/**
 * Qdrant Vector Database Client Configuration
 *
 * Required Environment Variables:
 * - QDRANT_URL: Qdrant server URL (default: http://localhost:6333)
 * - QDRANT_API_KEY: API key for Qdrant cloud (optional for local)
 * - QDRANT_COLLECTION_NAME: Collection name (default: documents)
 *
 * For local development:
 * 1. Install Qdrant: docker run -p 6333:6333 qdrant/qdrant
 * 2. Set QDRANT_URL=http://localhost:6333 in your .env.local
 *
 * For production:
 * 1. Create a Qdrant cloud cluster
 * 2. Set QDRANT_URL to your cluster URL
 * 3. Set QDRANT_API_KEY to your cluster API key
 */

// Qdrant configuration
export const QDRANT_CONFIG = {
  url: process.env.QDRANT_URL || "http://localhost:6333",
  apiKey: process.env.QDRANT_API_KEY,
  collectionName: process.env.QDRANT_COLLECTION_NAME || "documents",
  vectorSize: 768, // Must match the embedding model dimension
  metricType: "Cosine" as const,
  replicas: 1,
  shards: 1,
} as const;

// Initialize Qdrant client
export const qdrantClient = new QdrantClient({
  url: QDRANT_CONFIG.url,
  apiKey: QDRANT_CONFIG.apiKey,
});

// Collection schema for documents
export interface QdrantDocumentPayload {
  [key: string]: unknown;
  id: number;
  content: string;
  metadata: Record<string, any>;
  file_id?: string;
}

// Ensure collection exists with proper configuration
export async function ensureCollectionExists(): Promise<void> {
  try {
    const collections = await qdrantClient.getCollections();
    const collectionExists = collections.collections.some((col) => col.name === QDRANT_CONFIG.collectionName);

    if (!collectionExists) {
      console.log(`Creating collection: ${QDRANT_CONFIG.collectionName}`);
      await qdrantClient.createCollection(QDRANT_CONFIG.collectionName, {
        vectors: {
          size: QDRANT_CONFIG.vectorSize,
          distance: QDRANT_CONFIG.metricType,
        },
        shard_number: QDRANT_CONFIG.shards,
        replication_factor: QDRANT_CONFIG.replicas,
        optimizers_config: {
          deleted_threshold: 0.2,
          vacuum_min_vector_number: 1000,
          default_segment_number: 0,
          max_segment_size: 200000,
          memmap_threshold: 50000,
          indexing_threshold: 5000,
          flush_interval_sec: 3,
          max_optimization_threads: 2,
        },
        hnsw_config: {
          m: 16,
          ef_construct: 100,
        },
        quantization_config: {
          scalar: {
            type: "int8",
            quantile: 0.99,
            always_ram: true,
          },
        },
        on_disk_payload: false,
      });
      console.log(`Collection ${QDRANT_CONFIG.collectionName} created successfully`);
    } else {
      console.log(`Collection ${QDRANT_CONFIG.collectionName} already exists`);
    }
  } catch (error) {
    console.error("Error ensuring collection exists:", error);
    throw error;
  }
}

// Health check function
export async function checkQdrantHealth(): Promise<boolean> {
  try {
    // Test connectivity by getting collections
    await qdrantClient.getCollections();
    console.log("Qdrant health check: OK");
    return true;
  } catch (error) {
    console.error("Qdrant health check failed:", error);
    return false;
  }
}
