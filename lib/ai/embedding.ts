import { embedding<PERSON>odel } from "@/lib/ai/models";
import { embed } from "ai";

// Import Qdrant implementation
import { findRelevantContent as findRelevantContentQdrant, generateEmbedding as generateEmbeddingQdrant } from "./embedding-qdrant";

// Feature flag to switch between PostgreSQL and Qdrant
const USE_QDRANT = true;

console.log(`RAG Vector Store: ${USE_QDRANT ? "Qdrant" : "PostgreSQL (pgvector)"}`);

if (USE_QDRANT) {
  console.log("✅ Using Qdrant for vector embeddings");
} else {
  console.log("⚠️ Using legacy PostgreSQL with pgvector. Consider migrating to Qdrant for better performance.");
}

export const generateEmbedding = async (value: string): Promise<number[]> => {
  if (USE_QDRANT) {
    return generateEmbeddingQdrant(value);
  }

  // Legacy PostgreSQL implementation
  console.log("generateEmbedding (PostgreSQL)", value);
  const input = value.replaceAll("\\n", " ");
  const { embedding } = await embed({
    model: embeddingModel,
    value: input,
  });
  return embedding;
};

export const findRelevantContent = async (userQuery: string) => {
  if (USE_QDRANT) {
    return findRelevantContentQdrant(userQuery);
  }

  // Legacy PostgreSQL implementation - dynamically import to avoid loading when not needed
  // console.log("findRelevantContent (PostgreSQL)", userQuery);

  // Dynamic imports for PostgreSQL dependencies (only loaded when USE_QDRANT=false)
  // const { db } = await import("@/lib/db");
  // const { documents } = await import("@/lib/db/schema");
  // const { cosineDistance, desc, gt, sql } = await import("drizzle-orm");

  // const userQueryEmbedded = await generateEmbedding(userQuery);

  // try {
  //   const searchResult = await qdrantClient.search(QDRANT_COLLECTION_NAME, {
  //     vector: userQueryEmbedded,
  //     limit: 4, // Same limit as before
  //     // with_payload: true, // To retrieve the original document content
  //     // score_threshold: 0.5 // Optional: similar to your previous gt(similarity, 0.5)
  //   });

  //   // console.log("Qdrant searchResult", searchResult);

  //   // Transform Qdrant results to a format similar to the previous one,
  //   // assuming the payload contains the original document fields.
  //   // You'll need to adjust this based on how you structure your data in Qdrant's payload.
  //   const serializableResults = searchResult.map((item) => ({
  //     // Assuming 'payload' contains the fields of your original 'documents' table
  //     // and 'id' in Qdrant corresponds to your original document ID.
  //     // Adjust the payload access (e.g., item.payload?.text, item.payload?.source)
  //     // based on your actual payload structure in Qdrant.
  //     documents: {
  //       id: typeof item.id === "string" ? item.id : Number(item.id), // Qdrant ID can be string or number
  //       ...(item.payload as object), // Spread the payload
  //     },
  //     similarity: item.score, // Qdrant score is the similarity
  //   }));

  //   return serializableResults;
  // } catch (error) {
  //   console.error("Error searching with Qdrant:", error);
  //   // It's good practice to decide how to handle errors,
  //   // e.g., return empty array or throw the error
  //   return [];
  // }
};
