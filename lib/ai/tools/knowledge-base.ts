import { findRelevantContent } from "@/lib/ai/embedding";
import { tool } from "ai";
import { z } from "zod";

export const knowledgeBaseTool = tool({
  description: `Search and retrieve information from the company's knowledge base to answer user questions. 
  Use this tool when the user asks questions that might be answered by internal documents, policies, or company information.
  Always provide a clear, specific question to search for.`,
  parameters: z.object({
    question: z
      .string()
      .min(1, "Question cannot be empty")
      .describe("The specific question or search query to find relevant information from the knowledge base. Be as specific as possible."),
  }),
  execute: async ({ question }) => {
    console.log("API POST /chat: knowledgeBase tool invoked with question:", question);

    // Validate question parameter
    if (!question || typeof question !== "string" || question.trim().length === 0) {
      console.error("API POST /chat: knowledgeBase tool called with invalid question parameter:", question);
      return [];
    }

    const trimmedQuestion = question.trim();
    console.log("API POST /chat: Searching knowledge base for:", trimmedQuestion);

    try {
      const searchResults = await findRelevantContent(trimmedQuestion);
      console.log("API POST /chat: knowledgeBase results count:", searchResults?.length || 0);

      if (searchResults && searchResults.length > 0) {
        return searchResults;
      }

      // Return empty array when no results found
      console.log("API POST /chat: No relevant information found in knowledge base");
      return [];
    } catch (error) {
      console.error("API POST /chat: Error in knowledgeBase tool:", error);
      // Return empty array on error as well
      return [];
    }
  },
});
