import { tool } from "ai";
import { z } from "zod";
import { Version3Client } from "jira.js";

// Get Jira configuration from environment variables (server-side only)
const getJiraConfig = () => ({
  host: process.env.JIRA_BASE_URL,
  email: process.env.JIRA_ADMIN_EMAIL,
  apiToken: process.env.JIRA_API_TOKEN,
});

// Create a function to fetch Jira tasks for a user
async function fetchJiraTasks({ email }: { email?: string; jiraId?: string }) {
  if (!email) {
    return [];
  }

  const { host, email: jiraEmail, apiToken } = getJiraConfig();

  if (!host || !jiraEmail || !apiToken) {
    throw new Error("Jira configuration is incomplete");
  }

  // Create a Jira client
  const client = new Version3Client({
    host,
    authentication: {
      basic: {
        email: jiraEmail,
        apiToken,
      },
    },
  });

  try {
    // Search for issues assigned to the email using JQL with enhanced fields
    const jql = `assignee = "${email}" ORDER BY updated DESC`;
    const searchResult = await client.issueSearch.searchForIssuesUsingJqlPost({
      jql,
      fields: ["summary", "status", "priority", "duedate", "project", "description", "created", "updated"],
    });

    if (!searchResult.issues || searchResult.issues.length === 0) {
      return [];
    }

    // Format the results with enhanced information
    return searchResult.issues.map((issue: any) => ({
      id: issue.id,
      key: issue.key,
      title: issue.fields.summary,
      status: issue.fields.status
        ? {
            id: issue.fields.status.id,
            name: issue.fields.status.name,
          }
        : null,
      priority: issue.fields.priority
        ? {
            id: issue.fields.priority.id,
            name: issue.fields.priority.name,
          }
        : null,
      dueDate: issue.fields.duedate,
      project: issue.fields.project
        ? {
            key: issue.fields.project.key,
            name: issue.fields.project.name,
          }
        : null,
      description: issue.fields.description,
      created: issue.fields.created,
      updated: issue.fields.updated,
    }));
  } catch (error) {
    console.error("Error fetching Jira tasks:", error);
    throw new Error("Failed to fetch Jira tasks");
  }
}

// Function to fetch detailed information about a specific ticket
async function fetchTicketDetail(ticketKey: string) {
  if (!ticketKey) {
    return null;
  }

  const { host, email: jiraEmail, apiToken } = getJiraConfig();

  if (!host || !jiraEmail || !apiToken) {
    throw new Error("Jira configuration is incomplete");
  }

  // Create a Jira client
  const client = new Version3Client({
    host,
    authentication: {
      basic: {
        email: jiraEmail,
        apiToken,
      },
    },
  });

  try {
    // Get complete ticket details
    const issueDetails = await client.issues.getIssue({
      issueIdOrKey: ticketKey,
      fields: [
        "summary",
        "status",
        "priority",
        "duedate",
        "project",
        "description",
        "creator",
        "assignee",
        "created",
        "updated",
        "issuetype",
        "labels",
        "components",
      ],
    });

    // Get comments in a separate request
    const comments = await client.issueComments.getComments({
      issueIdOrKey: ticketKey,
    });

    // Return comprehensive ticket details
    return {
      id: issueDetails.id,
      key: issueDetails.key,
      title: issueDetails.fields.summary,
      status: issueDetails.fields.status
        ? {
            name: issueDetails.fields.status.name,
            category: issueDetails.fields.status.statusCategory?.name,
          }
        : null,
      priority: issueDetails.fields.priority
        ? {
            name: issueDetails.fields.priority.name,
          }
        : null,
      dueDate: issueDetails.fields.duedate,
      project: issueDetails.fields.project
        ? {
            key: issueDetails.fields.project.key,
            name: issueDetails.fields.project.name,
          }
        : null,
      description: issueDetails.fields.description,
      created: issueDetails.fields.created,
      updated: issueDetails.fields.updated,
      issueType: issueDetails.fields.issuetype
        ? {
            name: issueDetails.fields.issuetype.name,
          }
        : null,
      creator: issueDetails.fields.creator
        ? {
            displayName: issueDetails.fields.creator.displayName,
            emailAddress: issueDetails.fields.creator.emailAddress,
          }
        : null,
      assignee: issueDetails.fields.assignee
        ? {
            displayName: issueDetails.fields.assignee.displayName,
            emailAddress: issueDetails.fields.assignee.emailAddress,
          }
        : null,
      labels: issueDetails.fields.labels || [],
      components: (issueDetails.fields.components || []).map((c: any) => c.name),
      comments: (comments.comments || []).map((comment: any) => ({
        id: comment.id,
        author: comment.author ? comment.author.displayName : "Unknown",
        body: comment.body,
        created: comment.created,
      })),
    };
  } catch (error) {
    console.error("Error fetching ticket details:", error);
    return null;
  }
}

// Helper function to check if text contains a Jira ticket mention in the format [JIRA:XXX-123]
export function containsJiraTicketMention(text: string): boolean {
  const jiraTicketRegex = /\[JIRA:([A-Z]+-\d+)\]/g;
  return jiraTicketRegex.test(text);
}

// Helper function to extract ticket keys from text
export function extractJiraTicketKeys(text: string): string[] {
  const jiraTicketRegex = /\[JIRA:([A-Z]+-\d+)\]/g;
  const matches = Array.from(text.matchAll(jiraTicketRegex));
  return matches.map((match) => match[1]);
}

// Helper function to get ticket details for AI context
export async function getJiraTicketDetails(ticketKey: string): Promise<string> {
  try {
    const ticketDetail = await fetchTicketDetail(ticketKey);

    if (!ticketDetail) {
      return `Ticket ${ticketKey} not found.`;
    }

    // Format ticket details for AI consumption
    const details = [
      `Ticket: ${ticketKey} - ${ticketDetail.title}`,
      `Status: ${ticketDetail.status?.name || "Unknown"}`,
      `Priority: ${ticketDetail.priority?.name || "Unknown"}`,
      `Project: ${ticketDetail.project?.name || "Unknown"}`,
      `Description: ${ticketDetail.description?.replace(/<[^>]*>?/gm, "") || "No description provided."}`,
    ];

    // Add comments if available
    if (ticketDetail.comments && ticketDetail.comments.length > 0) {
      details.push("\nComments:");
      ticketDetail.comments.slice(0, 3).forEach((comment: any, index: number) => {
        details.push(
          `  ${index + 1}. ${comment.author}: ${comment.body?.replace(/<[^>]*>?/gm, "").slice(0, 100)}${comment.body?.length > 100 ? "..." : ""}`
        );
      });

      if (ticketDetail.comments.length > 3) {
        details.push(`  (${ticketDetail.comments.length - 3} more comments)`);
      }
    }

    return details.join("\n");
  } catch (error) {
    console.error("Error fetching Jira ticket details:", error);
    return `Error fetching details for ticket ${ticketKey}.`;
  }
}

// Main tool with enhanced functionality
export const jiraTool = tool({
  description: "Get Jira tickets assigned to a user or get details about a specific ticket.",
  parameters: z.object({
    email: z.string().optional().describe("User email to find assigned tickets"),
    jiraId: z.string().optional().describe("User jira id (alternative to email)"),
    ticketKey: z.string().optional().describe("Specific ticket key to get detailed information about (e.g., 'PROJ-123')"),
  }),
  execute: async ({ email, jiraId, ticketKey }) => {
    // If ticketKey is provided, get detailed information about that specific ticket
    if (ticketKey) {
      const ticketDetail = await fetchTicketDetail(ticketKey);
      if (!ticketDetail) {
        throw new Error(`Could not find ticket with key ${ticketKey}`);
      }
      return ticketDetail;
    }

    // Otherwise, get list of tickets assigned to the user
    const tasks = await fetchJiraTasks({ email, jiraId });
    return tasks;
  },
});
