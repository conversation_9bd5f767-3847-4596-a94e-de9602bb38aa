import { tool } from "ai";
import { z } from "zod";
import { Version3Client } from "jira.js";
import { generateObject } from "ai";
import { myProvider } from "../providers";

// Schema for backlog items matching the n8n workflow structure
const BacklogItemSchema = z.object({
  title: z.string().describe("A short, clear title for the backlog item (in English)"),
  description: z.string().describe("A concise explanation of the feature (in English)"),
  criteria_scores: z.object({
    "Business Value": z.number().min(0).max(10).describe("Business value score (0-10)"),
    "User Impact": z.number().min(0).max(10).describe("User impact score (0-10)"),
    "Technical Complexity": z.number().min(0).max(10).describe("Technical complexity score (0-10, lower is better)"),
    "Customer Demand": z.number().min(0).max(10).describe("Customer demand score (0-10)"),
    "Market & Stakeholder Priority": z.number().min(0).max(10).describe("Market & stakeholder priority score (0-10)"),
    "Data Availability / Feasibility": z.number().min(0).max(10).describe("Data availability/feasibility score (0-10)"),
    "Innovation Level": z.number().min(0).max(10).describe("Innovation level score (0-10)"),
  }),
  final_score: z.number().min(0).max(10).describe("Normalized final score based on all criteria (0-10)"),
});

const BacklogResponseSchema = z.object({
  action: z.literal("create_backlog"),
  items: z.array(BacklogItemSchema).min(1).max(20).describe("Array of backlog items"),
});

// Initialize Jira client
function getJiraClient() {
  const jiraHost = process.env.JIRA_HOST || "https://bravebits.atlassian.net";
  const jiraEmail = process.env.JIRA_EMAIL;
  const jiraToken = process.env.JIRA_API_TOKEN;

  if (!jiraEmail || !jiraToken) {
    console.warn("Jira credentials not configured. Backlog will be generated without Jira integration.");
    return null;
  }

  return new Version3Client({
    host: jiraHost,
    authentication: {
      basic: {
        email: jiraEmail,
        apiToken: jiraToken,
      },
    },
  });
}

export const createBacklogTool = tool({
  description:
    "Generate a structured backlog with scored items based on the user's requirements. This tool creates a prioritized list of features with detailed scoring criteria and can optionally integrate with Jira to fetch project context.",
  parameters: z.object({
    requirements: z.string().min(10).describe("Detailed description of the product requirements, features needed, or problem to solve"),
    numItems: z.number().min(3).max(20).default(10).describe("Number of backlog items to generate (default: 10)"),
    userContext: z
      .object({
        projectId: z.string().optional(),
        project: z.string().optional(),
        jiraId: z.string().optional(),
      })
      .optional()
      .describe("User context from user verification"),
  }),
  execute: async ({ requirements, numItems = 10, userContext }) => {
    console.log("CreateBacklog tool invoked with:", {
      requirements: requirements.substring(0, 100),
      numItems,
      userContext,
    });

    try {
      // Initialize Jira client if credentials are available
      const jiraClient = getJiraClient();
      let jiraContext = "";

      // Get Jira project context if available
      if (jiraClient && userContext?.projectId) {
        try {
          const project = await jiraClient.projects.getProject({ projectIdOrKey: userContext.projectId });
          jiraContext = `\nJira Project Context: ${project.name} (${project.key}) - ${project.description || "No description"}`;
          console.log("Retrieved Jira project context:", project.key);
        } catch (error) {
          console.warn("Could not fetch Jira project context:", error);
        }
      }

      // Generate backlog items using AI
      const backlogPrompt = `
You are an expert Product Owner creating a backlog for a software project. 

Requirements: ${requirements}
${jiraContext}
${userContext?.project ? `Team/Project Context: ${userContext.project}` : ""}

Generate ${numItems} backlog items that address these requirements. Each item should:
1. Have a clear, actionable title
2. Include a concise description explaining the feature/functionality
3. Be scored on 7 criteria (0-10 scale):
   - Business Value: How much business value this delivers
   - User Impact: How much this affects user experience
   - Technical Complexity: Implementation difficulty (0=easy, 10=very complex)
   - Customer Demand: How much customers are asking for this
   - Market & Stakeholder Priority: Strategic importance
   - Data Availability/Feasibility: How feasible with current resources
   - Innovation Level: How innovative/cutting-edge this is

4. Calculate a final normalized score (0-10) based on all criteria, where:
   - Higher business value, user impact, customer demand, market priority, feasibility, and innovation increase the score
   - Higher technical complexity should decrease the score (since it's harder to implement)

Sort the items by final score (highest first). Focus on practical, implementable features that solve real problems.
`;

      const backlogResult = await generateObject({
        model: myProvider.languageModel("chat-model"),
        schema: z.object({
          items: z.array(BacklogItemSchema),
        }),
        prompt: backlogPrompt,
      });

      // Validate and structure the response
      const backlogResponse = {
        action: "create_backlog" as const,
        items: backlogResult.object.items.slice(0, numItems).map((item) => ({
          ...item,
          // Ensure final_score is calculated properly
          final_score: Number(calculateFinalScore(item.criteria_scores).toFixed(1)),
        })),
      };

      // Sort by final score descending
      backlogResponse.items.sort((a, b) => b.final_score - a.final_score);

      console.log(`Generated ${backlogResponse.items.length} backlog items with AI`);

      // Validate the response structure
      const validatedResponse = BacklogResponseSchema.parse(backlogResponse);

      return {
        ...validatedResponse,
        metadata: {
          generatedBy: "AI",
          userContext: userContext || null,
          jiraIntegration: !!jiraClient,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      console.error("Error in createBacklog tool:", error);

      // Fallback to simplified generation
      return {
        error: `Failed to create backlog: ${error instanceof Error ? error.message : "Unknown error"}`,
        action: "create_backlog" as const,
        items: [],
        fallback: true,
      };
    }
  },
});

// Helper function to calculate final score based on criteria
function calculateFinalScore(scores: {
  "Business Value": number;
  "User Impact": number;
  "Technical Complexity": number;
  "Customer Demand": number;
  "Market & Stakeholder Priority": number;
  "Data Availability / Feasibility": number;
  "Innovation Level": number;
}): number {
  // Weights for each criterion
  const weights = {
    "Business Value": 0.2,
    "User Impact": 0.2,
    "Technical Complexity": -0.15, // Negative because higher complexity is worse
    "Customer Demand": 0.15,
    "Market & Stakeholder Priority": 0.15,
    "Data Availability / Feasibility": 0.15,
    "Innovation Level": 0.1,
  };

  let weightedSum = 0;
  let totalWeight = 0;

  for (const [criterion, score] of Object.entries(scores)) {
    const weight = weights[criterion as keyof typeof weights];
    if (weight !== undefined) {
      weightedSum += score * Math.abs(weight);
      totalWeight += Math.abs(weight);
    }
  }

  // Add complexity penalty
  const complexityPenalty = (scores["Technical Complexity"] / 10) * 1.5;

  // Calculate final score (0-10 range)
  const rawScore = weightedSum / totalWeight;
  const finalScore = Math.max(0, Math.min(10, rawScore - complexityPenalty));

  return finalScore;
}
