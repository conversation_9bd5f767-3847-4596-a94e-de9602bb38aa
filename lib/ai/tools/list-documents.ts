import { tool } from "ai";
import { z } from "zod";
import { auth, currentUser } from "@clerk/nextjs/server";

interface FileMetadata {
  id: string;
  name: string;
  createdAt: string | Date;
  updatedAt?: string | Date | null;
  department?: string | null;
  fileType: string;
  owner?: string | null;
  path?: string | null;
  permissions?: string[] | null;
  url?: string | null;
  source: "company" | "personal";
  size?: number;
  isAiGenerated?: boolean;
}

// Mock implementations for now
async function getCompanyFiles({ email }: { email: string }) {
  console.log(`Getting company files for ${email}`);
  return [];
}

async function getPersonalFiles({ userId }: { userId: string }) {
  console.log(`Getting personal files for ${userId}`);
  return [];
}

function createDocumentCacheKey(userId: string, source: string) {
  return `${userId}-${source}`;
}

const documentCache = new Map();

function applyDocumentSearchFilter(files: FileMetadata[], search: string) {
  return files.filter((file) => file.name.toLowerCase().includes(search.toLowerCase()));
}

/**
 * Tool for listing documents that the current user has access to.
 */
async function listDocumentsImpl({
  source = "company",
  search = "",
  useCache = true,
}: {
  source?: "personal" | "company";
  search?: string;
  useCache?: boolean;
}) {
  try {
    console.log(`[TOOL] Fetching ${source} documents`);

    // Get current user info from Clerk
    const { userId } = await auth();
    const user = await currentUser();

    if (!userId || !user?.emailAddresses?.[0]?.emailAddress) {
      return {
        error: "Unauthorized. User must be logged in to list documents.",
        files: [],
        count: 0,
        source,
      };
    }

    const email = user.emailAddresses[0].emailAddress;

    // Create a cache key based on user ID and source
    const cacheKey = createDocumentCacheKey(userId, source);

    // Check cache if enabled
    if (useCache) {
      const cachedData = documentCache.get(cacheKey);

      if (cachedData) {
        console.log(`[TOOL] Using cached document list for ${source} (${cachedData.count} files)`);

        // Apply search filter to cached data
        if (search) {
          const filteredFiles = applyDocumentSearchFilter(cachedData.files, search);
          return {
            files: filteredFiles,
            count: filteredFiles.length,
            source,
            fromCache: true,
          };
        }

        return {
          ...cachedData,
          fromCache: true,
        };
      }
    }

    // No cache hit or cache disabled, fetch from database
    let files: FileMetadata[] = [];

    if (source === "company") {
      const companyFiles = await getCompanyFiles({ email });
      files = companyFiles.map((file: any) => ({
        id: file.id,
        name: file.name || "Unnamed",
        fileType: file.fileType || "unknown",
        createdAt: file.createdAt || new Date(),
        updatedAt: file.updatedAt,
        url: file.url,
        source: "company" as const,
        permissions: file.permissions,
        department: file.department,
        owner: file.owner,
        path: file.path,
      }));
    } else {
      const personalFiles = await getPersonalFiles({ userId });
      files = personalFiles.map((file: any) => ({
        id: file.id,
        name: file.title || "Unnamed",
        fileType: file.kind || "unknown",
        createdAt: file.createdAt || new Date(),
        source: "personal" as const,
        isAiGenerated: file.metadata && typeof file.metadata === "object" ? (file.metadata as Record<string, any>).source === "ai" : false,
        size: file.metadata && typeof file.metadata === "object" ? ((file.metadata as Record<string, any>).size as number) || 0 : 0,
      }));
    }

    console.log(`[TOOL] Found ${files.length} ${source} documents`);

    // Store in cache for future use
    const result = {
      files: files,
      count: files.length,
      source,
    };

    documentCache.set(cacheKey, result);

    // Apply search filter if needed
    if (search) {
      const filteredFiles = applyDocumentSearchFilter(files, search);
      return {
        files: filteredFiles,
        count: filteredFiles.length,
        source,
      };
    }

    return result;
  } catch (error) {
    console.error("[TOOL] Error listing documents:", error);
    return {
      error: "Failed to list documents",
      details: error instanceof Error ? error.message : String(error),
      files: [],
      count: 0,
      source,
    };
  }
}

// Define the tool using the same pattern as other tools in the project
export const listDocumentsTool = tool({
  description: "List documents that the current user has access to. Use this to find files the user can reference.",
  parameters: z.object({
    source: z.enum(["personal", "company"]).optional().describe("The source of documents to list (personal or company). Defaults to company."),
    search: z.string().optional().describe("Optional search term to filter documents by name"),
    useCache: z.boolean().optional().describe("Whether to use cached results if available. Defaults to true."),
  }),
  execute: async ({ source, search, useCache = true }) => {
    return await listDocumentsImpl({ source, search, useCache });
  },
});
