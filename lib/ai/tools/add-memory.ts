import { createMemory } from "@/lib/db/queries";
import { getUserId } from "@/lib/server/user-actions";
import { tool } from "ai";
import { z } from "zod";

export const addMemory = tool({
  description: "Store important information in long term memory.",
  parameters: z.object({
    content: z.string().describe("the information to store"),
  }),
  execute: async ({ content }) => {
    const userId = await getUserId();
    if (!userId) {
      throw new Error("User not authenticated. Cannot save memory.");
    }
    const memory = await createMemory({ userId, content });
    return { id: memory.id, message: "memory stored" };
  },
});
