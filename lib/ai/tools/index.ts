// Unified server-only tools architecture
import type { Tool } from "ai";
import { addMemory } from "@/lib/ai/tools/add-memory";
import { listDocumentsTool } from "@/lib/ai/tools/list-documents";
import { googleSheetsTool } from "./google-sheets";
import { jiraTool } from "./jira";
import { knowledgeBaseTool } from "./knowledge-base";
import { n8nWorkflowTool } from "./n8n-workflow";
import { TOOL_IDS, type ToolId, type Mode } from "@/lib/constants";
import { getToolsForWorkspaceAndMode } from "@/lib/server/constants";

// Tool info interface
export interface ToolInfo {
  id: ToolId;
  name: string;
  description: string;
  prompt: string[];
  tool: Tool;
}

// Export individual tools for backward compatibility
export const addMemoryTool = addMemory;
export { listDocumentsTool, googleSheetsTool, jiraTool, knowledgeBaseTool, n8nWorkflowTool };

// All tools configuration (unified)
export const ALL_TOOLS_INFO: Record<ToolId, ToolInfo> = {
  [TOOL_IDS.KNOWLEDGE_BASE]: {
    id: TOOL_IDS.KNOWLEDGE_BASE,
    name: "Knowledge Base",
    description: "Search internal knowledge base for answers",
    prompt: ["tool to search internal knowledge base for answering questions."],
    tool: knowledgeBaseTool,
  },
  [TOOL_IDS.N8N_WORKFLOW]: {
    id: TOOL_IDS.N8N_WORKFLOW,
    name: "n8n Workflow",
    description: "Execute automation workflows",
    prompt: ["tool to execute n8n workflows for automating tasks."],
    tool: n8nWorkflowTool,
  },
  [TOOL_IDS.MEMORY]: {
    id: TOOL_IDS.MEMORY,
    name: "Memory",
    description: "Save and recall information",
    prompt: ["tool to remember important details from conversations."],
    tool: addMemory,
  },
  [TOOL_IDS.LIST_DOCUMENTS]: {
    id: TOOL_IDS.LIST_DOCUMENTS,
    name: "List Documents",
    description: "Browse available documents",
    prompt: ["tool to list available documents and files."],
    tool: listDocumentsTool,
  },
  [TOOL_IDS.GOOGLE_SHEETS]: {
    id: TOOL_IDS.GOOGLE_SHEETS,
    name: "Google Sheets",
    description: "Read and write spreadsheet data",
    prompt: ["tool to create and manage Google Sheets for backlog items."],
    tool: googleSheetsTool,
  },
  [TOOL_IDS.JIRA]: {
    id: TOOL_IDS.JIRA,
    name: "JIRA",
    description: "Manage project tasks and issues",
    prompt: ["tool to retrieve Jira tickets assigned to a user for project tracking."],
    tool: jiraTool,
  },
  // UI-only tools (no actual AI SDK implementation)
  [TOOL_IDS.CREATE_DOCUMENT]: {
    id: TOOL_IDS.CREATE_DOCUMENT,
    name: "Create Document",
    description: "Create new documents",
    prompt: [],
    tool: {} as Tool, // Placeholder - handled in UI only
  },
  [TOOL_IDS.UPDATE_DOCUMENT]: {
    id: TOOL_IDS.UPDATE_DOCUMENT,
    name: "Update Document",
    description: "Update existing documents",
    prompt: [],
    tool: {} as Tool, // Placeholder - handled in UI only
  },
  [TOOL_IDS.REQUEST_SUGGESTIONS]: {
    id: TOOL_IDS.REQUEST_SUGGESTIONS,
    name: "Request Suggestions",
    description: "Request document suggestions",
    prompt: [],
    tool: {} as Tool, // Placeholder - handled in UI only
  },
  [TOOL_IDS.GET_WEATHER]: {
    id: TOOL_IDS.GET_WEATHER,
    name: "Get Weather",
    description: "Get weather information",
    prompt: [],
    tool: {} as Tool, // Placeholder - handled in UI only
  },
};

// Get tools for a specific workspace and context
export const getToolsForContext = (
  workspaceId: string,
  toolIds: ToolId[]
): {
  availableTools: ToolInfo[];
  toolPrompts: string[];
  tools: Tool[];
} => {
  // Filter tools based on provided tool IDs
  const availableTools = toolIds.map((id) => ALL_TOOLS_INFO[id]).filter(Boolean);

  // Collect all prompts from available tools
  const toolPrompts = availableTools.flatMap((tool) => tool.prompt);

  // Get actual AI SDK tools
  const tools = availableTools.map((toolInfo) => toolInfo.tool);

  return {
    availableTools,
    toolPrompts,
    tools,
  };
};

// Get AI SDK tools for a specific context
export const getBuiltInToolsForContext = (toolIds: ToolId[]): Tool[] => {
  return toolIds.map((id) => ALL_TOOLS_INFO[id]?.tool).filter(Boolean);
};

// Get tool prompts for context-aware AI responses
export const getToolPromptsForContext = (toolIds: ToolId[]): string[] => {
  return toolIds
    .map((id) => ALL_TOOLS_INFO[id]?.prompt)
    .filter(Boolean)
    .flat();
};

// Legacy support functions (backward compatibility)
export const getAllBuiltInTools = (): Tool[] => {
  return Object.values(ALL_TOOLS_INFO).map((toolInfo) => toolInfo.tool);
};

export const getAllToolPrompts = (): string[] => {
  return Object.values(ALL_TOOLS_INFO).flatMap((tool) => tool.prompt);
};

// Legacy functions from server-tools.ts (for backward compatibility)
export const getServerBuiltInToolsForContext = (workspaceId: string, mode: Mode): Tool[] => {
  const { builtInTools } = getToolsForWorkspaceAndMode(workspaceId, mode);
  return getBuiltInToolsForContext(builtInTools);
};

export const getServerToolPromptsForContext = (workspaceId: string, mode: Mode): string[] => {
  const { builtInTools } = getToolsForWorkspaceAndMode(workspaceId, mode);
  return getToolPromptsForContext(builtInTools);
};

export const getAllServerBuiltInTools = getAllBuiltInTools;
export const getAllServerToolPrompts = getAllToolPrompts;
