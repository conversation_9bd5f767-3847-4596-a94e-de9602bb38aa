import { tool } from "ai";
import { z } from "zod";
import { google } from "googleapis";

// Initialize Google Sheets API
let sheetsClient: any = null;

const getSheetsClient = () => {
  if (!sheetsClient) {
    // Check if we have service account credentials
    const credentials = process.env.GOOGLE_SERVICE_ACCOUNT_CREDENTIALS;
    if (!credentials) {
      throw new Error("Google Service Account credentials not found. Please set GOOGLE_SERVICE_ACCOUNT_CREDENTIALS environment variable.");
    }

    try {
      const parsedCredentials = JSON.parse(credentials);
      const auth = new google.auth.GoogleAuth({
        credentials: parsedCredentials,
        scopes: ["https://www.googleapis.com/auth/spreadsheets"],
      });

      sheetsClient = google.sheets({ version: "v4", auth });
    } catch (error) {
      throw new Error(`Failed to initialize Google Sheets client: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
  return sheetsClient;
};

export const googleSheetsTool = tool({
  description: "Create and manage Google Sheets for backlog items. Can create new spreadsheets with backlog data and proper formatting.",
  parameters: z.object({
    operation: z.enum(["create", "update", "read"]).describe("The operation to perform"),

    // Create operation parameters
    title: z.string().optional().describe("Title for the new spreadsheet"),
    backlogItems: z
      .array(
        z.object({
          title: z.string(),
          description: z.string(),
          criteria_scores: z.object({
            "Business Value": z.number(),
            "User Impact": z.number(),
            "Technical Complexity": z.number(),
            "Customer Demand": z.number(),
            "Market & Stakeholder Priority": z.number(),
            "Data Availability / Feasibility": z.number(),
            "Innovation Level": z.number(),
          }),
          final_score: z.number(),
        })
      )
      .optional()
      .describe("Array of backlog items to add to the sheet"),

    // Update/Read operation parameters
    spreadsheetId: z.string().optional().describe("Google Sheets ID for update/read operations"),
    range: z.string().optional().describe("Range to update/read (e.g., 'A1:H10')"),
  }),
  execute: async (params) => {
    console.log("GoogleSheets tool invoked with operation:", params.operation);

    try {
      const sheets = getSheetsClient();

      switch (params.operation) {
        case "create":
          return await createBacklogSheet(sheets, params);

        case "update":
          if (!params.spreadsheetId) {
            throw new Error("spreadsheetId is required for update operation");
          }
          return await updateSheet(sheets, params);

        case "read":
          if (!params.spreadsheetId) {
            throw new Error("spreadsheetId is required for read operation");
          }
          return await readSheet(sheets, params);

        default:
          throw new Error(`Unknown operation: ${params.operation}`);
      }
    } catch (error) {
      console.error("Error in googleSheets tool:", error);
      return {
        error: `Failed to ${params.operation} Google Sheet: ${error instanceof Error ? error.message : "Unknown error"}`,
        operation: params.operation,
      };
    }
  },
});

// Create backlog sheet implementation
async function createBacklogSheet(sheets: any, params: any) {
  const title = params.title || `Product Backlog - ${new Date().toISOString().split("T")[0]}`;

  // Create new spreadsheet
  const createRequest = {
    resource: {
      properties: {
        title: title,
      },
      sheets: [
        {
          properties: {
            title: "Backlog Items",
          },
        },
      ],
    },
  };

  const spreadsheet = await sheets.spreadsheets.create(createRequest);
  const spreadsheetId = spreadsheet.data.spreadsheetId;

  console.log(`Created new spreadsheet: ${title} (ID: ${spreadsheetId})`);

  // Add headers
  const headers = [
    "Title",
    "Description",
    "Business Value",
    "User Impact",
    "Technical Complexity",
    "Customer Demand",
    "Market & Stakeholder Priority",
    "Data Availability / Feasibility",
    "Innovation Level",
    "Final Score",
  ];

  const headerRequest = {
    spreadsheetId,
    range: "Backlog Items!A1:J1",
    valueInputOption: "RAW",
    resource: {
      values: [headers],
    },
  };

  await sheets.spreadsheets.values.update(headerRequest);

  // Add backlog items if provided
  if (params.backlogItems && params.backlogItems.length > 0) {
    const rows = params.backlogItems.map((item: any) => [
      item.title,
      item.description,
      item.criteria_scores["Business Value"],
      item.criteria_scores["User Impact"],
      item.criteria_scores["Technical Complexity"],
      item.criteria_scores["Customer Demand"],
      item.criteria_scores["Market & Stakeholder Priority"],
      item.criteria_scores["Data Availability / Feasibility"],
      item.criteria_scores["Innovation Level"],
      item.final_score,
    ]);

    const dataRequest = {
      spreadsheetId,
      range: `Backlog Items!A2:J${rows.length + 1}`,
      valueInputOption: "RAW",
      resource: {
        values: rows,
      },
    };

    await sheets.spreadsheets.values.update(dataRequest);
    console.log(`Added ${rows.length} backlog items to spreadsheet`);
  }

  // Format the spreadsheet
  await formatBacklogSheet(sheets, spreadsheetId);

  const url = `https://docs.google.com/spreadsheets/d/${spreadsheetId}/edit`;

  return {
    operation: "create",
    success: true,
    spreadsheetId,
    title,
    url,
    itemCount: params.backlogItems?.length || 0,
    message: `✅ Backlog spreadsheet "${title}" created successfully with ${params.backlogItems?.length || 0} items`,
  };
}

// Update sheet implementation
// eslint-disable-next-line @typescript-eslint/no-unused-vars
async function updateSheet(_sheets: any, _params: any) {
  // Implementation for updating existing sheets
  // This would be used for adding more backlog items or updating existing ones
  return {
    operation: "update",
    success: true,
    message: "Sheet update functionality would be implemented here",
  };
}

// Read sheet implementation
async function readSheet(sheets: any, params: any) {
  const range = params.range || "A:J";

  const request = {
    spreadsheetId: params.spreadsheetId,
    range,
  };

  const response = await sheets.spreadsheets.values.get(request);
  const values = response.data.values || [];

  return {
    operation: "read",
    success: true,
    data: values,
    rowCount: values.length,
  };
}

// Format backlog sheet with proper styling
async function formatBacklogSheet(sheets: any, spreadsheetId: string) {
  const requests = [
    // Format header row
    {
      repeatCell: {
        range: {
          sheetId: 0,
          startRowIndex: 0,
          endRowIndex: 1,
          startColumnIndex: 0,
          endColumnIndex: 10,
        },
        cell: {
          userEnteredFormat: {
            backgroundColor: { red: 0.2, green: 0.6, blue: 0.9 },
            textFormat: { bold: true, foregroundColor: { red: 1, green: 1, blue: 1 } },
          },
        },
        fields: "userEnteredFormat(backgroundColor,textFormat)",
      },
    },
    // Auto-resize columns
    {
      autoResizeDimensions: {
        dimensions: {
          sheetId: 0,
          dimension: "COLUMNS",
          startIndex: 0,
          endIndex: 10,
        },
      },
    },
    // Freeze header row
    {
      updateSheetProperties: {
        properties: {
          sheetId: 0,
          gridProperties: {
            frozenRowCount: 1,
          },
        },
        fields: "gridProperties.frozenRowCount",
      },
    },
  ];

  await sheets.spreadsheets.batchUpdate({
    spreadsheetId,
    resource: { requests },
  });

  console.log("Applied formatting to backlog spreadsheet");
}
