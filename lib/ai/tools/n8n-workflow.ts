import { tool } from "ai";
import { z } from "zod";

export const n8nWorkflowTool = tool({
  description: `Use this tool when user asking about setting up a new automation workflow, this is the template or running workflow that user can take to have a look or duplicate.`,
  parameters: z.object({
    query: z.string().describe("The user's query about automation workflows"),
  }),
  execute: async ({ query }) => {
    console.log("API POST /chat: n8nWorkflowKB tool invoked with query:", query);

    try {
      // Simulate a slight delay to show the loading state
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Mock data for now - would be replaced with actual n8n workflow data in production
      const mockWorkflows = [
        {
          id: "1",
          name: "Email Notification Workflow",
          description: "Sends email notifications when new data is received from a webhook",
          tags: ["email", "notification", "webhook"],
          templateUrl: "https://n8n.io/workflows/templates/email-notification",
        },
        {
          id: "2",
          name: "Data Sync Workflow",
          description: "Synchronizes data between CRM and marketing platforms",
          tags: ["sync", "crm", "marketing"],
          templateUrl: "https://n8n.io/workflows/templates/data-sync",
        },
        {
          id: "3",
          name: "Lead Generation Workflow",
          description: "Captures website form submissions and processes new leads",
          tags: ["leads", "form", "website"],
          templateUrl: "https://n8n.io/workflows/templates/lead-generation",
        },
        {
          id: "4",
          name: "Social Media Scheduling",
          description: "Schedule and post content across multiple social media platforms",
          tags: ["social", "scheduling", "marketing"],
          templateUrl: "https://n8n.io/workflows/templates/social-media-scheduler",
        },
        {
          id: "5",
          name: "File Processing Pipeline",
          description: "Automatically process uploaded files and extract relevant information",
          tags: ["files", "automation", "processing"],
          templateUrl: "https://n8n.io/workflows/templates/file-processor",
        },
      ];

      // Filter workflows based on query keywords
      const keywords = query.toLowerCase().split(" ");

      const filteredWorkflows = mockWorkflows.filter((workflow) => {
        const workflowText = `${workflow.name} ${workflow.description} ${workflow.tags.join(" ")}`.toLowerCase();
        return keywords.some((keyword) => workflowText.includes(keyword.trim()) && keyword.trim().length > 2);
      });

      console.log(`API POST /chat: n8nWorkflowKB found ${filteredWorkflows.length} matching workflows`);

      return {
        workflows: filteredWorkflows.length > 0 ? filteredWorkflows : mockWorkflows.slice(0, 3),
        message:
          filteredWorkflows.length > 0
            ? `Here are some n8n workflow templates that might help with your automation needs related to "${query}".`
            : "Here are some popular n8n workflow templates that might help with your automation needs.",
      };
    } catch (error) {
      console.error("API POST /chat: Error in n8nWorkflowKB tool:", error);
      return {
        workflows: [],
        message: "Sorry, I couldn't find any specific workflow templates at the moment. Please try a different search term.",
      };
    }
  },
});
