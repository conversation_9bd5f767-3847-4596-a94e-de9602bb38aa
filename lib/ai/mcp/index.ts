import { experimental_createMCPClient } from "ai";
import type { Tool } from "ai";
import { MCP_IDS, MCP_CONFIGS, getMcpToolsForWorkspaceAndMode, type McpId, type McpConfig } from "./config";
import { WORKSPACE_IDS, type Mode } from "@/lib/constants";
import { Experimental_StdioMCPTransport } from "ai/mcp-stdio";

// Re-export types and constants for backward compatibility
export { MCP_IDS, type McpId, type McpConfig } from "./config";

// Create MCP client with proper error handling
export async function createMcpClient(config: McpConfig): Promise<any> {
  try {
    if (config.config.transport.type === "sse") {
      const client = experimental_createMCPClient({
        name: config.name,
        transport: {
          type: "sse",
          url: config.config.transport.url!,
          headers: config.config.transport.headers,
        },
      });
      return client;
    } else if (config.config.transport.type === "stdio") {
      // For stdio, we need to use the Experimental_StdioMCPTransport class
      const transport = new Experimental_StdioMCPTransport({
        command: config.config.transport.command!,
        args: config.config.transport.args || [],
      });

      const client = experimental_createMCPClient({
        name: config.name,
        transport,
      });
      return client;
    } else {
      console.warn(`Transport type ${config.config.transport.type} not implemented yet`);
      return null;
    }
  } catch (err) {
    console.error(`Failed to create MCP client for ${config.name}:`, err);
    return null;
  }
}

// Get tools for a single MCP config
export async function getToolsForMcp(config: McpConfig): Promise<Record<string, Tool> | null> {
  try {
    const client = await createMcpClient(config);
    if (client && typeof client.tools === "function") {
      const tools = await client.tools();
      return tools;
    }
    if (client) {
      console.warn(`MCP client for ${config.name} does not have a 'tools' function.`);
    }
    return null;
  } catch (err) {
    // Do not log connection errors as errors, as MCPs can be offline
    if (!(err instanceof Error) || !err.message.includes("fetch failed")) {
      console.error(`Failed to get tools for MCP ${config.name}:`, err);
    }
    return null;
  }
}

// Main function to load MCP tools based on mode and user configuration
export async function loadMcpToolsForMode(
  mode: Mode,
  userId: string,
  workspaceId: string = WORKSPACE_IDS.DEFAULT
): Promise<{
  tools: Record<string, Tool>;
  clients: any[];
}> {
  const tools: Record<string, Tool> = {};
  const clients: any[] = [];

  try {
    // Get default MCP tools for workspace and mode using the new config system
    const defaultMcpIds = getMcpToolsForWorkspaceAndMode(workspaceId, mode);
    console.log("defaultMcpIds", defaultMcpIds);

    // Process default MCP tools
    for (const mcpId of defaultMcpIds) {
      try {
        const config = MCP_CONFIGS[mcpId];
        if (!config) {
          console.warn(`No configuration found for MCP ID: ${mcpId}`);
          continue;
        }
        const client = await createMcpClient(config);
        if (client) {
          clients.push(client);
          if (client.tools) {
            const mcpTools = await client.tools();
            Object.assign(tools, mcpTools);
          }
        }
      } catch (err) {
        console.error(`Failed to load tools for MCP ${mcpId}:`, err);
      }
    }

    console.log(`Successfully loaded ${Object.keys(tools).length} MCP tools for mode: ${mode}`);
  } catch (err) {
    console.error(`Error loading MCP tools for mode ${mode}:`, err);
  }

  return { tools, clients };
}

// Utility function to clear MCP client cache (useful for development)
export function clearMcpClientCache(): void {
  console.log("MCP client cache clearing is deprecated as cache is removed.");
}

// Health check for MCP connections
export async function checkMcpHealth(): Promise<Record<McpId, boolean>> {
  const healthStatus: Record<McpId, boolean> = {} as Record<McpId, boolean>;

  for (const mcpId of Object.values(MCP_IDS) as McpId[]) {
    let client = null;
    try {
      const config = MCP_CONFIGS[mcpId];
      if (config) {
        client = await createMcpClient(config);
        healthStatus[mcpId] = client !== null;
      } else {
        healthStatus[mcpId] = false;
      }
    } catch {
      healthStatus[mcpId] = false;
    } finally {
      if (client) {
        await client.close();
      }
    }
  }

  return healthStatus;
}
