import { MODES, type Mode } from "@/lib/constants";

// MCP identifiers
export const MCP_IDS = {
  PO: "po_mcp",
} as const;

export type McpId = (typeof MCP_IDS)[keyof typeof MCP_IDS];

// MCP configuration interface
export interface McpConfig {
  name: string;
  config: {
    transport: {
      type: "sse" | "stdio";
      url?: string;
      headers?: Record<string, string>;
      command?: string;
      args?: string[];
    };
  };
  autoEnabled: boolean;
}

// MCP configurations
export const MCP_CONFIGS: Record<McpId, McpConfig> = {
  [MCP_IDS.PO]: {
    name: "PO Assistant Tools",
    config: {
      transport: {
        type: "stdio",
        command: "npx",
        args: [
          "mcp-remote",
          "https://workflow.bravebits.co/mcp/po-tools/sse",
          "--header",
          "Authorization: Bearer bb4rever",
          "--transport",
          "sse-only",
        ],
      },
    },
    autoEnabled: true,
  },
};

// Mode-based MCP tool mappings (no dependencies on constants-server)
export const MODE_MCP_TOOLS: Record<Mode, McpId[]> = {
  [MODES.DEFAULT]: [],
  [MODES.PO]: [MCP_IDS.PO],
};

// Workspace-based MCP tool mappings
export const WORKSPACE_MCP_TOOLS: Record<string, McpId[]> = {
  default: [],
  bravebits: [],
};

// Utility function to get MCP tools for a workspace and mode
export const getMcpToolsForWorkspaceAndMode = (workspaceId: string, mode: Mode): McpId[] => {
  const workspaceMcpTools = WORKSPACE_MCP_TOOLS[workspaceId] || [];
  const modeMcpTools = MODE_MCP_TOOLS[mode] || [];

  // Combine and deduplicate
  return Array.from(new Set([...workspaceMcpTools, ...modeMcpTools]));
};
