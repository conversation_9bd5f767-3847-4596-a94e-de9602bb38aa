#!/bin/bash

echo "🚀 Setting up database prerequisites..."

# Check if we're on macOS
if [[ "$OSTYPE" == "darwin"* ]]; then
  echo "📱 Detected macOS"
  
  # Check if Homebrew is installed
  if ! command -v brew &> /dev/null; then
    echo "❌ Homebrew not found. Please install Homebrew first:"
    echo "   /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
    exit 1
  fi
  
  # Check if pgvector is installed
  if ! brew list pgvector &> /dev/null; then
    echo "📦 Installing pgvector..."
    brew install pgvector
    echo "✅ pgvector installed"
  else
    echo "✅ pgvector already installed"
  fi
  
  # Check if PostgreSQL is running
  if ! pgrep -x "postgres" > /dev/null; then
    echo "⚠️  PostgreSQL not running. Please start Postgres.app or your PostgreSQL service"
    echo "   If using Postgres.app, make sure it's running and try again"
  else
    echo "✅ PostgreSQL is running"
  fi
  
else
  echo "🐧 Non-macOS system detected"
  echo "📝 Please install pgvector manually for your system:"
  echo "   - Ubuntu/Debian: sudo apt-get install postgresql-14-pgvector"
  echo "   - CentOS/RHEL: sudo yum install pgvector"
  echo "   - Or compile from source: https://github.com/pgvector/pgvector"
fi

echo ""
echo "🎯 Next steps:"
echo "1. Make sure your .env.local has POSTGRES_URL set"
echo "2. Run: pnpm db:migrate"
echo ""
echo "✨ Database setup complete!" 