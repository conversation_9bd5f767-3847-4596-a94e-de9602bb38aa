declare module "jira.js" {
  export class Version3Client {
    constructor(options: {
      host: string;
      authentication: {
        basic: {
          email: string;
          apiToken: string;
        };
      };
      newErrorHandling?: boolean;
    });

    issueSearch: {
      searchForIssuesUsingJqlPost(params: { jql: string; fields?: string[] }): Promise<{
        issues: any[];
        [key: string]: any;
      }>;
    };

    issues: {
      getIssue(params: { issueIdOrKey: string; fields?: string[] }): Promise<any>;
    };

    issueComments: {
      getComments(params: { issueIdOrKey: string }): Promise<{
        comments: any[];
        [key: string]: any;
      }>;
    };

    projects: {
      getProject(params: { projectIdOrKey: string }): Promise<any>;
    };
  }
}
